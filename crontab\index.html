<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crontab 时间戳生成工具</title>
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Custom styles for animations if needed, otherwise Tailwind handles most */
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        /* Hide scrollbar for cleaner look if content overflows, but allow scrolling */
        body {
            overflow-y: auto;
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4 font-sans">

    <div class="bg-white p-8 rounded-2xl shadow-xl w-full max-w-2xl transform transition-all duration-300 hover:scale-105 hover:shadow-2xl">
        <h1 class="text-4xl font-extrabold text-center text-gray-800 mb-8 tracking-tight">Crontab 时间戳生成工具</h1>

        <!-- Preset Selection (Moved to the very top) -->
        <div class="mb-6 bg-purple-50 p-4 rounded-xl border border-purple-200 shadow-sm">
            <label for="preset" class="block text-purple-800 text-lg font-bold mb-2">选择常用模式</label>
            <select
                id="preset"
                class="w-full p-3 border border-purple-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 transition-all duration-200 bg-white text-gray-800"
            >
                <option value="custom">自定义</option>
                <option value="every_minute">每分钟 (* * * * *)</option>
                <option value="every_5_minutes">每5分钟 (*/5 * * * *)</option>
                <option value="every_15_minutes">每15分钟 (*/15 * * * *)</option>
                <option value="every_30_minutes">每30分钟 (*/30 * * * *)</option>
                <option value="every_hour">每小时 (0 * * * *)</option>
                <option value="daily_midnight">每天午夜 (0 0 * * *)</option>
                <option value="weekly_sunday_midnight">每周日午夜 (0 0 * * 0)</option>
                <option value="monthly_first_day_midnight">每月第一天午夜 (0 0 1 * *)</option>
            </select>
        </div>

        <!-- Generated Crontab String -->
        <div class="mb-6 bg-blue-50 p-4 rounded-xl border border-blue-200 shadow-md">
            <label for="generatedCron" class="block text-blue-800 text-lg font-bold mb-2">生成的 Crontab 表达式</label>
            <div class="relative">
                <input
                    type="text"
                    id="generatedCron"
                    class="w-full p-3 pr-12 border rounded-lg bg-white text-blue-900 font-mono text-lg focus:outline-none focus:ring-2 focus:ring-blue-600 shadow-inner"
                    value="* * * * *"
                    readonly
                />
                <button
                    id="copyButton"
                    class="absolute right-2 top-1/2 -translate-y-1/2 p-2 bg-blue-600 text-white rounded-full hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 transform hover:scale-110 shadow-lg"
                    title="复制到剪贴板"
                >
                    <!-- Lucide-React Copy icon equivalent using SVG -->
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-copy"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"/><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2v2"/></svg>
                </button>
            </div>
            <p id="copyMessage" class="text-green-600 text-sm mt-3 text-center animate-pulse hidden"></p>
        </div>

        <!-- Custom Input Fields -->
        <div id="customInputs" class="grid grid-cols-1 md:grid-cols-2 gap-7 mb-8">
            <!-- Minute Input -->
            <div class="bg-gray-50 p-4 rounded-xl border border-gray-200 shadow-sm">
                <label for="minute" class="block text-gray-700 text-sm font-semibold mb-2">分钟 (0-59)</label>
                <input
                    type="text"
                    id="minute"
                    class="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200"
                    value="*"
                    placeholder="例如: *, 0, 5-10, */5, 0,30"
                />
                <p id="minuteError" class="text-red-500 text-xs mt-1 hidden items-center"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-triangle mr-1"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/><path d="M12 9v4"/><path d="M12 17h.01"/></svg></p>
            </div>

            <!-- Hour Input -->
            <div class="bg-gray-50 p-4 rounded-xl border border-gray-200 shadow-sm">
                <label for="hour" class="block text-gray-700 text-sm font-semibold mb-2">小时 (0-23)</label>
                <input
                    type="text"
                    id="hour"
                    class="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200"
                    value="*"
                    placeholder="例如: *, 10, 8-12, */6, 9,17"
                />
                <p id="hourError" class="text-red-500 text-xs mt-1 hidden items-center"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-triangle mr-1"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/><path d="M12 9v4"/><path d="M12 17h.01"/></svg></p>
            </div>

            <!-- Day of Month Input -->
            <div class="bg-gray-50 p-4 rounded-xl border border-gray-200 shadow-sm">
                <label for="dayOfMonth" class="block text-gray-700 text-sm font-semibold mb-2">每月日 (1-31)</label>
                <input
                    type="text"
                    id="dayOfMonth"
                    class="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200"
                    value="*"
                    placeholder="例如: *, 1, 15-20, */7, 1,15"
                />
                <p id="dayOfMonthError" class="text-red-500 text-xs mt-1 hidden items-center"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-triangle mr-1"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/><path d="M12 9v4"/><path d="M12 17h.01"/></svg></p>
            </div>

            <!-- Month Input -->
            <div class="bg-gray-50 p-4 rounded-xl border border-gray-200 shadow-sm">
                <label for="month" class="block text-gray-700 text-sm font-semibold mb-2">月份 (1-12)</label>
                <input
                    type="text"
                    id="month"
                    class="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200"
                    value="*"
                    placeholder="例如: *, 6, 1-3, */2, 1,7"
                />
                <p id="monthError" class="text-red-500 text-xs mt-1 hidden items-center"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-triangle mr-1"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/><path d="M12 9v4"/><path d="M12 17h.01"/></svg></p>
            </div>

            <!-- Day of Week Input -->
            <div class="bg-gray-50 p-4 rounded-xl border border-gray-200 shadow-sm">
                <label for="dayOfWeek" class="block text-gray-700 text-sm font-semibold mb-2">每周日 (0-7, 周日为0或7)</label>
                <input
                    type="text"
                    id="dayOfWeek"
                    class="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200"
                    value="*"
                    placeholder="例如: *, 0, 1-5, */2, 0,6"
                />
                <p id="dayOfWeekError" class="text-red-500 text-xs mt-1 hidden items-center"><svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-triangle mr-1"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/><path d="M12 9v4"/><path d="M12 17h.01"/></svg></p>
            </div>
        </div>

        <!-- Next Run Time Preview (Moved up for immediate analysis with generated cron) -->
        <div class="mt-8 bg-yellow-50 p-6 rounded-2xl border border-yellow-200 shadow-inner">
            <h2 class="text-xl font-bold text-yellow-800 mb-4">下次运行时间预览 (简化版)</h2>
            <p class="text-sm text-yellow-700 mb-3">
                <strong>注意：</strong> 这是一个简化版的预览，可能无法准确计算所有复杂的 Crontab 组合（例如，日期和星期同时指定时）。对于精确的计划，请始终验证您的 Crontab 环境。
            </p>
            <ul id="nextRunTimesList" class="list-disc list-inside text-gray-700 space-y-1"></ul>
            <p id="noNextRunTimes" class="text-gray-600 hidden">无法计算下次运行时间或表达式无效。</p>
        </div>

        <!-- Crontab Examples (Moved to reference section) -->
        <div class="mt-8 bg-indigo-50 p-6 rounded-2xl border border-indigo-200 shadow-inner">
            <h2 class="text-xl font-bold text-indigo-800 mb-4">Crontab 常见示例:</h2>
            <ul class="list-disc list-inside text-gray-700 space-y-2">
                <li><span class="font-mono text-indigo-700">0 0 * * *</span> - 每天午夜运行一次。</li>
                <li><span class="font-mono text-indigo-700">0 * * * *</span> - 每小时的第0分钟运行一次。</li>
                <li><span class="font-mono text-indigo-700">* * * * *</span> - 每分钟运行一次。</li>
                <li><span class="font-mono text-indigo-700">0 0 1 * *</span> - 每月的第一天午夜运行一次。</li>
                <li><span class="font-mono text-indigo-700">0 0 * * 0</span> - 每周日午夜运行一次。</li>
                <li><span class="font-mono text-indigo-700">*/5 * * * *</span> - 每5分钟运行一次。</li>
            </ul>
        </div>

        <!-- Cross-platform Compatibility Tips (Remains at the end as a disclaimer) -->
        <div class="mt-8 bg-red-50 p-6 rounded-2xl border border-red-200 shadow-inner">
            <h2 class="text-xl font-bold text-red-800 mb-4">跨平台兼容性提示</h2>
            <ul class="list-disc list-inside text-gray-700 space-y-2">
                <li><strong>周日表示：</strong> 在某些系统（如 Linux）中，周日可能表示为 `0` 或 `7`。本工具支持 `0` 和 `7`。</li>
                <li><strong>年字段：</strong> 标准 Crontab 只有 5 个字段（分钟、小时、每月日、月份、每周日），没有年字段。一些自定义的 Crontab 实现可能包含第 6 个字段用于年份。</li>
                <li><strong>环境变量：</strong> Crontab 任务通常在最小的环境变量下运行。如果您的脚本依赖特定路径或环境变量，请在脚本内部明确设置它们。</li>
                <li><strong>特殊字符：：</strong> `%, #` 等字符在 Crontab 表达式中可能有特殊含义，需要进行转义。</li>
                <li><strong>日志：</strong> 建议将 Crontab 任务的输出重定向到日志文件，以便于调试。</li>
            </ul>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const presets = {
                'custom': { name: '自定义', cron: '' },
                'every_minute': { name: '每分钟 (* * * * *)', cron: '* * * * *' },
                'every_5_minutes': { name: '每5分钟 (*/5 * * * *)', cron: '*/5 * * * *' },
                'every_15_minutes': { name: '每15分钟 (*/15 * * * *)', cron: '*/15 * * * *' },
                'every_30_minutes': { name: '每30分钟 (*/30 * * * *)', cron: '*/30 * * * *' },
                'every_hour': { name: '每小时 (0 * * * *)', cron: '0 * * * *' },
                'daily_midnight': { name: '每天午夜 (0 0 * * *)', cron: '0 0 * * *' },
                'weekly_sunday_midnight': { name: '每周日午夜 (0 0 * * 0)', cron: '0 0 * * 0' },
                'monthly_first_day_midnight': { name: '每月第一天午夜 (0 0 1 * *)', cron: '0 0 1 * *' },
            };

            const regexMap = {
                minute: /^((\*|[0-5]?[0-9](-[0-5]?[0-9])?)(,[0-5]?[0-9](-[0-5]?[0-9])?)*|\*\/[0-5]?[0-9])$/,
                hour: /^((\*|[0-1]?[0-9](-[0-1]?[0-9])?|2[0-3](-2[0-3])?)(,[0-1]?[0-9](-[0-1]?[0-9])?|2[0-3](-2[0-3])?)*|\*\/[0-1]?[0-9]|2[0-3])$/,
                dayOfMonth: /^((\*|[1-2]?[0-9](-[1-2]?[0-9])?|3[0-1](-3[0-1])?)(,[1-2]?[0-9](-[1-2]?[0-9])?|3[0-1](-3[0-1])?)*|\*\/[1-2]?[0-9]|3[0-1])$/,
                month: /^((\*|[1-9](-[1-9])?|1[0-2](-1[0-2])?)(,[1-9](-[1-9])?|1[0-2](-1[0-2])?)*|\*\/[1-9]|1[0-2])$/,
                dayOfWeek: /^((\*|[0-7](-[0-7])?)(,[0-7](-[0-7])?)*|\*\/[0-7])$/,
            };

            const errorMessages = {
                minute: '无效分钟值 (0-59, 范围, 列表, 或 */步长)',
                hour: '无效小时值 (0-23, 范围, 列表, 或 */步长)',
                dayOfMonth: '无效每月日值 (1-31, 范围, 列表, 或 */步长)',
                month: '无效月份值 (1-12, 范围, 列表, 或 */步长)',
                dayOfWeek: '无效每周日值 (0-7, 周日为0或7, 范围, 列表, 或 */步长)',
                generic: '无效的 Crontab 表达式格式。请检查字段数量（应为5个）。'
            };

            const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六', '周日']; // 0 and 7 are Sunday
            const monthNames = ['无效', '一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];

            // Get references to DOM elements
            const generatedCronInput = document.getElementById('generatedCron');
            const copyButton = document.getElementById('copyButton');
            const copyMessage = document.getElementById('copyMessage');
            const presetSelect = document.getElementById('preset');
            const customInputsDiv = document.getElementById('customInputs');

            const minuteInput = document.getElementById('minute');
            const hourInput = document.getElementById('hour');
            const dayOfMonthInput = document.getElementById('dayOfMonth');
            const monthInput = document.getElementById('month');
            const dayOfWeekInput = document.getElementById('dayOfWeek');

            const minuteErrorP = document.getElementById('minuteError');
            const hourErrorP = document.getElementById('hourError');
            const dayOfMonthErrorP = document.getElementById('dayOfMonthError');
            const monthErrorP = document.getElementById('monthError');
            const dayOfWeekErrorP = document.getElementById('dayOfWeekError');

            // Removed parser related elements
            const nextRunTimesList = document.getElementById('nextRunTimesList');
            const noNextRunTimesP = document.getElementById('noNextRunTimes');

            // --- Utility Functions ---
            function showElement(element) { element.classList.remove('hidden'); }
            function hideElement(element) { element.classList.add('hidden'); }
            function addClass(element, className) { element.classList.add(className); }
            function removeClass(element, className) { element.classList.remove(className); }
            function setInputError(inputElement, errorPElement, message) {
                if (message) {
                    errorPElement.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-alert-triangle mr-1"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/><path d="M12 9v4"/><path d="M12 17h.01"/></svg>${message}`;
                    showElement(errorPElement);
                    addClass(inputElement.parentNode, 'border-red-500');
                    removeClass(inputElement.parentNode, 'border-gray-200');
                } else {
                    hideElement(errorPElement);
                    removeClass(inputElement.parentNode, 'border-red-500');
                    addClass(inputElement.parentNode, 'border-gray-200');
                }
            }

            // --- Input Validation and State Update ---
            const handleCustomInputChange = (inputElement, errorPElement, fieldName) => {
                const value = inputElement.value.trim();
                const regex = regexMap[fieldName];
                const errorMessage = errorMessages[fieldName];
                let isValid = true;

                if (value === '' || value === '*') {
                    setInputError(inputElement, errorPElement, '');
                } else if (!regex.test(value)) {
                    setInputError(inputElement, errorPElement, errorMessage);
                    isValid = false;
                } else {
                    setInputError(inputElement, errorPElement, '');
                }
                updateGeneratedCron();
                return isValid;
            };

            const updateGeneratedCron = () => {
                const m = minuteInput.value.trim() || '*';
                const h = hourInput.value.trim() || '*';
                const dom = dayOfMonthInput.value.trim() || '*';
                const mon = monthInput.value.trim() || '*';
                const dow = dayOfWeekInput.value.trim() || '*';

                const allValid =
                    regexMap.minute.test(m) &&
                    regexMap.hour.test(h) &&
                    regexMap.dayOfMonth.test(dom) &&
                    regexMap.month.test(mon) &&
                    regexMap.dayOfWeek.test(dow);

                if (allValid) {
                    const newCron = `${m} ${h} ${dom} ${mon} ${dow}`;
                    generatedCronInput.value = newCron;
                    removeClass(generatedCronInput, 'border-red-500');
                    removeClass(generatedCronInput, 'text-red-700');
                    addClass(generatedCronInput, 'border-blue-300');
                    calculateNextRunTimes(newCron); // Calculate next run times directly
                } else {
                    generatedCronInput.value = '无效表达式';
                    addClass(generatedCronInput, 'border-red-500');
                    addClass(generatedCronInput, 'text-red-700');
                    removeClass(generatedCronInput, 'border-blue-300');
                    calculateNextRunTimes(''); // Clear next run times for invalid expression
                }
            };

            // --- Basic Next Run Time Calculator (Simplified) ---
            const calculateNextRunTimes = (cronString) => {
                const parts = cronString.split(/\s+/);
                if (parts.length !== 5 || cronString === '无效表达式') { // Also handle "无效表达式" from generator
                    nextRunTimesList.innerHTML = '';
                    showElement(noNextRunTimesP);
                    return;
                }

                const [m, h, dom, mon, dow] = parts;
                const now = new Date();
                const results = [];
                let current = new Date(now.getTime() + 60 * 1000); // Start 1 minute from now

                const checkPart = (value, cronPart, min, max) => {
                    if (cronPart === '*') return true;
                    if (cronPart.startsWith('*/')) {
                        const step = parseInt(cronPart.substring(2));
                        return !isNaN(step) && step > 0 && value % step === 0;
                    }
                    if (cronPart.includes(',')) {
                        return cronPart.split(',').map(Number).includes(value);
                    }
                    if (cronPart.includes('-')) {
                        const [start, end] = cronPart.split('-').map(Number);
                        return !isNaN(start) && !isNaN(end) && value >= start && value <= end;
                    }
                    const num = parseInt(cronPart);
                    return !isNaN(num) && value === num;
                };

                let iterationCount = 0;
                const maxIterations = 5000;

                while (results.length < 5 && iterationCount < maxIterations) {
                    iterationCount++;

                    let minuteMatch = checkPart(current.getMinutes(), m, 0, 59);
                    let hourMatch = checkPart(current.getHours(), h, 0, 23);
                    let dayOfMonthMatch = checkPart(current.getDate(), dom, 1, 31);
                    let monthMatch = checkPart(current.getMonth() + 1, mon, 1, 12);
                    let dayOfWeekMatch = checkPart(current.getDay(), dow, 0, 7) || (current.getDay() === 0 && checkPart(7, dow, 0, 7));

                    if (minuteMatch && hourMatch && dayOfMonthMatch && monthMatch && dayOfWeekMatch) {
                        results.push(new Date(current));
                        current.setMinutes(current.getMinutes() + 1);
                        current.setSeconds(0);
                        current.setMilliseconds(0);
                        continue;
                    }

                    if (!minuteMatch) {
                        current.setMinutes(current.getMinutes() + 1);
                    } else if (!hourMatch) {
                        current.setHours(current.getHours() + 1);
                        current.setMinutes(0);
                    } else if (!dayOfMonthMatch) {
                        current.setDate(current.getDate() + 1);
                        current.setHours(0);
                        current.setMinutes(0);
                    } else if (!monthMatch) {
                        current.setMonth(current.getMonth() + 1);
                        current.setDate(1);
                        current.setHours(0);
                        current.setMinutes(0);
                    } else if (!dayOfWeekMatch) {
                        current.setDate(current.getDate() + 1);
                        current.setHours(0);
                        current.setMinutes(0);
                    }
                    current.setSeconds(0);
                    current.setMilliseconds(0);
                }

                nextRunTimesList.innerHTML = '';
                if (results.length > 0) {
                    hideElement(noNextRunTimesP);
                    results.forEach(date => {
                        const li = document.createElement('li');
                        li.textContent = date.toLocaleString('zh-CN');
                        nextRunTimesList.appendChild(li);
                    });
                } else {
                    showElement(noNextRunTimesP);
                }
            };

            // --- Event Listeners ---
            copyButton.addEventListener('click', () => {
                if (generatedCronInput.value === '无效表达式') {
                    copyMessage.textContent = '无法复制无效表达式!';
                    addClass(copyMessage, 'text-red-600');
                    removeClass(copyMessage, 'text-green-600');
                } else {
                    const textArea = document.createElement('textarea');
                    textArea.value = generatedCronInput.value;
                    document.body.appendChild(textArea);
                    textArea.select();
                    try {
                        document.execCommand('copy');
                        copyMessage.textContent = '已复制!';
                        addClass(copyMessage, 'text-green-600');
                        removeClass(copyMessage, 'text-red-600');
                    } catch (err) {
                        copyMessage.textContent = '复制失败!';
                        addClass(copyMessage, 'text-red-600');
                        removeClass(copyMessage, 'text-green-600');
                    }
                    document.body.removeChild(textArea);
                }
                showElement(copyMessage);
                setTimeout(() => hideElement(copyMessage), 2000);
            });

            presetSelect.addEventListener('change', (e) => {
                const selected = e.target.value;
                if (selected === 'custom') {
                    showElement(customInputsDiv);
                    // Reset custom inputs to default wildcards and clear errors
                    minuteInput.value = '*';
                    hourInput.value = '*';
                    dayOfMonthInput.value = '*';
                    monthInput.value = '*';
                    dayOfWeekInput.value = '*';
                    // Clear all custom input errors
                    setInputError(minuteInput, minuteErrorP, '');
                    setInputError(hourInput, hourErrorP, '');
                    setInputError(dayOfMonthInput, dayOfMonthErrorP, '');
                    setInputError(monthInput, monthErrorP, '');
                    setInputError(dayOfWeekInput, dayOfWeekErrorP, '');
                    updateGeneratedCron(); // Update generated cron based on reset custom values
                } else {
                    hideElement(customInputsDiv);
                    const presetCron = presets[selected].cron;
                    generatedCronInput.value = presetCron;
                    removeClass(generatedCronInput, 'border-red-500');
                    removeClass(generatedCronInput, 'text-red-700');
                    addClass(generatedCronInput, 'border-blue-300');
                    calculateNextRunTimes(presetCron); // Calculate next run times for preset
                }
            });

            // Add event listeners for custom input fields
            minuteInput.addEventListener('input', () => handleCustomInputChange(minuteInput, minuteErrorP, 'minute'));
            hourInput.addEventListener('input', () => handleCustomInputChange(hourInput, hourErrorP, 'hour'));
            dayOfMonthInput.addEventListener('input', () => handleCustomInputChange(dayOfMonthInput, dayOfMonthErrorP, 'dayOfMonth'));
            monthInput.addEventListener('input', () => handleCustomInputChange(monthInput, monthErrorP, 'month'));
            dayOfWeekInput.addEventListener('input', () => handleCustomInputChange(dayOfWeekInput, dayOfWeekErrorP, 'dayOfWeek'));

            // Initial setup
            if (presetSelect.value === 'custom') {
                showElement(customInputsDiv);
            } else {
                hideElement(customInputsDiv);
            }
            updateGeneratedCron(); // Initialize generated cron and next run times
        });
    </script>
    <script type="text/javascript">
    (function(c,l,a,r,i,t,y){
        c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
        t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
        y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
    })(window, document, "clarity", "script", "jtxjtkxsi8");
</script>
</body>
</html>
