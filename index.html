<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的工具集 - 6ird.com</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --text-primary: #ffffff;
            --text-secondary: #e2e8f0;
            --text-light: #a0aec0;
            --bg-primary: rgba(255, 255, 255, 0.1);
            --bg-secondary: rgba(255, 255, 255, 0.05);
            --bg-glass: rgba(255, 255, 255, 0.15);
            --border-color: rgba(255, 255, 255, 0.2);
            --shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            --shadow-lg: 0 20px 40px rgba(0, 0, 0, 0.4);
            --shadow-xl: 0 25px 50px rgba(0, 0, 0, 0.5);
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-bg: linear-gradient(135deg, #0f0c29 0%, #302b63 50%, #24243e 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: linear-gradient(135deg, #0f0c29 0%, #302b63 50%, #24243e 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        /* 动态星空背景 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image:
                radial-gradient(2px 2px at 20px 30px, #eee, transparent),
                radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
                radial-gradient(1px 1px at 90px 40px, #fff, transparent),
                radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
                radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
            background-repeat: repeat;
            background-size: 200px 100px;
            z-index: -2;
            animation: twinkle 4s ease-in-out infinite alternate;
        }

        /* 流动的光效 */
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(ellipse at top, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
                radial-gradient(ellipse at bottom, rgba(240, 147, 251, 0.1) 0%, transparent 50%),
                linear-gradient(45deg, rgba(255, 255, 255, 0.02) 0%, transparent 100%);
            z-index: -1;
            animation: aurora 15s ease-in-out infinite;
        }

        @keyframes twinkle {
            0% { opacity: 0.3; }
            100% { opacity: 1; }
        }

        @keyframes aurora {
            0%, 100% {
                transform: translateX(0) translateY(0) rotate(0deg);
                opacity: 0.8;
            }
            33% {
                transform: translateX(30px) translateY(-30px) rotate(1deg);
                opacity: 0.6;
            }
            66% {
                transform: translateX(-20px) translateY(20px) rotate(-1deg);
                opacity: 0.9;
            }
        }

        /* 导航栏样式 */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(15, 12, 41, 0.9);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .navbar.scrolled {
            background: rgba(15, 12, 41, 0.95);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 70px;
        }

        .nav-logo {
            display: flex;
            align-items: center;
            text-decoration: none;
            color: var(--text-primary);
            font-weight: 700;
            font-size: 1.5rem;
        }

        .nav-logo-icon {
            width: 40px;
            height: 40px;
            background: var(--gradient-primary);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.75rem;
            font-size: 1.2rem;
            color: white;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            gap: 2rem;
        }

        .nav-item {
            position: relative;
        }

        .nav-link {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--gradient-primary);
            opacity: 0.1;
            transition: left 0.3s ease;
            z-index: -1;
        }

        .nav-link:hover {
            color: var(--text-primary);
            transform: translateY(-2px);
        }

        .nav-link:hover::before {
            left: 0;
        }

        .nav-link.active {
            color: var(--primary-color);
            background: rgba(102, 126, 234, 0.1);
        }

        .nav-link.active::before {
            left: 0;
            opacity: 0.2;
        }

        .nav-toggle {
            display: none;
            flex-direction: column;
            cursor: pointer;
            padding: 0.5rem;
        }

        .nav-toggle span {
            width: 25px;
            height: 3px;
            background: var(--text-primary);
            margin: 3px 0;
            transition: 0.3s;
            border-radius: 2px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 6rem 1rem 3rem;
            position: relative;
        }

        .header {
            text-align: center;
            margin-bottom: 6rem;
            position: relative;
            min-height: 80vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            width: 100%;
        }

        .avatar-container {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 3rem;
        }

        .avatar {
            width: 160px;
            height: 160px;
            border-radius: 50%;
            background: var(--gradient-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            box-shadow:
                0 25px 50px rgba(102, 126, 234, 0.4),
                0 0 0 4px rgba(255, 255, 255, 0.1),
                inset 0 0 0 1px rgba(255, 255, 255, 0.2);
            transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            overflow: hidden;
        }

        .avatar-text {
            font-size: 4rem;
            color: white;
            font-weight: 700;
            z-index: 2;
            position: relative;
        }

        .avatar-ring {
            position: absolute;
            width: 180px;
            height: 180px;
            border: 2px solid rgba(102, 126, 234, 0.3);
            border-radius: 50%;
            border-top-color: rgba(102, 126, 234, 0.8);
            animation: rotate 3s linear infinite;
        }

        .avatar-ring-2 {
            position: absolute;
            width: 200px;
            height: 200px;
            border: 1px solid rgba(240, 147, 251, 0.2);
            border-radius: 50%;
            border-bottom-color: rgba(240, 147, 251, 0.6);
            animation: rotate 4s linear infinite reverse;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .avatar::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.15), transparent);
            transform: rotate(45deg);
            transition: transform 0.8s ease;
        }

        .avatar:hover {
            transform: scale(1.1);
            box-shadow:
                0 35px 70px rgba(102, 126, 234, 0.6),
                0 0 0 8px rgba(255, 255, 255, 0.15),
                inset 0 0 0 1px rgba(255, 255, 255, 0.3);
        }

        .avatar:hover::before {
            transform: rotate(45deg) translate(50%, 50%);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 1rem;
            padding: 0.5rem 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .status-text {
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .name {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            display: inline-block;
            /* 添加回退颜色以防渐变文字不支持 */
            color: var(--primary-color);
        }

        /* 确保渐变文字在所有浏览器中正常显示 */
        @supports not (-webkit-background-clip: text) {
            .name {
                background: none;
                -webkit-text-fill-color: initial;
                color: var(--primary-color);
            }
        }

        .name::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: var(--gradient-secondary);
            border-radius: 2px;
            opacity: 0.8;
        }

        .bio {
            color: var(--text-secondary);
            font-size: 1.2rem;
            margin-bottom: 2rem;
            line-height: 1.8;
            max-width: 650px;
            margin-left: auto;
            margin-right: auto;
            font-weight: 400;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        /* 新的Hero样式 */
        .hero-text {
            margin-bottom: 3rem;
        }

        .name-main {
            display: block;
            font-size: 3rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
            line-height: 1.2;
        }

        .name-subtitle {
            display: block;
            font-size: 1.2rem;
            font-weight: 400;
            color: var(--text-light);
            opacity: 0.8;
            letter-spacing: 2px;
            text-transform: uppercase;
        }

        .bio-line {
            display: block;
            color: var(--text-secondary);
            font-size: 1.1rem;
            line-height: 1.8;
            margin-bottom: 0.5rem;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.8s ease-out forwards;
        }

        .bio-line:nth-child(1) { animation-delay: 0.5s; }
        .bio-line:nth-child(2) { animation-delay: 0.7s; }
        .bio-line:nth-child(3) { animation-delay: 0.9s; }

        .hero-stats {
            display: flex;
            justify-content: center;
            gap: 3rem;
            margin-bottom: 3rem;
            flex-wrap: wrap;
        }

        .stat-item {
            text-align: center;
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-width: 120px;
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
        }

        .stat-number {
            display: block;
            font-size: 2rem;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            display: block;
            font-size: 0.875rem;
            color: var(--text-light);
            font-weight: 500;
        }

        .hero-actions {
            display: flex;
            justify-content: center;
            gap: 1.5rem;
            flex-wrap: wrap;
        }

        .btn-primary, .btn-secondary {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 32px rgba(102, 126, 234, 0.6);
            color: white;
            text-decoration: none;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-primary);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            color: var(--text-primary);
            text-decoration: none;
        }

        /* 装饰性元素 */
        .hero-decoration {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            z-index: 1;
        }

        .decoration-circle {
            position: absolute;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        .decoration-1 {
            width: 200px;
            height: 200px;
            top: 10%;
            right: 10%;
            animation-delay: 0s;
        }

        .decoration-2 {
            width: 150px;
            height: 150px;
            bottom: 20%;
            left: 15%;
            animation-delay: 2s;
        }

        .decoration-3 {
            width: 100px;
            height: 100px;
            top: 60%;
            right: 20%;
            animation-delay: 4s;
        }

        .section {
            margin-bottom: 3rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--text-primary);
            position: relative;
            padding-bottom: 0.5rem;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
        }

        .projects-grid {
            display: grid;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .project-card {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 2rem;
            box-shadow:
                var(--shadow-lg),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            text-decoration: none;
            color: inherit;
            display: block;
            position: relative;
            overflow: hidden;
        }

        .project-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s ease;
        }

        .project-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
        }

        .project-card:hover::before {
            left: 100%;
        }

        .project-card:hover::after {
            opacity: 0.05;
        }

        .project-card:hover {
            box-shadow:
                var(--shadow-xl),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
            transform: translateY(-8px) scale(1.02);
            text-decoration: none;
            color: inherit;
            border-color: rgba(255, 255, 255, 0.4);
        }

        .project-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .project-icon {
            width: 64px;
            height: 64px;
            border-radius: 16px;
            margin-right: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            font-weight: 600;
            box-shadow:
                0 8px 16px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }

        .project-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.2), transparent);
            transform: rotate(45deg) translate(-100%, -100%);
            transition: transform 0.6s ease;
        }

        .project-card:hover .project-icon {
            transform: scale(1.15) rotate(-5deg);
            box-shadow:
                0 12px 24px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .project-card:hover .project-icon::before {
            transform: rotate(45deg) translate(50%, 50%);
        }

        .project-title {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
            transition: color 0.3s ease;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .project-card:hover .project-title {
            color: var(--primary-color);
        }

        .project-url {
            font-size: 0.9rem;
            color: var(--text-secondary);
            font-weight: 500;
            opacity: 0.9;
        }

        .project-description {
            color: var(--text-primary);
            line-height: 1.7;
            font-size: 1rem;
            margin-top: 1rem;
            font-weight: 400;
            opacity: 0.8;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .contact-item {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow:
                var(--shadow),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-decoration: none;
            color: inherit;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }

        .contact-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
        }

        .contact-item:hover::before {
            opacity: 0.05;
        }

        .contact-item:hover {
            box-shadow:
                var(--shadow-lg),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            transform: translateY(-4px) scale(1.02);
            text-decoration: none;
            color: inherit;
            border-color: rgba(255, 255, 255, 0.3);
        }

        .contact-title {
            font-weight: 700;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
            color: var(--text-primary);
        }

        .contact-subtitle {
            font-size: 0.9rem;
            color: var(--text-secondary);
            line-height: 1.5;
        }

        .footer {
            text-align: center;
            padding: 3rem 0 2rem;
            margin-top: 4rem;
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-radius: 24px 24px 0 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-bottom: none;
            color: var(--text-secondary);
            font-size: 0.9rem;
            position: relative;
            overflow: hidden;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--gradient-primary);
            opacity: 0.6;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2.5rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .footer-links a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: all 0.3s ease;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 500;
            position: relative;
        }

        .footer-links a::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: var(--gradient-primary);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .footer-links a:hover {
            color: var(--primary-color);
            transform: translateY(-2px);
        }

        .footer-links a:hover::before {
            width: 80%;
        }

        .footer p {
            margin: 0;
            opacity: 0.8;
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(40px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .header {
            animation: fadeInScale 1s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .section {
            animation: fadeInUp 0.8s ease-out;
            animation-fill-mode: both;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .project-card {
            animation: fadeInUp 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            animation-fill-mode: both;
        }

        .project-card:nth-child(1) { animation-delay: 0.3s; }
        .project-card:nth-child(2) { animation-delay: 0.4s; }
        .project-card:nth-child(3) { animation-delay: 0.5s; }
        .project-card:nth-child(4) { animation-delay: 0.6s; }
        .project-card:nth-child(5) { animation-delay: 0.7s; }

        .contact-item {
            animation: fadeInUp 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            animation-fill-mode: both;
        }

        .contact-item:nth-child(1) { animation-delay: 0.7s; }
        .contact-item:nth-child(2) { animation-delay: 0.8s; }
        .contact-item:nth-child(3) { animation-delay: 0.9s; }
        .contact-item:nth-child(4) { animation-delay: 1.0s; }

        /* 浮动装饰元素 */
        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .shape {
            position: absolute;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, rgba(102, 126, 234, 0.1) 50%, transparent 100%);
            animation: float 8s ease-in-out infinite;
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
        }

        .shape:nth-child(1) {
            width: 120px;
            height: 120px;
            top: 15%;
            left: 10%;
            animation-delay: 0s;
            background: radial-gradient(circle, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.1) 50%, transparent 100%);
        }

        .shape:nth-child(2) {
            width: 80px;
            height: 80px;
            top: 25%;
            right: 15%;
            animation-delay: 3s;
            background: radial-gradient(circle, rgba(240, 147, 251, 0.2) 0%, rgba(245, 87, 108, 0.1) 50%, transparent 100%);
        }

        .shape:nth-child(3) {
            width: 150px;
            height: 150px;
            bottom: 25%;
            left: 8%;
            animation-delay: 6s;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.15) 0%, rgba(102, 126, 234, 0.08) 50%, transparent 100%);
        }

        .shape:nth-child(4) {
            width: 60px;
            height: 60px;
            bottom: 15%;
            right: 25%;
            animation-delay: 2s;
            background: radial-gradient(circle, rgba(118, 75, 162, 0.2) 0%, rgba(240, 147, 251, 0.1) 50%, transparent 100%);
        }

        /* 移动端导航 */
        @media (max-width: 768px) {
            .nav-menu {
                position: fixed;
                top: 70px;
                left: -100%;
                width: 100%;
                height: calc(100vh - 70px);
                background: rgba(15, 12, 41, 0.95);
                backdrop-filter: blur(20px);
                flex-direction: column;
                justify-content: flex-start;
                align-items: center;
                padding-top: 2rem;
                gap: 1rem;
                transition: left 0.3s ease;
            }

            .nav-menu.active {
                left: 0;
            }

            .nav-toggle {
                display: flex;
            }

            .nav-toggle.active span:nth-child(1) {
                transform: rotate(-45deg) translate(-5px, 6px);
            }

            .nav-toggle.active span:nth-child(2) {
                opacity: 0;
            }

            .nav-toggle.active span:nth-child(3) {
                transform: rotate(45deg) translate(-5px, -6px);
            }

            .nav-link {
                font-size: 1.2rem;
                padding: 1rem 2rem;
                width: 200px;
                text-align: center;
            }
        }

        @media (max-width: 640px) {
            .container {
                padding: 5rem 1rem 1rem;
            }

            .avatar {
                width: 100px;
                height: 100px;
                font-size: 2.5rem;
            }

            .name {
                font-size: 1.75rem;
            }

            .contact-grid {
                grid-template-columns: 1fr;
            }

            .footer-links {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-container {
                padding: 0 1rem;
            }

            .nav-logo {
                font-size: 1.3rem;
            }

            .nav-logo-icon {
                width: 35px;
                height: 35px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <a href="#" class="nav-logo">
                <div class="nav-logo-icon">🛠️</div>
                我的工具集
            </a>
            <ul class="nav-menu" id="nav-menu">
                <li class="nav-item">
                    <a href="#home" class="nav-link">首页</a>
                </li>
                <li class="nav-item">
                    <a href="#projects" class="nav-link">作品</a>
                </li>
                <li class="nav-item">
                    <a href="#skills" class="nav-link">技术栈</a>
                </li>
                <li class="nav-item">
                    <a href="#contact" class="nav-link">联系</a>
                </li>
            </ul>
            <div class="nav-toggle" id="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 浮动装饰元素 -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="container">
        <header class="header" id="home">
            <div class="hero-content">
                <div class="avatar-container">
                    <div class="avatar">
                        <span class="avatar-text">6ird</span>
                        <div class="avatar-ring"></div>
                        <div class="avatar-ring-2"></div>
                    </div>
                </div>

                <div class="hero-text">
                    <h1 class="name">
                        <span class="name-main">我的工具集</span>
                        <span class="name-subtitle">Developer Tools Collection</span>
                    </h1>

                    <p class="bio">
                        <span class="bio-line">致力于创造简洁、高效、易用的开发工具</span>
                        <span class="bio-line">让复杂的技术变得简单易懂</span>
                    </p>

                </div>
            </div>

            <!-- 装饰性元素 -->
            <div class="hero-decoration">
                <div class="decoration-circle decoration-1"></div>
                <div class="decoration-circle decoration-2"></div>
                <div class="decoration-circle decoration-3"></div>
            </div>
        </header>

        <section class="section" id="projects">
            <h2 class="section-title">我的作品</h2>
            <div class="projects-grid">
                <a href="./crontab/" class="project-card">
                    <div class="project-header">
                        <div class="project-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">⏰</div>
                        <div>
                            <div class="project-title">Crontab 时间戳生成工具</div>
                            <div class="project-url">https://www.6ird.com/tools/crontab</div>
                        </div>
                    </div>
                    <div class="project-description">
                        智能 Crontab 表达式生成器，支持常用模式选择、实时预览和下次运行时间计算。让复杂的定时任务配置变得简单直观，提升运维效率。
                    </div>
                </a>

                <a href="https://www.6ird.com/tools/ipcheck" class="project-card">
                    <div class="project-header">
                        <div class="project-icon" style="background: linear-gradient(135deg, #06d6a0 0%, #118ab2 100%);">🌐</div>
                        <div>
                            <div class="project-title">IP 查询工具</div>
                            <div class="project-url">6ird.com/tools/ipcheck</div>
                        </div>
                    </div>
                    <div class="project-description">
                        全能网络诊断工具集，集成IP地理位置查询、批量分析、网速测试和浏览器指纹检测。为网络管理员和开发者提供专业的一站式解决方案。
                    </div>
                </a>

                <a href="https://www.6ird.com/tools/suan" class="project-card">
                    <div class="project-header">
                        <div class="project-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">🔮</div>
                        <div>
                            <div class="project-title">AI八字分析工具</div>
                            <div class="project-url">6ird.com/tools/suan</div>
                        </div>
                    </div>
                    <div class="project-description">
                        融合传统命理学与前沿AI技术的智能分析平台，基于深度学习模型提供精准的八字解读和个性化人生指导，传承古典智慧。
                    </div>
                </a>

                <a href="https://www.6ird.com/tools/subnet" class="project-card">
                    <div class="project-header">
                        <div class="project-icon" style="background: linear-gradient(135deg, #ffd60a 0%, #ff8500 100%);">🔧</div>
                        <div>
                            <div class="project-title">子网计算工具</div>
                            <div class="project-url">6ird.com/tools/subnet</div>
                        </div>
                    </div>
                    <div class="project-description">
                        专业级IPv4/IPv6子网计算器，支持VLSM子网拆分、CIDR汇总合并等高级功能。为网络工程师和系统管理员提供精确的网络规划工具。
                    </div>
                </a>

                <a href="https://www.6ird.com/tools/cfip/" class="project-card" target="_blank">
                    <div class="project-header">
                        <div class="project-icon" style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);">☁️</div>
                        <div>
                            <div class="project-title">Cloudflare优选IP工具</div>
                            <div class="project-url">6ird.com/tools/cfip</div>
                        </div>
                    </div>
                    <div class="project-description">
                        Cloudflare全能优选工具，从全球网络中智能筛选最快的节点IP。支持IPv4/IPv6双栈测试，提供延迟和速度双重优化，助力网络加速。
                    </div>
                </a>
            </div>
        </section>

        <section class="section" id="skills">
            <h2 class="section-title">技术栈</h2>
            <div class="contact-grid">
                <div class="contact-item">
                    <div class="contact-title">JavaScript</div>
                    <div class="contact-subtitle">前端开发的核心语言，构建交互式用户界面</div>
                </div>
                <div class="contact-item">
                    <div class="contact-title">PHP</div>
                    <div class="contact-subtitle">服务端开发，处理业务逻辑和数据交互</div>
                </div>
                <div class="contact-item">
                    <div class="contact-title">HTML/CSS</div>
                    <div class="contact-subtitle">网页结构与样式设计，追求简洁美观</div>
                </div>
                <div class="contact-item">
                    <div class="contact-title">AI集成</div>
                    <div class="contact-subtitle">将人工智能技术融入实用工具开发</div>
                </div>
            </div>
        </section>

        <section class="section" id="contact">
            <h2 class="section-title">联系我</h2>
            <div class="contact-grid">
                <a href="mailto:<EMAIL>" class="contact-item">
                    <div class="contact-title">邮箱</div>
                    <div class="contact-subtitle"><EMAIL></div>
                </a>
                <a href="https://github.com" class="contact-item">
                    <div class="contact-title">GitHub</div>
                    <div class="contact-subtitle">@developer</div>
                </a>
            </div>
        </section>

        <footer class="footer">
            <div class="footer-links">
                <a href="./crontab/">Crontab工具</a>
                <a href="./ipcheck/">IP查询</a>
                <a href="./suan/">八字分析</a>
                <a href="./subnet/">子网计算</a>
                <a href="https://www.6ird.com/tools/cfip/" target="_blank">CF优选IP</a>
            </div>
            <p>© 2025 开发者工具集</p>
        </footer>
    </div>

    <script>
        // 导航栏功能
        document.addEventListener('DOMContentLoaded', function() {
            const navbar = document.getElementById('navbar');
            const navToggle = document.getElementById('nav-toggle');
            const navMenu = document.getElementById('nav-menu');
            const navLinks = document.querySelectorAll('.nav-link');

            // 移动端菜单切换
            navToggle.addEventListener('click', function() {
                navMenu.classList.toggle('active');
                navToggle.classList.toggle('active');
            });

            // 点击导航链接关闭移动端菜单
            navLinks.forEach(link => {
                link.addEventListener('click', function() {
                    navMenu.classList.remove('active');
                    navToggle.classList.remove('active');
                });
            });

            // 滚动时改变导航栏样式
            window.addEventListener('scroll', function() {
                if (window.scrollY > 50) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            });

            // 平滑滚动到锚点
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href');
                    const targetSection = document.querySelector(targetId);

                    if (targetSection) {
                        const offsetTop = targetSection.offsetTop - 80; // 考虑导航栏高度
                        window.scrollTo({
                            top: offsetTop,
                            behavior: 'smooth'
                        });
                    }
                });
            });

            // 高亮当前页面部分
            window.addEventListener('scroll', function() {
                let current = '';
                const sections = document.querySelectorAll('section, header');

                sections.forEach(section => {
                    const sectionTop = section.offsetTop - 100;
                    const sectionHeight = section.clientHeight;

                    if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
                        current = section.getAttribute('id');
                    }
                });

                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === '#' + current) {
                        link.classList.add('active');
                    }
                });
            });
        });
    </script>
</body>
</html>
