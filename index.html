<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开发者工具集 - 实用工具推荐</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #3b82f6;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --border-color: #e5e7eb;
            --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem 1rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto 1.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
            font-weight: 600;
            box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
            transition: transform 0.3s ease;
        }

        .avatar:hover {
            transform: scale(1.05);
        }

        .name {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .bio {
            color: var(--text-secondary);
            font-size: 1.1rem;
            margin-bottom: 2rem;
            line-height: 1.8;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .section {
            margin-bottom: 3rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--text-primary);
            position: relative;
            padding-bottom: 0.5rem;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
        }

        .projects-grid {
            display: grid;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .project-card {
            background: var(--bg-primary);
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
            display: block;
            position: relative;
            overflow: hidden;
        }

        .project-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s ease;
        }

        .project-card:hover::before {
            left: 100%;
        }

        .project-card:hover {
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transform: translateY(-4px);
            text-decoration: none;
            color: inherit;
            border-color: rgba(102, 126, 234, 0.2);
        }

        .project-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .project-icon {
            width: 56px;
            height: 56px;
            border-radius: 12px;
            margin-right: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.75rem;
            color: white;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: transform 0.2s ease;
        }

        .project-card:hover .project-icon {
            transform: scale(1.1);
        }

        .project-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .project-url {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .project-description {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .contact-item {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: 1rem;
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            text-decoration: none;
            color: inherit;
            transition: all 0.2s ease;
        }

        .contact-item:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-1px);
            text-decoration: none;
            color: inherit;
        }

        .contact-title {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .contact-subtitle {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }

        .footer {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid var(--border-color);
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 1rem;
            flex-wrap: wrap;
        }

        .footer-links a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: color 0.2s ease;
        }

        .footer-links a:hover {
            color: var(--primary-color);
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header {
            animation: fadeInUp 0.8s ease-out;
        }

        .project-card {
            animation: fadeInUp 0.8s ease-out;
            animation-fill-mode: both;
        }

        .project-card:nth-child(1) { animation-delay: 0.1s; }
        .project-card:nth-child(2) { animation-delay: 0.2s; }
        .project-card:nth-child(3) { animation-delay: 0.3s; }
        .project-card:nth-child(4) { animation-delay: 0.4s; }

        .contact-item {
            animation: fadeInUp 0.8s ease-out;
            animation-fill-mode: both;
            animation-delay: 0.5s;
        }

        @media (max-width: 640px) {
            .container {
                padding: 1rem;
            }

            .avatar {
                width: 100px;
                height: 100px;
                font-size: 2.5rem;
            }

            .name {
                font-size: 1.75rem;
            }

            .contact-grid {
                grid-template-columns: 1fr;
            }

            .footer-links {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="avatar">工</div>
            <h1 class="name">开发者工具集</h1>
            <p class="bio">
                专注于实用工具开发的程序员<br>
                致力于创造简洁、高效、易用的开发工具<br>
                让复杂的技术变得简单易懂
            </p>
        </header>

        <section class="section">
            <h2 class="section-title">在做什么</h2>
            <div class="projects-grid">
                <a href="./crontab/" class="project-card">
                    <div class="project-header">
                        <div class="project-icon" style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);">⏰</div>
                        <div>
                            <div class="project-title">Crontab 时间戳生成工具</div>
                            <div class="project-url">crontab/</div>
                        </div>
                    </div>
                    <div class="project-description">
                        智能 Crontab 表达式生成器，支持常用模式选择、实时预览和下次运行时间计算。让复杂的定时任务配置变得简单直观，提升运维效率。
                    </div>
                </a>

                <a href="./ipcheck/" class="project-card">
                    <div class="project-header">
                        <div class="project-icon" style="background: linear-gradient(135deg, #10b981 0%, #059669 100%);">🌐</div>
                        <div>
                            <div class="project-title">IP 查询工具</div>
                            <div class="project-url">ipcheck/</div>
                        </div>
                    </div>
                    <div class="project-description">
                        全能网络诊断工具集，集成IP地理位置查询、批量分析、网速测试和浏览器指纹检测。为网络管理员和开发者提供专业的一站式解决方案。
                    </div>
                </a>

                <a href="./suan/" class="project-card">
                    <div class="project-header">
                        <div class="project-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);">🔮</div>
                        <div>
                            <div class="project-title">AI八字分析工具</div>
                            <div class="project-url">suan/</div>
                        </div>
                    </div>
                    <div class="project-description">
                        融合传统命理学与前沿AI技术的智能分析平台，基于深度学习模型提供精准的八字解读和个性化人生指导，传承古典智慧。
                    </div>
                </a>

                <a href="./subnet/" class="project-card">
                    <div class="project-header">
                        <div class="project-icon" style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);">🔧</div>
                        <div>
                            <div class="project-title">子网计算工具</div>
                            <div class="project-url">subnet/</div>
                        </div>
                    </div>
                    <div class="project-description">
                        专业级IPv4/IPv6子网计算器，支持VLSM子网拆分、CIDR汇总合并等高级功能。为网络工程师和系统管理员提供精确的网络规划工具。
                    </div>
                </a>
            </div>
        </section>

        <section class="section">
            <h2 class="section-title">技术栈</h2>
            <div class="contact-grid">
                <div class="contact-item">
                    <div class="contact-title">JavaScript</div>
                    <div class="contact-subtitle">前端开发的核心语言，构建交互式用户界面</div>
                </div>
                <div class="contact-item">
                    <div class="contact-title">PHP</div>
                    <div class="contact-subtitle">服务端开发，处理业务逻辑和数据交互</div>
                </div>
                <div class="contact-item">
                    <div class="contact-title">HTML/CSS</div>
                    <div class="contact-subtitle">网页结构与样式设计，追求简洁美观</div>
                </div>
                <div class="contact-item">
                    <div class="contact-title">AI集成</div>
                    <div class="contact-subtitle">将人工智能技术融入实用工具开发</div>
                </div>
            </div>
        </section>

        <section class="section">
            <h2 class="section-title">联系我</h2>
            <div class="contact-grid">
                <a href="mailto:<EMAIL>" class="contact-item">
                    <div class="contact-title">邮箱</div>
                    <div class="contact-subtitle"><EMAIL></div>
                </a>
                <a href="https://github.com" class="contact-item">
                    <div class="contact-title">GitHub</div>
                    <div class="contact-subtitle">@developer</div>
                </a>
            </div>
        </section>

        <footer class="footer">
            <div class="footer-links">
                <a href="./crontab/">Crontab工具</a>
                <a href="./ipcheck/">IP查询</a>
                <a href="./suan/">八字分析</a>
                <a href="./subnet/">子网计算</a>
            </div>
            <p>© 2025 开发者工具集</p>
        </footer>
    </div>
</body>
</html>
