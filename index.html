<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>开发者工具集 - 实用工具推荐</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --secondary-color: #764ba2;
            --accent-color: #f093fb;
            --text-primary: #1a202c;
            --text-secondary: #718096;
            --text-light: #a0aec0;
            --bg-primary: #ffffff;
            --bg-secondary: #f7fafc;
            --bg-glass: rgba(255, 255, 255, 0.25);
            --border-color: #e2e8f0;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: var(--gradient-bg);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            z-index: -1;
            animation: backgroundShift 20s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 3rem 1rem;
            position: relative;
        }

        .header {
            text-align: center;
            margin-bottom: 4rem;
            position: relative;
        }

        .avatar {
            width: 140px;
            height: 140px;
            border-radius: 50%;
            margin: 0 auto 2rem;
            background: var(--gradient-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3.5rem;
            color: white;
            font-weight: 600;
            box-shadow:
                0 20px 40px rgba(102, 126, 234, 0.4),
                0 0 0 4px rgba(255, 255, 255, 0.1),
                inset 0 0 0 1px rgba(255, 255, 255, 0.2);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }

        .avatar::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            transition: transform 0.6s ease;
        }

        .avatar:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow:
                0 30px 60px rgba(102, 126, 234, 0.5),
                0 0 0 8px rgba(255, 255, 255, 0.1),
                inset 0 0 0 1px rgba(255, 255, 255, 0.3);
        }

        .avatar:hover::before {
            transform: rotate(45deg) translate(50%, 50%);
        }

        .name {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            display: inline-block;
            /* 添加回退颜色以防渐变文字不支持 */
            color: var(--primary-color);
        }

        /* 确保渐变文字在所有浏览器中正常显示 */
        @supports not (-webkit-background-clip: text) {
            .name {
                background: none;
                -webkit-text-fill-color: initial;
                color: var(--primary-color);
            }
        }

        .name::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: var(--gradient-secondary);
            border-radius: 2px;
            opacity: 0.8;
        }

        .bio {
            color: var(--text-secondary);
            font-size: 1.2rem;
            margin-bottom: 2rem;
            line-height: 1.8;
            max-width: 650px;
            margin-left: auto;
            margin-right: auto;
            font-weight: 400;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .section {
            margin-bottom: 3rem;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: var(--text-primary);
            position: relative;
            padding-bottom: 0.5rem;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
        }

        .projects-grid {
            display: grid;
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .project-card {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 2rem;
            box-shadow:
                var(--shadow-lg),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            text-decoration: none;
            color: inherit;
            display: block;
            position: relative;
            overflow: hidden;
        }

        .project-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s ease;
        }

        .project-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
        }

        .project-card:hover::before {
            left: 100%;
        }

        .project-card:hover::after {
            opacity: 0.05;
        }

        .project-card:hover {
            box-shadow:
                var(--shadow-xl),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
            transform: translateY(-8px) scale(1.02);
            text-decoration: none;
            color: inherit;
            border-color: rgba(255, 255, 255, 0.4);
        }

        .project-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .project-icon {
            width: 64px;
            height: 64px;
            border-radius: 16px;
            margin-right: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            font-weight: 600;
            box-shadow:
                0 8px 16px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }

        .project-icon::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.2), transparent);
            transform: rotate(45deg) translate(-100%, -100%);
            transition: transform 0.6s ease;
        }

        .project-card:hover .project-icon {
            transform: scale(1.15) rotate(-5deg);
            box-shadow:
                0 12px 24px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .project-card:hover .project-icon::before {
            transform: rotate(45deg) translate(50%, 50%);
        }

        .project-title {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--text-primary);
            transition: color 0.3s ease;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .project-card:hover .project-title {
            color: var(--primary-color);
        }

        .project-url {
            font-size: 0.9rem;
            color: var(--text-secondary);
            font-weight: 500;
            opacity: 0.9;
        }

        .project-description {
            color: var(--text-primary);
            line-height: 1.7;
            font-size: 1rem;
            margin-top: 1rem;
            font-weight: 400;
            opacity: 0.8;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .contact-item {
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow:
                var(--shadow),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-decoration: none;
            color: inherit;
            transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }

        .contact-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--gradient-primary);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
        }

        .contact-item:hover::before {
            opacity: 0.05;
        }

        .contact-item:hover {
            box-shadow:
                var(--shadow-lg),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            transform: translateY(-4px) scale(1.02);
            text-decoration: none;
            color: inherit;
            border-color: rgba(255, 255, 255, 0.3);
        }

        .contact-title {
            font-weight: 700;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
            color: var(--text-primary);
        }

        .contact-subtitle {
            font-size: 0.9rem;
            color: var(--text-secondary);
            line-height: 1.5;
        }

        .footer {
            text-align: center;
            padding: 3rem 0 2rem;
            margin-top: 4rem;
            background: var(--bg-glass);
            backdrop-filter: blur(20px);
            border-radius: 24px 24px 0 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-bottom: none;
            color: var(--text-secondary);
            font-size: 0.9rem;
            position: relative;
            overflow: hidden;
        }

        .footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--gradient-primary);
            opacity: 0.6;
        }

        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2.5rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .footer-links a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: all 0.3s ease;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 500;
            position: relative;
        }

        .footer-links a::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: var(--gradient-primary);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .footer-links a:hover {
            color: var(--primary-color);
            transform: translateY(-2px);
        }

        .footer-links a:hover::before {
            width: 80%;
        }

        .footer p {
            margin: 0;
            opacity: 0.8;
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(40px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInScale {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .header {
            animation: fadeInScale 1s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .section {
            animation: fadeInUp 0.8s ease-out;
            animation-fill-mode: both;
        }

        .section:nth-child(2) { animation-delay: 0.2s; }
        .section:nth-child(3) { animation-delay: 0.4s; }
        .section:nth-child(4) { animation-delay: 0.6s; }

        .project-card {
            animation: fadeInUp 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            animation-fill-mode: both;
        }

        .project-card:nth-child(1) { animation-delay: 0.3s; }
        .project-card:nth-child(2) { animation-delay: 0.4s; }
        .project-card:nth-child(3) { animation-delay: 0.5s; }
        .project-card:nth-child(4) { animation-delay: 0.6s; }
        .project-card:nth-child(5) { animation-delay: 0.7s; }

        .contact-item {
            animation: fadeInUp 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            animation-fill-mode: both;
        }

        .contact-item:nth-child(1) { animation-delay: 0.7s; }
        .contact-item:nth-child(2) { animation-delay: 0.8s; }
        .contact-item:nth-child(3) { animation-delay: 0.9s; }
        .contact-item:nth-child(4) { animation-delay: 1.0s; }

        /* 浮动装饰元素 */
        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .shape {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 60px;
            height: 60px;
            top: 20%;
            right: 10%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 100px;
            height: 100px;
            bottom: 20%;
            left: 5%;
            animation-delay: 4s;
        }

        .shape:nth-child(4) {
            width: 40px;
            height: 40px;
            bottom: 10%;
            right: 20%;
            animation-delay: 1s;
        }

        @media (max-width: 640px) {
            .container {
                padding: 1rem;
            }

            .avatar {
                width: 100px;
                height: 100px;
                font-size: 2.5rem;
            }

            .name {
                font-size: 1.75rem;
            }

            .contact-grid {
                grid-template-columns: 1fr;
            }

            .footer-links {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 浮动装饰元素 -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="container">
        <header class="header">
            <div class="avatar">工</div>
            <h1 class="name">开发者工具集</h1>
            <p class="bio">
                专注于实用工具开发的程序员<br>
                致力于创造简洁、高效、易用的开发工具<br>
                让复杂的技术变得简单易懂
            </p>
        </header>

        <section class="section">
            <h2 class="section-title">在做什么</h2>
            <div class="projects-grid">
                <a href="./crontab/" class="project-card">
                    <div class="project-header">
                        <div class="project-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">⏰</div>
                        <div>
                            <div class="project-title">Crontab 时间戳生成工具</div>
                            <div class="project-url">crontab/</div>
                        </div>
                    </div>
                    <div class="project-description">
                        智能 Crontab 表达式生成器，支持常用模式选择、实时预览和下次运行时间计算。让复杂的定时任务配置变得简单直观，提升运维效率。
                    </div>
                </a>

                <a href="./ipcheck/" class="project-card">
                    <div class="project-header">
                        <div class="project-icon" style="background: linear-gradient(135deg, #06d6a0 0%, #118ab2 100%);">🌐</div>
                        <div>
                            <div class="project-title">IP 查询工具</div>
                            <div class="project-url">ipcheck/</div>
                        </div>
                    </div>
                    <div class="project-description">
                        全能网络诊断工具集，集成IP地理位置查询、批量分析、网速测试和浏览器指纹检测。为网络管理员和开发者提供专业的一站式解决方案。
                    </div>
                </a>

                <a href="./suan/" class="project-card">
                    <div class="project-header">
                        <div class="project-icon" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">🔮</div>
                        <div>
                            <div class="project-title">AI八字分析工具</div>
                            <div class="project-url">suan/</div>
                        </div>
                    </div>
                    <div class="project-description">
                        融合传统命理学与前沿AI技术的智能分析平台，基于深度学习模型提供精准的八字解读和个性化人生指导，传承古典智慧。
                    </div>
                </a>

                <a href="./subnet/" class="project-card">
                    <div class="project-header">
                        <div class="project-icon" style="background: linear-gradient(135deg, #ffd60a 0%, #ff8500 100%);">🔧</div>
                        <div>
                            <div class="project-title">子网计算工具</div>
                            <div class="project-url">subnet/</div>
                        </div>
                    </div>
                    <div class="project-description">
                        专业级IPv4/IPv6子网计算器，支持VLSM子网拆分、CIDR汇总合并等高级功能。为网络工程师和系统管理员提供精确的网络规划工具。
                    </div>
                </a>

                <a href="https://www.6ird.com/tools/cfip/" class="project-card" target="_blank">
                    <div class="project-header">
                        <div class="project-icon" style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);">☁️</div>
                        <div>
                            <div class="project-title">Cloudflare优选IP工具</div>
                            <div class="project-url">6ird.com/tools/cfip/</div>
                        </div>
                    </div>
                    <div class="project-description">
                        Cloudflare全能优选工具，从全球网络中智能筛选最快的节点IP。支持IPv4/IPv6双栈测试，提供延迟和速度双重优化，助力网络加速。
                    </div>
                </a>
            </div>
        </section>

        <section class="section">
            <h2 class="section-title">技术栈</h2>
            <div class="contact-grid">
                <div class="contact-item">
                    <div class="contact-title">JavaScript</div>
                    <div class="contact-subtitle">前端开发的核心语言，构建交互式用户界面</div>
                </div>
                <div class="contact-item">
                    <div class="contact-title">PHP</div>
                    <div class="contact-subtitle">服务端开发，处理业务逻辑和数据交互</div>
                </div>
                <div class="contact-item">
                    <div class="contact-title">HTML/CSS</div>
                    <div class="contact-subtitle">网页结构与样式设计，追求简洁美观</div>
                </div>
                <div class="contact-item">
                    <div class="contact-title">AI集成</div>
                    <div class="contact-subtitle">将人工智能技术融入实用工具开发</div>
                </div>
            </div>
        </section>

        <section class="section">
            <h2 class="section-title">联系我</h2>
            <div class="contact-grid">
                <a href="mailto:<EMAIL>" class="contact-item">
                    <div class="contact-title">邮箱</div>
                    <div class="contact-subtitle"><EMAIL></div>
                </a>
                <a href="https://github.com" class="contact-item">
                    <div class="contact-title">GitHub</div>
                    <div class="contact-subtitle">@developer</div>
                </a>
            </div>
        </section>

        <footer class="footer">
            <div class="footer-links">
                <a href="./crontab/">Crontab工具</a>
                <a href="./ipcheck/">IP查询</a>
                <a href="./suan/">八字分析</a>
                <a href="./subnet/">子网计算</a>
                <a href="https://www.6ird.com/tools/cfip/" target="_blank">CF优选IP</a>
            </div>
            <p>© 2025 开发者工具集</p>
        </footer>
    </div>
</body>
</html>
