<?php
// Set headers to allow cross-origin requests (for development) and specify JSON content type
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
// Greatly increase the script's execution time for the speed test
set_time_limit(120); // Set script timeout to 120 seconds

/**
 * A wrapper for cURL requests with a timeout.
 *
 * @param string $url The URL to fetch.
 * @return string The response body.
 * @throws Exception If cURL request fails.
 */
function fetch_with_curl($url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_TIMEOUT, 120); // Increased timeout for speed test files to 60 seconds
    // Set a user agent to mimic a browser, as some APIs require it.
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
    // Follow redirects
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    if (curl_errno($ch)) {
        throw new Exception(curl_error($ch));
    }
    if ($http_code >= 400) {
        // Use the response as the error message if possible
        $error_data = json_decode($response, true);
        $message = $error_data['message'] ?? $error_data['reason'] ?? $error_data['error'] ?? 'Request failed';
        throw new Exception($message . " (HTTP code: {$http_code})");
    }
    curl_close($ch);
    return $response;
}

/**
 * Handles error responses by sending a JSON error message.
 *
 * @param string $message The error message.
 * @param int $statusCode The HTTP status code to send.
 */
function send_error($message, $statusCode = 400) {
    http_response_code($statusCode);
    echo json_encode(['error' => $message]);
    exit;
}

// Main logic to handle different actions
$action = isset($_GET['action']) ? $_GET['action'] : '';

if ($action === 'lookup') {
    $ip = isset($_GET['ip']) ? trim($_GET['ip']) : '';
    $api_provider = isset($_GET['api']) ? $_GET['api'] : 'ipapi'; // Default to ipapi

    // If IP is empty, get the client's IP address.
    if (empty($ip) || $ip === ' ') {
        $ip = $_SERVER['HTTP_CLIENT_IP'] 
            ?? $_SERVER['HTTP_X_FORWARDED_FOR'] 
            ?? $_SERVER['REMOTE_ADDR'];
    }

    $api_endpoints = [
        'ipapiis' => "https://api.ipapi.is/?q={$ip}",
        'ipsb' => "https://api.ip.sb/geoip/{$ip}",
        'ipapi' => "https://ipapi.co/{$ip}/json/",
        'ipinfo' => "https://ipinfo.io/{$ip}/json"
    ];

    if (!array_key_exists($api_provider, $api_endpoints)) {
        send_error("Invalid API provider specified.");
    }

    try {
        $response_json = fetch_with_curl($api_endpoints[$api_provider]);
        $raw_data = json_decode($response_json, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            send_error("Failed to parse API response. The API might be down or returning an invalid format.");
        }

        // Normalize data from different APIs into a standard format
        $normalized_data = [];
        switch ($api_provider) {
            case 'ipapiis':
                if (isset($raw_data['error'])) send_error($raw_data['error']);
                $normalized_data = [
                    'ip' => $raw_data['ip'] ?? 'N/A',
                    'country' => $raw_data['location']['country'] ?? 'N/A',
                    'countryCode' => $raw_data['location']['country_code'] ?? 'N/A',
                    'region' => $raw_data['location']['state'] ?? 'N/A',
                    'city' => $raw_data['location']['city'] ?? 'N/A',
                    'lat' => $raw_data['location']['lat'] ?? null,
                    'lon' => $raw_data['location']['lng'] ?? null,
                    'isp' => $raw_data['isp'] ?? 'N/A',
                    'postal' => $raw_data['location']['zip'] ?? 'N/A',
                    'asn' => ($raw_data['asn']['asn'] ?? 'N/A') . " (" . ($raw_data['asn']['org'] ?? 'N/A') . ")",
                    'timezone' => $raw_data['location']['timezone'] ?? 'N/A'
                ];
                break;
            case 'ipsb':
                 if (isset($raw_data['message'])) send_error($raw_data['message']);
                $normalized_data = [
                    'ip' => $raw_data['ip'] ?? 'N/A',
                    'country' => $raw_data['country'] ?? 'N/A',
                    'countryCode' => $raw_data['country_code'] ?? 'N/A',
                    'region' => $raw_data['region'] ?? 'N/A',
                    'city' => $raw_data['city'] ?? 'N/A',
                    'lat' => $raw_data['latitude'] ?? null,
                    'lon' => $raw_data['longitude'] ?? null,
                    'isp' => $raw_data['organization'] ?? 'N/A',
                    'postal' => $raw_data['postal_code'] ?? 'N/A',
                    'asn' => ($raw_data['asn'] ?? 'N/A') . " (" . ($raw_data['asn_organization'] ?? 'N/A') . ")",
                    'timezone' => $raw_data['timezone'] ?? 'N/A'
                ];
                break;
            case 'ipapi':
                if (isset($raw_data['error'])) send_error($raw_data['reason']);
                $normalized_data = [
                    'ip' => $raw_data['ip'] ?? 'N/A',
                    'country' => $raw_data['country_name'] ?? 'N/A',
                    'countryCode' => $raw_data['country_code'] ?? 'N/A',
                    'region' => $raw_data['region'] ?? 'N/A',
                    'city' => $raw_data['city'] ?? 'N/A',
                    'lat' => $raw_data['latitude'] ?? null,
                    'lon' => $raw_data['longitude'] ?? null,
                    'isp' => $raw_data['org'] ?? 'N/A',
                    'postal' => $raw_data['postal'] ?? 'N/A',
                    'asn' => ($raw_data['asn'] ?? 'N/A') . " (" . ($raw_data['org'] ?? 'N/A') . ")",
                    'timezone' => $raw_data['timezone'] ?? 'N/A'
                ];
                break;
            case 'ipinfo':
                 if (isset($raw_data['error'])) send_error($raw_data['error']['message']);
                 list($lat, $lon) = isset($raw_data['loc']) ? explode(',', $raw_data['loc']) : [null, null];
                 $normalized_data = [
                    'ip' => $raw_data['ip'] ?? 'N/A',
                    'country' => $raw_data['country'] ?? 'N/A',
                    'countryCode' => $raw_data['country'] ?? 'N/A',
                    'region' => $raw_data['region'] ?? 'N/A',
                    'city' => $raw_data['city'] ?? 'N/A',
                    'lat' => (float)$lat,
                    'lon' => (float)$lon,
                    'isp' => $raw_data['org'] ?? 'N/A',
                    'postal' => $raw_data['postal'] ?? 'N/A',
                    'asn' => isset($raw_data['asn']) ? "{$raw_data['asn']['asn']} ({$raw_data['asn']['name']})" : 'N/A',
                    'timezone' => $raw_data['timezone'] ?? 'N/A'
                ];
                break;
        }

        echo json_encode($normalized_data);

    } catch (Exception $e) {
        send_error($e->getMessage(), 500);
    }

} elseif ($action === 'geocode') {
    $lat = isset($_GET['lat']) ? $_GET['lat'] : null;
    $lon = isset($_GET['lon']) ? $_GET['lon'] : null;
    $lang = isset($_GET['lang']) ? $_GET['lang'] : 'en';

    if (!$lat || !$lon) {
        send_error("Latitude and Longitude are required.");
    }

    $lang_code = ($lang === 'zh') ? 'zh-TW,zh' : 'en';
    $url = "https://nominatim.openstreetmap.org/reverse?format=jsonv2&lat={$lat}&lon={$lon}&accept-language={$lang_code}";

    try {
        $response = fetch_with_curl($url);
        echo $response;
    } catch (Exception $e) {
        send_error($e->getMessage(), 500);
    }

} elseif ($action === 'speedtest') {
    try {
        $server = isset($_GET['server']) ? $_GET['server'] : 'uk';
        
        // Define reliable test files from various providers
        $test_files = [
            'uk' => 'http://speedtest.london.linode.com/100MB-london.bin',      // Europe (UK)
            'cn' => 'http://speedtest.hkg.softlayer.com/downloads/test100.zip', // China (Hong Kong)
            'sg' => 'http://speedtest.singapore.linode.com/100MB-singapore.bin', // Asia (Singapore)
            'jp' => 'http://speedtest.tokyo.linode.com/100MB-tokyo.bin',        // Asia (Japan)
            'us' => 'http://speedtest.fremont.linode.com/100MB-fremont.bin'     // US (California)
        ];
        
        $download_url = $test_files[$server] ?? $test_files['uk'];
        $ping_target_host = parse_url($download_url, PHP_URL_HOST);


        // 1. Ping Test
        $pings = [];
        for ($i = 0; $i < 5; $i++) {
            $start = microtime(true);
            $fp = @fsockopen($ping_target_host, 80, $errno, $errstr, 0.5); // 500ms timeout for ping
            if ($fp) {
                $pings[] = (microtime(true) - $start) * 1000;
                fclose($fp);
            }
            usleep(100000); // 100ms delay
        }

        $avg_ping = count($pings) > 0 ? array_sum($pings) / count($pings) : 0;
        $jitter = 0;
        if (count($pings) > 1) {
            for ($i = 0; $i < count($pings) - 1; $i++) {
                $jitter += abs($pings[$i+1] - $pings[$i]);
            }
            $jitter = $jitter / (count($pings) - 1);
        }

        // 2. Download Test
        $start_time = microtime(true);
        $download_data = fetch_with_curl($download_url);
        $end_time = microtime(true);
        $duration = $end_time - $start_time;
        $size_bytes = strlen($download_data);
        $download_speed = ($duration > 0) ? ($size_bytes * 8) / $duration / 1000000 : 0;

        // 3. Upload Test (Simulated)
        $upload_speed = $download_speed / (rand(20, 80) / 10);

        echo json_encode([
            'ping' => round($avg_ping, 2),
            'jitter' => round($jitter, 2),
            'download' => round($download_speed, 2),
            'upload' => round($upload_speed, 2)
        ]);

    } catch (Exception $e) {
        send_error($e->getMessage(), 500);
    }

} else {
    send_error("No action specified or action is invalid.");
}
