<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IP 查询工具 - IP Lookup Tool</title>
    
    <!-- LeafletJS for Map Display -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
    
    <!-- Google Fonts for better typography -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">

    <style>
        /* General Styling with CSS Variables for Theming */
        :root {
            --primary-color: #3b82f6; /* A slightly softer blue */
            --primary-hover: #2563eb;
            --secondary-color: #6b7280; /* A neutral gray */
            --secondary-hover: #4b5563;
            --background-color: #f9fafb; /* Lighter gray background */
            --container-bg: #ffffff;
            --text-color: #1f2937; /* Darker text for better contrast */
            --subtle-text-color: #6b7280;
            --border-color: #e5e7eb;
            --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --table-header-bg: #f3f4f6;
        }

        .dark {
            --background-color: #111827; /* Deeper dark blue */
            --container-bg: #1f2937;
            --text-color: #e5e7eb;
            --subtle-text-color: #9ca3af;
            --border-color: #374151;
            --shadow: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
            --table-header-bg: #374151;
        }

        body {
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
            transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out;
        }

        .container {
            width: 100%;
            max-width: 800px;
            background: var(--container-bg);
            padding: 25px 35px;
            border-radius: 16px; /* Larger radius */
            box-shadow: var(--shadow);
            border: 1px solid var(--border-color);
            transition: background-color 0.3s ease-in-out, border-color 0.3s ease-in-out;
        }

        /* Header and Controls */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            flex-wrap: wrap;
            gap: 15px;
        }

        h1 {
            color: var(--primary-color);
            margin: 0;
            font-size: 26px;
            font-weight: 700;
        }

        .controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .theme-switch, .lang-switch button, .api-switch select, .server-switch select {
            background-color: transparent;
            border: 1px solid var(--border-color);
            padding: 8px 12px;
            cursor: pointer;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s ease-in-out;
            color: var(--subtle-text-color);
        }
        .theme-switch:hover, .lang-switch button:hover, .api-switch select:hover, .server-switch select:hover {
            background-color: var(--background-color);
            border-color: var(--primary-color);
            color: var(--primary-color);
        }
        #theme-icon {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* Tab Controls */
        .tab-controls {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 25px;
            flex-wrap: wrap;
        }
        .tab-btn {
            padding: 10px 15px;
            cursor: pointer;
            border: none;
            background: none;
            color: var(--subtle-text-color);
            font-size: 16px;
            position: relative;
            margin-bottom: -1px;
            border-bottom: 2px solid transparent;
             transition: color 0.2s ease-in-out;
        }
        .tab-btn.active {
            color: var(--primary-color);
            font-weight: 600;
            border-bottom: 2px solid var(--primary-color);
        }

        .tab-content { display: none; }
        .tab-content.active { display: block; }
        
        /* Input Group */
        .input-group {
            display: flex;
            margin-bottom: 20px;
        }
        #ipInput, #bulkIpInput {
            flex-grow: 1;
            padding: 12px 15px;
            border: 1px solid var(--border-color);
            border-radius: 8px 0 0 8px;
            font-size: 16px;
            outline: none;
            background-color: var(--background-color);
            color: var(--text-color);
            transition: all 0.2s ease-in-out;
        }
        #bulkIpInput {
            min-height: 150px;
            border-radius: 8px;
            resize: vertical;
        }
        #ipInput:focus, #bulkIpInput:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px color-mix(in srgb, var(--primary-color) 20%, transparent);
        }
        #queryBtn, #showMyIpBtn, #bulkQueryBtn, #startSpeedTestBtn {
            padding: 12px 20px;
            border: none;
            color: white;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            white-space: nowrap;
        }
        #queryBtn {
            background: var(--primary-color);
            border-radius: 0;
        }
        #bulkQueryBtn, #startSpeedTestBtn {
             background: var(--primary-color);
             border-radius: 8px;
             margin-top: 10px;
             width: 100%;
        }
        #startSpeedTestBtn:disabled {
            background-color: var(--secondary-color);
            cursor: not-allowed;
        }
        #queryBtn:hover, #bulkQueryBtn:hover, #startSpeedTestBtn:not(:disabled):hover {
            background: var(--primary-hover);
        }
        #queryBtn:active, #bulkQueryBtn:active, #startSpeedTestBtn:active {
            transform: scale(0.98);
        }
        #showMyIpBtn {
            background: var(--secondary-color);
            border-radius: 0 8px 8px 0;
        }
        #showMyIpBtn:hover {
             background: var(--secondary-hover);
        }
        
        /* Result and History Sections */
        #map {
            height: 300px;
            width: 100%;
            margin-top: 20px;
            border-radius: 12px;
            border: 1px solid var(--border-color);
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 25px;
            margin-top: 25px;
        }
        .info-section {
            position: relative;
        }
        .info-section h3 {
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 12px;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 18px;
            color: var(--text-color);
            font-weight: 600;
        }
        #result, #history, #fingerprint-lookup {
            background-color: var(--background-color);
            padding: 20px;
            border-radius: 12px;
            border: 1px solid var(--border-color);
            min-height: 150px;
        }
        #result p, #fingerprint-details p {
            margin: 0 0 12px;
            line-height: 1.7;
            word-break: break-all;
            display: flex;
            flex-direction: column;
        }
        #result p strong, #fingerprint-details p strong {
            color: var(--subtle-text-color);
            font-weight: 500;
            margin-bottom: 4px;
            width: auto;
        }
        .copy-btn {
            position: absolute;
            top: -2px;
            right: 0;
            background: none;
            border: none;
            color: var(--subtle-text-color);
            padding: 4px 8px;
            border-radius: 5px;
            cursor: pointer;
            transition: color 0.2s ease-in-out;
        }
        .copy-btn:hover {
             color: var(--primary-color);
        }
        .copy-btn svg {
            width: 16px;
            height: 16px;
        }
        #history ul {
            list-style: none;
            padding: 0;
            margin: 0;
            max-height: 330px; 
            overflow-y: auto;
        }
        #history li {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px;
            border-bottom: 1px solid var(--border-color);
            border-radius: 8px;
             transition: background-color 0.2s ease-in-out;
        }
        #history li:last-child {
            border-bottom: none;
        }
        #history li:hover {
            background: color-mix(in srgb, var(--background-color) 50%, var(--container-bg));
        }
        .history-ip-text { flex-grow: 1; cursor: pointer; }
        .delete-btn {
            padding: 0 10px;
            color: var(--subtle-text-color);
            font-size: 22px;
            font-weight: bold;
            transition: color 0.2s ease-in-out;
            border-radius: 50%;
            cursor: pointer;
        }
        .delete-btn:hover { color: #ef4444; }
        
        /* Fingerprint Section */
        #fingerprint-hash {
            font-family: monospace;
            background-color: var(--background-color);
            padding: 15px;
            border-radius: 8px;
            word-break: break-all;
            margin-top: 20px;
            border: 1px solid var(--border-color);
            text-align: center;
        }
         #fingerprint-hash strong {
            display: block;
            margin-bottom: 10px;
            font-size: 16px;
            color: var(--primary-color);
         }
        
        /* Speed Test UI */
        #speedtest-lookup {
            padding: 20px 0;
        }
        #speed-test-info, .server-switch {
            background-color: var(--background-color);
            padding: 15px;
            border-radius: 12px;
            text-align: center;
            margin-bottom: 20px;
            border: 1px solid var(--border-color);
        }
        #speed-test-display {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .speed-gauge {
            background-color: var(--background-color);
            padding: 20px;
            border-radius: 12px;
            border: 1px solid var(--border-color);
        }
        .speed-gauge .speed-label {
            font-size: 16px;
            color: var(--subtle-text-color);
            margin-bottom: 10px;
        }
        .speed-gauge .speed-value {
            font-size: 32px;
            font-weight: 700;
            color: var(--primary-color);
            transition: color 0.3s ease-in-out;
        }
        .speed-gauge .speed-unit {
            font-size: 16px;
            color: var(--subtle-text-color);
            margin-left: 5px;
        }
        .speed-meta {
            margin-top: 15px;
            display: flex;
            justify-content: center;
            gap: 20px;
        }
        .speed-meta span {
            font-size: 14px;
        }

        /* Bulk Results Table */
        #bulk-results-container {
            margin-top: 20px;
            max-height: 500px;
            overflow: auto;
            border: 1px solid var(--border-color);
            border-radius: 12px;
        }
        #bulk-results-table {
            width: 100%;
            border-collapse: collapse;
        }
        #bulk-results-table th, #bulk-results-table td {
            border-bottom: 1px solid var(--border-color);
            padding: 12px;
            text-align: left;
        }
        #bulk-results-table tr:last-child td {
            border-bottom: none;
        }
        #bulk-results-table th {
            background-color: var(--table-header-bg);
            position: sticky;
            top: 0;
            font-weight: 600;
        }

        .loader {
            text-align: center;
            padding: 20px;
            display: none;
        }
        .spinner {
            border: 4px solid var(--border-color);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border-left-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
            margin: auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Responsive Overrides */
        @media (min-width: 640px) {
            .info-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            #speed-test-display {
                grid-template-columns: 1fr 1fr;
            }
             #result p, #fingerprint-details p {
                flex-direction: row;
             }
             #result p strong, #fingerprint-details p strong {
                width: 120px;
             }
        }
        
        @media (max-width: 480px) {
            .container {
                 padding: 15px 20px;
            }
            h1 {
                font-size: 22px;
            }
            .tab-btn {
                padding: 10px;
                font-size: 14px;
            }
            .input-group {
                flex-direction: column;
            }
            #ipInput {
                border-radius: 8px;
                margin-bottom: 10px;
            }
            #showMyIpBtn, #queryBtn {
                 border-radius: 8px;
            }
            #showMyIpBtn {
                margin-bottom: 10px; /* Spacing between the two buttons */
            }
            .input-group button {
                 width: 100%;
            }
            .header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }
            .controls {
                width: 100%;
            }
            .controls .lang-switch {
                margin-left: auto; /* Push lang and theme switches to the right */
            }
        }
    </style>
</head>
<body>

<div class="container">
    <div class="header">
        <h1 id="title">IP 查询工具</h1>
        <div class="controls">
            <div class="api-switch">
                <select id="apiSelector">
                    <option value="ipapi" selected>ipapi.co</option>
                    <option value="ipapiis">ipapi.is</option>
                    <option value="ipsb">ip.sb</option>
                    <option value="ipinfo">ipinfo.io</option>
                </select>
            </div>
            <div class="lang-switch">
                <button id="langBtn">English</button>
            </div>
             <button class="theme-switch" id="theme-switch" aria-label="Toggle dark mode">
                <span id="theme-icon"></span>
            </button>
        </div>
    </div>

    <div class="tab-controls">
        <button class="tab-btn active" data-tab="single">单个查询</button>
        <button class="tab-btn" data-tab="bulk">批量查询</button>
        <button class="tab-btn" data-tab="speedtest">网速测试</button>
        <button class="tab-btn" data-tab="fingerprint">浏览器指纹</button>
    </div>

    <div id="single-lookup" class="tab-content active">
        <div class="input-group">
            <input type="text" id="ipInput" placeholder="输入IP地址">
            <button id="queryBtn">查询</button>
            <button id="showMyIpBtn">我的IP</button>
        </div>
        <div id="map"></div>
        <div class="info-grid">
            <div class="info-section">
                <h3 id="resultTitle">查询结果</h3>
                <button class="copy-btn" id="copy-btn" style="display: none;" title="复制结果">
                     <svg id="copy-icon-svg" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect><path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path></svg>
                     <span id="copy-text"></span>
                </button>
                <div id="result">
                    <p id="resultDefaultText" style="color: var(--subtle-text-color);">暂无结果。</p>
                </div>
            </div>
            <div class="info-section">
                <h3 id="historyTitle">查询历史</h3>
                <div id="history">
                    <ul id="historyList"></ul>
                </div>
            </div>
        </div>
    </div>

    <div id="bulk-lookup" class="tab-content">
        <textarea id="bulkIpInput" rows="8" placeholder="每行输入一个IP地址..."></textarea>
        <button id="bulkQueryBtn">查询所有</button>
        <div class="loader" id="bulk-loader">
            <div class="spinner"></div>
        </div>
        <div id="bulk-results-container"></div>
    </div>
    
    <div id="speedtest-lookup" class="tab-content">
        <div id="speed-test-info">
            <p><span id="your-ip-label">您的IP地址</span>: <strong id="speed-test-ip">...</strong></p>
            <p><span id="provider-label">服务由</span>: <strong id="speed-test-isp">...</strong></p>
        </div>
        <div class="server-switch">
             <label for="speed-test-server" id="test-node-label">测试节点: </label>
             <select id="speed-test-server">
                <option value="uk" id="node-uk">欧洲 (英国)</option>
                <option value="cn" id="node-cn">中国 (香港)</option>
                <option value="sg" id="node-sg">亚洲 (新加坡)</option>
                <option value="jp" id="node-jp">亚洲 (日本)</option>
                <option value="us" id="node-us" selected>美国 (加州)</option>
            </select>
        </div>
        <div id="speed-test-display">
            <div class="speed-gauge">
                <div class="speed-label" id="download-label">下载速度</div>
                <div class="speed-value" id="download-speed">0.00</div>
                <div class="speed-unit">Mbps</div>
            </div>
             <div class="speed-gauge">
                <div class="speed-label" id="upload-label">上传速度</div>
                <div class="speed-value" id="upload-speed">0.00</div>
                <div class="speed-unit">Mbps</div>
            </div>
        </div>
        <div class="speed-meta">
            <span>Ping: <strong id="ping-value">-</strong> ms</span>
            <span>Jitter: <strong id="jitter-value">-</strong> ms</span>
        </div>
        <button id="startSpeedTestBtn">开始测试</button>
    </div>

    <div id="fingerprint-lookup" class="tab-content">
        <h3 id="fingerprint-title">浏览器指纹信息</h3>
        <div id="fingerprint-details"></div>
        <div id="fingerprint-hash"></div>
    </div>

</div>

<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
<script>
    // --- DOM Elements ---
    const ipInput = document.getElementById('ipInput');
    const queryBtn = document.getElementById('queryBtn');
    const showMyIpBtn = document.getElementById('showMyIpBtn');
    const resultDiv = document.getElementById('result');
    const historyList = document.getElementById('historyList');
    const mapDiv = document.getElementById('map');
    const langBtn = document.getElementById('langBtn');
    const apiSelector = document.getElementById('apiSelector');
    const themeSwitch = document.getElementById('theme-switch');
    const themeIcon = document.getElementById('theme-icon');
    const copyBtn = document.getElementById('copy-btn');
    const copyText = document.getElementById('copy-text');
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    const singleLookupDiv = document.getElementById('single-lookup');
    const bulkIpInput = document.getElementById('bulkIpInput');
    const bulkQueryBtn = document.getElementById('bulkQueryBtn');
    const bulkLoader = document.getElementById('bulk-loader');
    const bulkResultsContainer = document.getElementById('bulk-results-container');
    const startSpeedTestBtn = document.getElementById('startSpeedTestBtn');
    const downloadSpeedEl = document.getElementById('download-speed');
    const uploadSpeedEl = document.getElementById('upload-speed');
    const pingValueEl = document.getElementById('ping-value');
    const jitterValueEl = document.getElementById('jitter-value');
    const speedTestIpEl = document.getElementById('speed-test-ip');
    const speedTestIspEl = document.getElementById('speed-test-isp');
    const fingerprintDetailsDiv = document.getElementById('fingerprint-details');
    const fingerprintHashDiv = document.getElementById('fingerprint-hash');
    const loader = document.createElement('div');
    loader.className = 'loader';
    loader.innerHTML = '<div class="spinner"></div>';
    singleLookupDiv.insertBefore(loader, mapDiv);


    // --- Language & Translation Data ---
    let currentLang = 'zh';
    const translations = {
        zh: {
            title: "IP 查询工具",
            placeholder: "输入IP地址",
            query: "查询",
            showMyIp: "我的IP",
            apiLabel: "接口:",
            resultTitle: "查询结果",
            resultDefaultText: "暂无结果。",
            historyTitle: "查询历史",
            querying: "查询中...",
            generatingAddr: "生成真实地址中...",
            error: "查询失败，请检查IP地址或网络。",
            langBtn: "English",
            ip: "IP 地址",
            country: "国家",
            region: "省份/州",
            city: "城市",
            isp: "运营商",
            postal: "邮政编码",
            asn: "ASN",
            timezone: "时区",
            lat: "纬度",
            lon: "经度",
            generatedAddr: "生成地址",
            estimated: "(估算)",
            delete: "删除此条记录",
            copy: "复制",
            copied: "已复制!",
            singleLookup: "单个查询",
            bulkLookup: "批量查询",
            speedTest: "网速测试",
            fingerprint: "浏览器指纹",
            fingerprintTitle: "浏览器指纹信息",
            fingerprintHash: "浏览器指纹 (SHA-256)",
            fpUserAgent: "用户代理",
            fpResolution: "屏幕分辨率",
            fpColorDepth: "色彩深度",
            fpLanguage: "语言",
            fpPlatform: "平台",
            fpPlugins: "插件",
            fpFonts: "字体",
            fpCanvas: "Canvas指纹",
            startTest: "开始测试",
            testing: "测试中...",
            download: "下载速度",
            upload: "上传速度",
            queryAll: "查询所有",
            bulkPlaceholder: "每行输入一个IP地址...",
            yourIP: "您的IP地址",
            provider: "服务由",
            testNode: "测试节点",
            nodeUK: "欧洲 (英国)",
            nodeCN: "中国 (香港)",
            nodeSG: "亚洲 (新加坡)",
            nodeJP: "亚洲 (日本)",
            nodeUS: "美国 (加州)"
        },
        en: {
            title: "IP Lookup Tool",
            placeholder: "Enter IP address",
            query: "Query",
            showMyIp: "My IP",
            apiLabel: "API:",
            resultTitle: "Query Result",
            resultDefaultText: "No results yet.",
            historyTitle: "Query History",
            querying: "Querying...",
            generatingAddr: "Generating real address...",
            error: "Failed to query. Check the IP or your connection.",
            langBtn: "中文",
            ip: "IP Address",
            country: "Country",
            region: "Region/State",
            city: "City",
            isp: "ISP",
            postal: "Postal Code",
            asn: "ASN",
            timezone: "Timezone",
            lat: "Latitude",
            lon: "Longitude",
            generatedAddr: "Generated Address",
            estimated: "(Estimated)",
            delete: "Delete this record",
            copy: "Copy",
            copied: "Copied!",
            singleLookup: "Single Lookup",
            bulkLookup: "Bulk Lookup",
            speedTest: "Speed Test",
            fingerprint: "Browser Fingerprint",
            fingerprintTitle: "Browser Fingerprint Information",
            fingerprintHash: "Browser Fingerprint (SHA-256)",
            fpUserAgent: "User Agent",
            fpResolution: "Screen Resolution",
            fpColorDepth: "Color Depth",
            fpLanguage: "Language",
            fpPlatform: "Platform",
            fpPlugins: "Plugins",
            fpFonts: "Fonts",
            fpCanvas: "Canvas Fingerprint",
            startTest: "Start Test",
            testing: "Testing...",
            download: "Download",
            upload: "Upload",
            queryAll: "Query All",
            bulkPlaceholder: "Enter one IP address...",
            yourIP: "Your IP Address",
            provider: "Service provided by",
            testNode: "Test Node",
            nodeUK: "Europe (UK)",
            nodeCN: "China (Hong Kong)",
            nodeSG: "Asia (Singapore)",
            nodeJP: "Asia (Japan)",
            nodeUS: "US (California)"
        }
    };

    function setLanguage(lang) {
        currentLang = lang;
        document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en';
        const t = translations[lang];
        document.getElementById('title').textContent = t.title;
        ipInput.placeholder = t.placeholder;
        bulkIpInput.placeholder = t.bulkPlaceholder;
        queryBtn.textContent = t.query;
        showMyIpBtn.textContent = t.showMyIp;
        bulkQueryBtn.textContent = t.queryAll;
        document.getElementById('resultTitle').textContent = t.resultTitle;
        if(document.getElementById('resultDefaultText')) {
            document.getElementById('resultDefaultText').textContent = t.resultDefaultText;
        }
        document.getElementById('historyTitle').textContent = t.historyTitle;
        langBtn.textContent = t.langBtn;
        document.querySelector('[data-tab="single"]').textContent = t.singleLookup;
        document.querySelector('[data-tab="bulk"]').textContent = t.bulkLookup;
        document.querySelector('[data-tab="speedtest"]').textContent = t.speedTest;
        document.querySelector('[data-tab="fingerprint"]').textContent = t.fingerprint;
        document.getElementById('download-label').textContent = t.download;
        document.getElementById('upload-label').textContent = t.upload;
        document.getElementById('your-ip-label').textContent = t.yourIP;
        document.getElementById('provider-label').textContent = t.provider;
        document.getElementById('test-node-label').textContent = t.testNode + ": ";
        document.getElementById('node-uk').textContent = t.nodeUK;
        document.getElementById('node-cn').textContent = t.nodeCN;
        document.getElementById('node-sg').textContent = t.nodeSG;
        document.getElementById('node-jp').textContent = t.nodeJP;
        document.getElementById('node-us').textContent = t.nodeUS;
        document.getElementById('fingerprint-title').textContent = t.fingerprintTitle;
        startSpeedTestBtn.textContent = startSpeedTestBtn.disabled ? t.testing : t.startTest;
        copyText.textContent = t.copy;
        copyBtn.title = t.copy;
        
        document.querySelectorAll('.delete-btn').forEach(btn => btn.title = t.delete);
    }

    // --- Theme Management ---
    function applyTheme(theme) {
        if (theme === 'dark') {
            document.documentElement.classList.add('dark');
            themeIcon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path></svg>`;
        } else {
            document.documentElement.classList.remove('dark');
            themeIcon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="5"></circle><line x1="12" y1="1" x2="12" y2="3"></line><line x1="12" y1="21" x2="12" y2="23"></line><line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line><line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line><line x1="1" y1="12" x2="3" y2="12"></line><line x1="21" y1="12" x2="23" y2="12"></line><line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line><line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line></svg>`;
        }
    }

    themeSwitch.addEventListener('click', () => {
        const currentTheme = document.documentElement.classList.contains('dark') ? 'light' : 'dark';
        localStorage.setItem('theme', currentTheme);
        applyTheme(currentTheme);
    });
    
    // --- Tab Switching ---
    tabBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            tabBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            tabContents.forEach(c => c.classList.remove('active'));
            document.getElementById(btn.dataset.tab + '-lookup').classList.add('active');
            
            if (btn.dataset.tab === 'speedtest') {
                prepareSpeedTest();
            }
            if (btn.dataset.tab === 'fingerprint') {
                generateBrowserFingerprint();
            }
        });
    });

    // --- Map Initialization ---
    let map = L.map('map').setView([23.5, 121], 7);
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);
    let marker;

    // --- History Management ---
    let history = JSON.parse(localStorage.getItem('ipHistory')) || [];
    
    function updateHistory(ip) {
        history = history.filter(item => item !== ip);
        history.unshift(ip);
        if (history.length > 10) history.pop();
        localStorage.setItem('ipHistory', JSON.stringify(history));
        renderHistory();
    }

    function deleteFromHistory(ipToDelete) {
        history = history.filter(item => item !== ipToDelete);
        localStorage.setItem('ipHistory', JSON.stringify(history));
        renderHistory();
    }

    function renderHistory() {
        historyList.innerHTML = '';
        const t = translations[currentLang];
        if (history.length === 0) {
             historyList.innerHTML = `<li style="cursor:default; justify-content:center; color: var(--subtle-text-color);">${t.resultDefaultText}</li>`;
             return;
        }

        history.forEach(ip => {
            const li = document.createElement('li');
            
            const ipText = document.createElement('span');
            ipText.className = 'history-ip-text';
            ipText.textContent = ip;
            ipText.addEventListener('click', () => {
                document.querySelector('[data-tab="single"]').click();
                ipInput.value = ip;
                queryIPWrapper();
            });
            
            const deleteBtn = document.createElement('span');
            deleteBtn.className = 'delete-btn';
            deleteBtn.innerHTML = '&times;';
            deleteBtn.title = t.delete;
            deleteBtn.addEventListener('click', (event) => {
                event.stopPropagation();
                deleteFromHistory(ip);
            });
            
            li.appendChild(ipText);
            li.appendChild(deleteBtn);
            historyList.appendChild(li);
        });
    }
    
    function countryCodeToFlag(isoCode) {
        if (!isoCode || isoCode.length !== 2) return '';
        return isoCode.toUpperCase().replace(/./g, char => 
            String.fromCodePoint(char.charCodeAt(0) + 127397)
        );
    }
    
    async function queryIP(ipToQuery) {
        const ip = ipToQuery || ipInput.value.trim() || ' '; 
        const api = apiSelector.value;
        
        try {
            const response = await fetch(`api.php?action=lookup&ip=${encodeURIComponent(ip)}&api=${api}`);
            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(errorText || `Request failed with status ${response.status}`);
            }
            const data = await response.json();
             if (data.error) {
                throw new Error(data.error);
            }
            return data;
        } catch (error) {
            console.error("Query Error:", error);
            throw error;
        }
    }

    async function generateRealisticAddress(lat, lon, city, country) {
         if (!lat || !lon) return { address: 'N/A', isReal: false };
         try {
            const response = await fetch(`api.php?action=geocode&lat=${lat}&lon=${lon}&lang=${currentLang}`);
            if (!response.ok) throw new Error('Geocoding request failed');
            
            const data = await response.json();
            if (data.error) throw new Error(data.error);

            if (data && data.display_name) {
                return { address: data.display_name, isReal: true };
            } else {
                return { address: generateSimpleAddress(city, country), isReal: false };
            }
        } catch (error) {
            console.error("Reverse geocoding failed, using fallback:", error);
            return { address: generateSimpleAddress(city, country), isReal: false };
        }
    }
    
    function generateSimpleAddress(city, country) {
        if (!city || !country) return 'N/A';
        const randomNum = Math.floor(Math.random() * 800) + 1;
        
        if (currentLang === 'zh') {
            const streetTypes_zh = ["路", "街", "大道", "巷", "里"];
            const streetName = ["中山", "中正", "民權", "民族", "民生"][Math.floor(Math.random() * 5)];
            const randomStreetType = streetTypes_zh[Math.floor(Math.random() * streetTypes_zh.length)];
            return `${city}${streetName}${randomStreetType}${randomNum}號`;
        } else {
            const streetTypes_en = ["St", "Rd", "Ave", "Ln", "Blvd"];
            const streetName = ["Main", "Oak", "Pine", "Maple", "Washington"][Math.floor(Math.random() * 5)];
            const randomStreetType = streetTypes_en[Math.floor(Math.random() * streetTypes_en.length)];
            return `${randomNum} ${streetName} ${randomStreetType}, ${city}`;
        }
    }


    async function handleSingleQuery() {
        loader.style.display = 'block';
        resultDiv.innerHTML = `<p style="color: var(--subtle-text-color);">${translations[currentLang].querying}</p>`;
        copyBtn.style.display = 'none';

        try {
            const data = await queryIP();
            await displayResult(data);
            updateMap(data.lat, data.lon, data.ip);
            updateHistory(data.ip);
        } catch (error) {
            resultDiv.innerHTML = `<p style="color: #ef4444;">${translations[currentLang].error} (${error.message})</p>`;
        } finally {
            loader.style.display = 'none';
        }
    }

    async function handleBulkQuery() {
        const ips = bulkIpInput.value.split('\n').map(ip => ip.trim()).filter(ip => ip);
        if (ips.length === 0) return;

        bulkLoader.style.display = 'block';
        bulkResultsContainer.innerHTML = '';

        const t = translations[currentLang];
        const table = document.createElement('table');
        table.id = 'bulk-results-table';
        table.innerHTML = `
            <thead>
                <tr>
                    <th>${t.ip}</th>
                    <th>${t.country}</th>
                    <th>${t.city}</th>
                    <th>${t.isp}</th>
                </tr>
            </thead>
            <tbody></tbody>
        `;
        bulkResultsContainer.appendChild(table);
        const tbody = table.querySelector('tbody');

        const promises = ips.map(ip => queryIP(ip));
        const results = await Promise.allSettled(promises);

        results.forEach((result, index) => {
            const ip = ips[index];
            const row = tbody.insertRow();
            if (result.status === 'fulfilled') {
                const data = result.value;
                 row.innerHTML = `
                    <td>${data.ip || 'N/A'}</td>
                    <td>${countryCodeToFlag(data.countryCode)} ${data.country || 'N/A'}</td>
                    <td>${data.city || 'N/A'}</td>
                    <td>${data.isp || 'N/A'}</td>
                `;
            } else {
                 row.innerHTML = `
                    <td>${ip}</td>
                    <td colspan="3" style="color: #ef4444;">${result.reason.message}</td>
                `;
            }
        });
        
        bulkLoader.style.display = 'none';
    }

    async function displayResult(data) {
        const t = translations[currentLang];
        const flag = countryCodeToFlag(data.countryCode);
        
        resultDiv.innerHTML = `
            <p><strong>${t.ip}:</strong> <span>${data.ip || 'N/A'}</span></p>
            <p><strong>${t.country}:</strong> <span>${flag} ${data.country || 'N/A'}</span></p>
            <p><strong>${t.region}:</strong> <span>${data.region || 'N/A'}</span></p>
            <p><strong>${t.city}:</strong> <span>${data.city || 'N/A'}</span></p>
            <p><strong>${t.postal}:</strong> <span>${data.postal || 'N/A'}</span></p>
            <p><strong>${t.isp}:</strong> <span>${data.isp || 'N/A'}</span></p>
            <p><strong>${t.asn}:</strong> <span>${data.asn || 'N/A'}</span></p>
            <p><strong>${t.timezone}:</strong> <span>${data.timezone || 'N/A'}</span></p>
            <p><strong>${t.lat}, ${t.lon}:</strong> <span>${data.lat || 'N/A'}, ${data.lon || 'N/A'}</span></p>
            <p id="generated-address-line"><strong>${t.generatedAddr}:</strong> <span><span class="spinner" style="width:16px; height:16px; display:inline-block; border-left-color:var(--secondary-color);"></span> ${t.generatingAddr}</span></p>
        `;
        
        copyBtn.style.display = 'block';

        const generatedData = await generateRealisticAddress(data.lat, data.lon, data.city, data.country);
        const addressLine = document.getElementById('generated-address-line');
        if (addressLine) {
            let displayText = generatedData.address;
            if (!generatedData.isReal) {
                displayText = `<span style="color: var(--subtle-text-color);">${t.estimated}</span> ${displayText}`;
            }
            addressLine.querySelector('span').innerHTML = displayText;
        }
    }

    function updateMap(lat, lon, ip) {
        if (lat && lon) {
            const coords = [lat, lon];
            map.flyTo(coords, 13);
            if (marker) {
                marker.setLatLng(coords);
            } else {
                marker = L.marker(coords).addTo(map);
            }
            marker.bindPopup(`<b>${ip}</b>`).openPopup();
        }
    }
    
    copyBtn.addEventListener('click', () => {
        const t = translations[currentLang];
        const textToCopy = Array.from(resultDiv.querySelectorAll('p')).map(p => p.innerText).join('\n');
        
        navigator.clipboard.writeText(textToCopy).then(() => {
            copyText.textContent = t.copied;
            setTimeout(() => { copyText.textContent = t.copy; }, 2000);
        });
    });

    // --- Speed Test Logic ---
    async function prepareSpeedTest() {
        try {
            const data = await queryIP(' ');
            speedTestIpEl.textContent = data.ip;
            speedTestIspEl.textContent = data.isp;
        } catch (e) {
            speedTestIpEl.textContent = 'N/A';
            speedTestIspEl.textContent = 'N/A';
        }
    }
    
    function animateValue(element, start, end, duration) {
        let startTimestamp = null;
        const step = (timestamp) => {
            if (!startTimestamp) startTimestamp = timestamp;
            const progress = Math.min((timestamp - startTimestamp) / duration, 1);
            element.innerHTML = (progress * (end - start) + start).toFixed(2);
            if (progress < 1) {
                window.requestAnimationFrame(step);
            }
        };
        window.requestAnimationFrame(step);
    }

    async function runSpeedTest() {
        startSpeedTestBtn.disabled = true;
        startSpeedTestBtn.textContent = translations[currentLang].testing;
        
        animateValue(downloadSpeedEl, parseFloat(downloadSpeedEl.textContent) || 0, 0, 300);
        animateValue(uploadSpeedEl, parseFloat(uploadSpeedEl.textContent) || 0, 0, 300);
        pingValueEl.textContent = '-';
        jitterValueEl.textContent = '-';
        
        const serverNode = document.getElementById('speed-test-server').value;

        try {
            const response = await fetch(`api.php?action=speedtest&server=${serverNode}`);
            const data = await response.json();
            if (data.error) throw new Error(data.error);
            
            animateValue(pingValueEl, 0, data.ping, 500);
            animateValue(jitterValueEl, 0, data.jitter, 500);
            animateValue(downloadSpeedEl, 0, data.download, 1000);
            animateValue(uploadSpeedEl, 0, data.upload, 1000);

        } catch (error) {
            console.error('Speed test failed:', error);
            alert(`${translations[currentLang].error} (${error.message})`);
        } finally {
            startSpeedTestBtn.disabled = false;
            startSpeedTestBtn.textContent = translations[currentLang].startTest;
        }
    }
    
    // --- Fingerprint Logic ---
    async function generateBrowserFingerprint() {
        const t = translations[currentLang];
        fingerprintDetailsDiv.innerHTML = `<div class="loader"><div class="spinner"></div></div>`;
        fingerprintHashDiv.innerHTML = '';

        // Use a timeout to avoid waiting forever for async operations
        setTimeout(async () => {
            const components = {};
            components.userAgent = navigator.userAgent;
            components.language = navigator.language;
            components.colorDepth = screen.colorDepth;
            components.resolution = `${screen.width}x${screen.height}`;
            components.timezone = new Date().getTimezoneOffset();
            components.platform = navigator.platform;
            components.plugins = Array.from(navigator.plugins).map(p => p.name).join(',');
            
            // Canvas Fingerprint
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            ctx.textBaseline = "top";
            ctx.font = "14px 'Arial'";
            ctx.textBaseline = "alphabetic";
            ctx.fillStyle = "#f60";
            ctx.fillRect(125, 1, 62, 20);
            ctx.fillStyle = "#069";
            ctx.fillText("Browser Fingerprint", 2, 15);
            components.canvas = canvas.toDataURL();

            const values = Object.values(components).join('~~~');
            
            const hash = await sha256(values);

            fingerprintDetailsDiv.innerHTML = `
                <p><strong>${t.fpUserAgent}:</strong> <span>${components.userAgent}</span></p>
                <p><strong>${t.fpResolution}:</strong> <span>${components.resolution}</span></p>
                <p><strong>${t.fpColorDepth}:</strong> <span>${components.colorDepth}</span></p>
                <p><strong>${t.fpLanguage}:</strong> <span>${components.language}</span></p>
                <p><strong>${t.fpPlatform}:</strong> <span>${components.platform}</span></p>
                <p><strong>${t.fpPlugins}:</strong> <span>${components.plugins.length > 0 ? components.plugins : 'N/A'}</span></p>
                <p><strong>${t.fpCanvas}:</strong> <span style="font-size: 12px;">${components.canvas.substring(0, 100)}...</span></p>
            `;

            fingerprintHashDiv.innerHTML = `<strong>${t.fingerprintHash}</strong><span>${hash}</span>`;
        }, 50);
    }
    
    async function sha256(message) {
        const msgBuffer = new TextEncoder().encode(message);
        const hashBuffer = await crypto.subtle.digest('SHA-256', msgBuffer);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    }
    
    // --- Event Listeners & Initialization ---
    function queryIPWrapper() {
        if (document.getElementById('single-lookup').classList.contains('active')) {
            handleSingleQuery();
        }
    }
    queryBtn.addEventListener('click', queryIPWrapper);
    bulkQueryBtn.addEventListener('click', handleBulkQuery);
    startSpeedTestBtn.addEventListener('click', runSpeedTest);

    showMyIpBtn.addEventListener('click', () => {
        ipInput.value = ''; // Clear input to query own IP
        handleSingleQuery();
    });

    ipInput.addEventListener('keyup', (event) => {
        if (event.key === 'Enter') {
            queryIPWrapper();
        }
    });
    langBtn.addEventListener('click', () => {
        setLanguage(currentLang === 'zh' ? 'en' : 'zh');
        // Re-generate fingerprint if that tab is active
        if (document.querySelector('[data-tab="fingerprint"]').classList.contains('active')) {
             generateBrowserFingerprint();
        }
    });

    window.addEventListener('DOMContentLoaded', () => {
        const savedTheme = localStorage.getItem('theme') || 'light';
        applyTheme(savedTheme);
        setLanguage('zh'); // Default to Chinese
        renderHistory();
        handleSingleQuery(); // Auto-query user's IP on load
    });

</script>
<script type="text/javascript">
    (function(c,l,a,r,i,t,y){
        c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
        t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
        y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
    })(window, document, "clarity", "script", "jtxjtkxsi8");
</script>
</body>
</html>
