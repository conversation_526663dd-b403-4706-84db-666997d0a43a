<?php
session_start();
require_once '../config/database.php';

// 检查登录状态
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: index.php');
    exit();
}

// 处理删除操作
if (isset($_POST['delete_record']) && isset($_POST['record_id'])) {
    try {
        $recordId = intval($_POST['record_id']);
        $stmt = $pdo->prepare("DELETE FROM analysis_records WHERE id = ?");
        $stmt->execute([$recordId]);
        
        if ($stmt->rowCount() > 0) {
            $success_msg = "记录删除成功！";
        } else {
            $error_msg = "记录不存在或删除失败！";
        }
    } catch (Exception $e) {
        $error_msg = "删除失败：" . $e->getMessage();
    }
}

// 处理批量删除
if (isset($_POST['batch_delete']) && isset($_POST['selected_records'])) {
    try {
        $selectedIds = array_map('intval', $_POST['selected_records']);
        $placeholders = str_repeat('?,', count($selectedIds) - 1) . '?';
        $stmt = $pdo->prepare("DELETE FROM analysis_records WHERE id IN ({$placeholders})");
        $stmt->execute($selectedIds);
        
        $deletedCount = $stmt->rowCount();
        $success_msg = "成功删除 {$deletedCount} 条记录！";
    } catch (Exception $e) {
        $error_msg = "批量删除失败：" . $e->getMessage();
    }
}

// 分页参数
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$perPage = 20;
$offset = ($page - 1) * $perPage;

// 搜索参数
$searchModel = $_GET['model'] ?? '';
$searchType = $_GET['type'] ?? '';
$searchDate = $_GET['date'] ?? '';

// 构建查询条件 - 兼容新旧字段
$whereConditions = [];
$params = [];

// 兼容AI模型字段
$modelField = 'COALESCE(ai_model, model_used)';
$typeField = 'COALESCE(analysis_type, "basic")';

if ($searchModel) {
    $whereConditions[] = "({$modelField} = ?)";
    $params[] = $searchModel;
}

if ($searchType) {
    $whereConditions[] = "({$typeField} = ?)";
    $params[] = $searchType;
}

if ($searchDate) {
    $whereConditions[] = "DATE(created_at) = ?";
    $params[] = $searchDate;
}

$whereClause = $whereConditions ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// 获取总记录数
$countSql = "SELECT COUNT(*) FROM analysis_records {$whereClause}";
$stmt = $pdo->prepare($countSql);
$stmt->execute($params);
$totalRecords = $stmt->fetchColumn();
$totalPages = ceil($totalRecords / $perPage);

// 获取记录列表 - 兼容新旧字段结构
$sql = "
    SELECT 
        *,
        COALESCE(user_name, name, '未提供') as display_name,
        COALESCE(birth_info, CONCAT(
            COALESCE(name, '未知'), ' ',
            COALESCE(gender, '未知'), ' ',
            COALESCE(birth_year, ''), '年',
            COALESCE(birth_month, ''), '月',
            COALESCE(birth_day, ''), '日 ',
            COALESCE(birth_hour, ''), ':',
            LPAD(COALESCE(birth_minute, 0), 2, '0')
        )) as birth_info_display,
        COALESCE(prompt_content, CONCAT(
            '用户问题：', COALESCE(question, '未提供问题'), '\n',
            '姓名：', COALESCE(COALESCE(user_name, name), '未知'), '\n',
            '性别：', COALESCE(gender, '未知')
        )) as prompt_display,
        COALESCE(ai_response, ai_analysis, '暂无AI回复') as response_display,
        {$modelField} as model_display,
        {$typeField} as type_display
    FROM analysis_records 
    {$whereClause} 
    ORDER BY created_at DESC 
    LIMIT {$perPage} OFFSET {$offset}
";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$records = $stmt->fetchAll();

// 获取统计信息 - 兼容新旧字段
$statsStmt = $pdo->query("
    SELECT 
        COUNT(*) as total_records,
        COUNT(DISTINCT {$modelField}) as unique_models,
        AVG(COALESCE(request_time, 0)) as avg_request_time,
        DATE(created_at) as record_date,
        COUNT(*) as daily_count
    FROM analysis_records 
    WHERE DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
    GROUP BY DATE(created_at)
    ORDER BY record_date DESC
");
$dailyStats = $statsStmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI分析记录管理 - AI八字命理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .record-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }
        .record-card:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .record-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px 8px 0 0;
        }
        .record-body {
            padding: 20px;
        }
        .prompt-text {
            max-height: 100px;
            overflow-y: auto;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-size: 0.9rem;
        }
        .response-text {
            max-height: 150px;
            overflow-y: auto;
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #28a745;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .model-badge {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
        }
        .search-form {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .debug-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            font-size: 12px;
            font-family: monospace;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="bi bi-gear"></i> AI八字命理系统 - 分析记录管理
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="debug_records.php">
                    <i class="bi bi-bug"></i> 调试信息
                </a>
                <a class="nav-link" href="fix_analysis_table.php">
                    <i class="bi bi-tools"></i> 数据修复
                </a>
                <a class="nav-link" href="index.php">
                    <i class="bi bi-arrow-left"></i> 返回管理后台
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <?php if (isset($success_msg)): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="bi bi-check-circle"></i> <?php echo $success_msg; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if (isset($error_msg)): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="bi bi-exclamation-triangle"></i> <?php echo $error_msg; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- 调试信息 -->
        <div class="debug-info">
            <strong>调试信息:</strong> 
            总记录数: <?php echo $totalRecords; ?> | 
            当前页: <?php echo $page; ?> | 
            查询到记录: <?php echo count($records); ?> | 
            搜索条件: <?php echo $whereClause ?: '无'; ?>
        </div>

        <!-- 统计信息 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card h-100">
                    <h6><i class="bi bi-files"></i> 总记录数</h6>
                    <h3><?php echo number_format($totalRecords); ?></h3>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card h-100">
                    <h6><i class="bi bi-robot"></i> 使用模型</h6>
                    <h3>
                        <?php
                        $modelsStmt = $pdo->query("SELECT COUNT(DISTINCT {$modelField}) FROM analysis_records WHERE {$modelField} IS NOT NULL AND {$modelField} != ''");
                        echo $modelsStmt->fetchColumn();
                        ?>
                    </h3>
                </div>
            </div>
<div class="col-md-3">
    <div class="stats-card h-100">
        <h6><i class="bi bi-speedometer2"></i> 平均耗时</h6>
        <h3>
            <?php
            // 修复平均耗时计算 - 更宽松的条件
            $avgStmt = $pdo->query("
                SELECT 
                    AVG(CAST(request_time AS DECIMAL(10,3))) as avg_time,
                    COUNT(CASE WHEN request_time IS NOT NULL AND request_time != '' AND CAST(request_time AS DECIMAL(10,3)) > 0 THEN 1 END) as valid_count,
                    MIN(CAST(request_time AS DECIMAL(10,3))) as min_time,
                    MAX(CAST(request_time AS DECIMAL(10,3))) as max_time
                FROM analysis_records 
                WHERE request_time IS NOT NULL 
                AND request_time != '' 
                AND request_time != '0'
                AND request_time REGEXP '^[0-9]+\.?[0-9]*$'
            ");
            $avgData = $avgStmt->fetch();
            
            if ($avgData['valid_count'] > 0 && $avgData['avg_time']) {
                echo '<span title="最小: ' . number_format($avgData['min_time'], 2) . 's, 最大: ' . number_format($avgData['max_time'], 2) . 's">' . 
                     number_format($avgData['avg_time'], 2) . 's</span>';
                echo '<br><small style="font-size: 0.7rem; opacity: 0.8;">(' . $avgData['valid_count'] . ' 条有效记录)</small>';
            } else {
                // 显示调试信息
                $debugStmt = $pdo->query("
                    SELECT 
                        COUNT(*) as total_records,
                        COUNT(CASE WHEN request_time IS NOT NULL THEN 1 END) as has_request_time,
                        COUNT(CASE WHEN request_time IS NOT NULL AND request_time != '' THEN 1 END) as non_empty,
                        COUNT(CASE WHEN request_time IS NOT NULL AND request_time != '' AND request_time != '0' THEN 1 END) as non_zero
                    FROM analysis_records
                ");
                $debugData = $debugStmt->fetch();
                
                echo '<small style="font-size: 0.6rem;">暂无数据</small>';

            }
            ?>
        </h3>
    </div>
</div>

            <div class="col-md-3">
                <div class="stats-card h-100">
                    <h6><i class="bi bi-calendar-today"></i> 今日分析</h6>
                    <h3>
                        <?php
                        $todayStmt = $pdo->query("SELECT COUNT(*) FROM analysis_records WHERE DATE(created_at) = CURDATE()");
                        echo $todayStmt->fetchColumn();
                        ?>
                    </h3>
                </div>
            </div>
        </div>

        <!-- 搜索和筛选 -->
        <div class="search-form">
            <form method="get" action="">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">AI模型</label>
                        <select name="model" class="form-select">
                            <option value="">所有模型</option>
                            <?php
                            $modelsStmt = $pdo->query("SELECT DISTINCT {$modelField} as model_name FROM analysis_records WHERE {$modelField} IS NOT NULL AND {$modelField} != '' ORDER BY model_name");
                            while ($model = $modelsStmt->fetch()): ?>
                                <option value="<?php echo htmlspecialchars($model['model_name']); ?>" 
                                        <?php echo $searchModel === $model['model_name'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($model['model_name'] ?: '未知'); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">分析类型</label>
                        <select name="type" class="form-select">
                            <option value="">所有类型</option>
                            <?php
                            $typesStmt = $pdo->query("SELECT DISTINCT {$typeField} as type_name FROM analysis_records WHERE {$typeField} IS NOT NULL AND {$typeField} != '' ORDER BY type_name");
                            while ($type = $typesStmt->fetch()): ?>
                                <option value="<?php echo htmlspecialchars($type['type_name']); ?>" 
                                        <?php echo $searchType === $type['type_name'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($type['type_name'] ?: '基础'); ?>
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">选择日期</label>
                        <input type="date" name="date" class="form-control" value="<?php echo htmlspecialchars($searchDate); ?>">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-flex gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> 搜索
                            </button>
                            <a href="analysis_records.php" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-clockwise"></i> 重置
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- 批量操作 -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5><i class="bi bi-list-ul"></i> 分析记录列表 (共 <?php echo $totalRecords; ?> 条)</h5>
            <div>
                <button type="button" class="btn btn-danger" onclick="batchDelete()" id="batchDeleteBtn" disabled>
                    <i class="bi bi-trash"></i> 批量删除
                </button>
                <button type="button" class="btn btn-outline-primary" onclick="selectAll()">
                    <i class="bi bi-check-all"></i> 全选
                </button>
                <a href="update_request_times.php" class="btn btn-outline-warning">
                    <i class="bi bi-clock"></i> 修复耗时
                </a>
            </div>
        </div>

        <!-- 记录列表 -->
        <?php if (empty($records)): ?>
        <div class="text-center py-5">
            <i class="bi bi-inbox display-1 text-muted"></i>
            <h4 class="text-muted mt-3">暂无分析记录</h4>
            <p class="text-muted">当有用户进行AI分析时，记录会显示在这里</p>
        </div>
        <?php else: ?>
        <form id="batchDeleteForm" method="post">
            <?php foreach ($records as $record): ?>
            <div class="record-card">
                <div class="record-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <input type="checkbox" class="form-check-input me-3 record-checkbox" 
                                   name="selected_records[]" value="<?php echo $record['id']; ?>">
                            <div>
                                <strong>#<?php echo $record['id']; ?></strong>
                                - <?php echo htmlspecialchars($record['display_name']); ?>
                            </div>
                        </div>
                        <div class="text-end">
                            <span class="model-badge"><?php echo htmlspecialchars($record['model_display'] ?: '未知'); ?></span>
                            <div style="font-size: 0.9rem; opacity: 0.9;">
                                <?php echo date('Y-m-d H:i:s', strtotime($record['created_at'])); ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="record-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="bi bi-chat-square-text"></i> 用户提示</h6>
                            <div class="prompt-text">
                                <?php echo nl2br(htmlspecialchars(substr($record['prompt_display'], 0, 300))); ?>
                                <?php if (strlen($record['prompt_display']) > 300): ?>
                                    <span class="text-muted">...</span>
                                <?php endif; ?>
                            </div>
                            
                            <div class="mt-3">
                                <h6><i class="bi bi-calendar3"></i> 生辰信息</h6>
                                <small class="text-muted"><?php echo htmlspecialchars($record['birth_info_display']); ?></small>
                            </div>
                            
                            <div class="mt-3">
                                <small class="text-muted">
                                    <i class="bi bi-geo-alt"></i> IP: <?php echo htmlspecialchars($record['ip_address'] ?: '未知'); ?>
                                    <?php if ($record['request_time']): ?>
                                        | <i class="bi bi-stopwatch"></i> <?php echo $record['request_time']; ?>s
                                    <?php endif; ?>
                                    <?php if ($record['session_id']): ?>
                                        | <i class="bi bi-fingerprint"></i> Session: <?php echo substr($record['session_id'], 0, 8); ?>...
                                    <?php endif; ?>
                                </small>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6><i class="bi bi-robot"></i> AI回复</h6>
                                <div>
                                    <button type="button" class="btn btn-sm btn-info me-2" 
                                            onclick="viewFullContent(<?php echo $record['id']; ?>)">
                                        <i class="bi bi-eye"></i> 查看详情
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                            onclick="deleteRecord(<?php echo $record['id']; ?>)">
                                        <i class="bi bi-trash"></i> 删除
                                    </button>
                                </div>
                            </div>
                            <div class="response-text">
                                <?php echo nl2br(htmlspecialchars(substr($record['response_display'], 0, 400))); ?>
                                <?php if (strlen($record['response_display']) > 400): ?>
                                    <span class="text-muted">...</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
            
            <input type="hidden" name="batch_delete" value="1">
        </form>

        <!-- 分页 -->
        <?php if ($totalPages > 1): ?>
        <nav>
            <ul class="pagination justify-content-center">
                <?php if ($page > 1): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=1<?php echo $searchModel ? '&model=' . urlencode($searchModel) : ''; ?><?php echo $searchType ? '&type=' . urlencode($searchType) : ''; ?><?php echo $searchDate ? '&date=' . urlencode($searchDate) : ''; ?>">首页</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo $searchModel ? '&model=' . urlencode($searchModel) : ''; ?><?php echo $searchType ? '&type=' . urlencode($searchType) : ''; ?><?php echo $searchDate ? '&date=' . urlencode($searchDate) : ''; ?>">上一页</a>
                </li>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                    <a class="page-link" href="?page=<?php echo $i; ?><?php echo $searchModel ? '&model=' . urlencode($searchModel) : ''; ?><?php echo $searchType ? '&type=' . urlencode($searchType) : ''; ?><?php echo $searchDate ? '&date=' . urlencode($searchDate) : ''; ?>"><?php echo $i; ?></a>
                </li>
                <?php endfor; ?>
                
                <?php if ($page < $totalPages): ?>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo $searchModel ? '&model=' . urlencode($searchModel) : ''; ?><?php echo $searchType ? '&type=' . urlencode($searchType) : ''; ?><?php echo $searchDate ? '&date=' . urlencode($searchDate) : ''; ?>">下一页</a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page=<?php echo $totalPages; ?><?php echo $searchModel ? '&model=' . urlencode($searchModel) : ''; ?><?php echo $searchType ? '&type=' . urlencode($searchType) : ''; ?><?php echo $searchDate ? '&date=' . urlencode($searchDate) : ''; ?>">末页</a>
                </li>
                <?php endif; ?>
            </ul>
        </nav>
        <?php endif; ?>
        <?php endif; ?>
    </div>

    <!-- 详情查看模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">分析记录详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="detailContent">
                    <!-- 详情内容将通过JS加载 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全选/取消全选
        function selectAll() {
            const checkboxes = document.querySelectorAll('.record-checkbox');
            const allChecked = Array.from(checkboxes).every(cb => cb.checked);
            
            checkboxes.forEach(cb => {
                cb.checked = !allChecked;
            });
            
            updateBatchDeleteButton();
        }
        
        // 更新批量删除按钮状态
        function updateBatchDeleteButton() {
            const checkedBoxes = document.querySelectorAll('.record-checkbox:checked');
            const batchDeleteBtn = document.getElementById('batchDeleteBtn');
            batchDeleteBtn.disabled = checkedBoxes.length === 0;
        }
        
        // 监听复选框变化
        document.addEventListener('change', function(e) {
            if (e.target.classList.contains('record-checkbox')) {
                updateBatchDeleteButton();
            }
        });
        
        // 批量删除
        function batchDelete() {
            const checkedBoxes = document.querySelectorAll('.record-checkbox:checked');
            if (checkedBoxes.length === 0) {
                alert('请先选择要删除的记录');
                return;
            }
            
            if (confirm(`确定要删除选中的 ${checkedBoxes.length} 条记录吗？此操作不可恢复！`)) {
                document.getElementById('batchDeleteForm').submit();
            }
        }
        
        // 删除单条记录
        function deleteRecord(id) {
            if (confirm('确定要删除这条记录吗？此操作不可恢复！')) {
                const form = document.createElement('form');
                form.method = 'post';
                form.innerHTML = `
                    <input type="hidden" name="delete_record" value="1">
                    <input type="hidden" name="record_id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        // 查看完整内容
        function viewFullContent(id) {
            // 显示加载状态
            document.getElementById('detailContent').innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2 text-muted">正在加载记录详情...</p>
                </div>
            `;
            
            // 先显示模态框
            const modal = new bootstrap.Modal(document.getElementById('detailModal'));
            modal.show();
            
            // 使用AJAX获取完整记录详情
            fetch(`get_record_detail.php?id=${id}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        document.getElementById('detailContent').innerHTML = data.html;
                    } else {
                        document.getElementById('detailContent').innerHTML = `
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle"></i> 
                                获取记录详情失败：${data.error}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('detailContent').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i> 
                            请求失败：${error.message}
                        </div>
                    `;
                });
        }
    </script>
</body>
</html>
