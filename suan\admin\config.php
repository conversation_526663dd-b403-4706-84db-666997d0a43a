<?php
session_start();
require_once '../config/config.php';

if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit;
}

// 处理配置更新
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $configs = [
        'deepseek_api_url', 'deepseek_api_key',
        'qwen_api_url', 'qwen_api_key',
        'default_model', 'site_title'
    ];
    
    foreach ($configs as $config) {
        if (isset($_POST[$config])) {
            Config::set($config, $_POST[$config]);
        }
    }
    
    $message = '配置已更新成功！';
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统配置 - 八字AI管理后台</title>
    <link rel="stylesheet" href="../assets/css/admin.css">
</head>
<body>
    <div class="admin-container">
        <header class="admin-header">
            <h1>系统配置</h1>
            <div class="admin-nav">
                <a href="index.php">首页</a>
                <a href="config.php" class="active">系统配置</a>
                <a href="login.php?logout=1">退出登录</a>
            </div>
        </header>
        
        <main class="admin-main">
            <?php if (isset($message)): ?>
            <div class="alert alert-success"><?php echo $message; ?></div>
            <?php endif; ?>
            
            <form method="POST" class="config-form">
                <div class="config-section">
                    <h2>DeepSeek API 配置</h2>
                    <div class="form-group">
                        <label>API地址：</label>
                        <input type="url" name="deepseek_api_url" value="<?php echo Config::get('deepseek_api_url'); ?>" required>
                    </div>
                    <div class="form-group">
                        <label>API密钥：</label>
                        <input type="text" name="deepseek_api_key" value="<?php echo Config::get('deepseek_api_key'); ?>" required>
                    </div>
                </div>
                
                <div class="config-section">
                    <h2>Qwen API 配置</h2>
                    <div class="form-group">
                        <label>API地址：</label>
                        <input type="url" name="qwen_api_url" value="<?php echo Config::get('qwen_api_url'); ?>" required>
                    </div>
                    <div class="form-group">
                        <label>API密钥：</label>
                        <input type="text" name="qwen_api_key" value="<?php echo Config::get('qwen_api_key'); ?>" required>
                    </div>
                </div>
                
                <div class="config-section">
                    <h2>系统设置</h2>
                    <div class="form-group">
                        <label>默认AI模型：</label>
                        <select name="default_model">
                            <option value="deepseek" <?php echo Config::get('default_model') === 'deepseek' ? 'selected' : ''; ?>>DeepSeek</option>
                            <option value="qwen" <?php echo Config::get('default_model') === 'qwen' ? 'selected' : ''; ?>>Qwen</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>网站标题：</label>
                        <input type="text" name="site_title" value="<?php echo Config::get('site_title'); ?>" required>
                    </div>
                </div>
                
                <button type="submit" class="save-btn">保存配置</button>
            </form>
        </main>
    </div>
</body>
</html>
