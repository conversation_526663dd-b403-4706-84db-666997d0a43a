<?php
session_start();
require_once '../config/database.php';

// 检查登录状态
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: index.php');
    exit();
}

echo "<h1>创建付费系统数据库表</h1>";

if (isset($_POST['create_tables'])) {
    try {
        // 检查 MySQL 版本
        $stmt = $pdo->query("SELECT VERSION() as version");
        $version = $stmt->fetch()['version'];
        $mysqlVersion = floatval($version);
        
        echo "<div style='color: blue;'>MySQL 版本: {$version}</div>";
        
        // 1. 用户表
        $sql1 = "
        CREATE TABLE IF NOT EXISTS users (
            id int(11) NOT NULL AUTO_INCREMENT,
            phone varchar(20) NOT NULL COMMENT '手机号（用作用户名）',
            password varchar(255) NOT NULL COMMENT '密码哈希',
            name varchar(100) DEFAULT NULL COMMENT '真实姓名',
            email varchar(100) DEFAULT NULL COMMENT '邮箱',
            avatar varchar(255) DEFAULT NULL COMMENT '头像URL',
            status enum('active','inactive','banned') DEFAULT 'active' COMMENT '账户状态',
            last_login_at datetime DEFAULT NULL COMMENT '最后登录时间',
            last_login_ip varchar(45) DEFAULT NULL COMMENT '最后登录IP',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY unique_phone (phone),
            UNIQUE KEY unique_email (email),
            KEY idx_status (status),
            KEY idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
        ";
        
        // 2. 用户使用记录表（兼容旧版本MySQL）
        if ($mysqlVersion >= 5.7) {
            // MySQL 5.7+ 支持生成列
            $sql2 = "
            CREATE TABLE IF NOT EXISTS user_usage (
                id int(11) NOT NULL AUTO_INCREMENT,
                user_id int(11) DEFAULT NULL COMMENT '用户ID，NULL表示游客',
                phone varchar(20) DEFAULT NULL COMMENT '手机号（用于游客识别）',
                ip_address varchar(45) NOT NULL COMMENT 'IP地址',
                user_agent varchar(500) DEFAULT NULL COMMENT '用户代理',
                analysis_type varchar(50) DEFAULT '八字分析' COMMENT '分析类型',
                used_at datetime DEFAULT CURRENT_TIMESTAMP COMMENT '使用时间',
                date_used date AS (DATE(used_at)) STORED COMMENT '使用日期',
                is_free tinyint(1) DEFAULT 1 COMMENT '是否免费使用',
                analysis_record_id int(11) DEFAULT NULL COMMENT '关联的分析记录ID',
                PRIMARY KEY (id),
                KEY idx_user_id (user_id),
                KEY idx_phone (phone),
                KEY idx_ip_date (ip_address, date_used),
                KEY idx_date_used (date_used),
                KEY idx_used_at (used_at),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户使用记录表';
            ";
        } else {
            // 旧版本MySQL使用普通字段
            $sql2 = "
            CREATE TABLE IF NOT EXISTS user_usage (
                id int(11) NOT NULL AUTO_INCREMENT,
                user_id int(11) DEFAULT NULL COMMENT '用户ID，NULL表示游客',
                phone varchar(20) DEFAULT NULL COMMENT '手机号（用于游客识别）',
                ip_address varchar(45) NOT NULL COMMENT 'IP地址',
                user_agent varchar(500) DEFAULT NULL COMMENT '用户代理',
                analysis_type varchar(50) DEFAULT '八字分析' COMMENT '分析类型',
                used_at datetime DEFAULT CURRENT_TIMESTAMP COMMENT '使用时间',
                date_used date NOT NULL COMMENT '使用日期',
                is_free tinyint(1) DEFAULT 1 COMMENT '是否免费使用',
                analysis_record_id int(11) DEFAULT NULL COMMENT '关联的分析记录ID',
                PRIMARY KEY (id),
                KEY idx_user_id (user_id),
                KEY idx_phone (phone),
                KEY idx_ip_date (ip_address, date_used),
                KEY idx_date_used (date_used),
                KEY idx_used_at (used_at),
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户使用记录表';
            ";
        }
        
        // 3. 套餐表
        $sql3 = "
        CREATE TABLE IF NOT EXISTS packages (
            id int(11) NOT NULL AUTO_INCREMENT,
            name varchar(100) NOT NULL COMMENT '套餐名称',
            description text COMMENT '套餐描述',
            price decimal(10,2) NOT NULL COMMENT '价格（元）',
            currency varchar(10) DEFAULT 'CNY' COMMENT '货币类型',
            analysis_count int(11) NOT NULL COMMENT '分析次数',
            valid_days int(11) NOT NULL COMMENT '有效天数',
            is_active tinyint(1) DEFAULT 1 COMMENT '是否启用',
            sort_order int(11) DEFAULT 0 COMMENT '排序顺序',
            features text DEFAULT NULL COMMENT '套餐特性（JSON格式）',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY idx_is_active (is_active),
            KEY idx_sort_order (sort_order)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='套餐表';
        ";
        
        // 4. 用户订单表
        $sql4 = "
        CREATE TABLE IF NOT EXISTS user_orders (
            id int(11) NOT NULL AUTO_INCREMENT,
            user_id int(11) NOT NULL COMMENT '用户ID',
            package_id int(11) NOT NULL COMMENT '套餐ID',
            order_no varchar(64) NOT NULL COMMENT '订单号',
            amount decimal(10,2) NOT NULL COMMENT '订单金额',
            currency varchar(10) DEFAULT 'CNY' COMMENT '货币类型',
            status enum('pending','paid','cancelled','refunded') DEFAULT 'pending' COMMENT '订单状态',
            payment_method varchar(50) DEFAULT NULL COMMENT '支付方式',
            payment_trade_no varchar(100) DEFAULT NULL COMMENT '第三方支付交易号',
            paid_at datetime DEFAULT NULL COMMENT '支付时间',
            expired_at datetime DEFAULT NULL COMMENT '过期时间',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY unique_order_no (order_no),
            KEY idx_user_id (user_id),
            KEY idx_package_id (package_id),
            KEY idx_status (status),
            KEY idx_created_at (created_at),
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (package_id) REFERENCES packages(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户订单表';
        ";
        
        // 5. 用户余额表
        $sql5 = "
        CREATE TABLE IF NOT EXISTS user_balance (
            id int(11) NOT NULL AUTO_INCREMENT,
            user_id int(11) NOT NULL COMMENT '用户ID',
            analysis_count int(11) DEFAULT 0 COMMENT '剩余分析次数',
            expires_at datetime DEFAULT NULL COMMENT '过期时间',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY unique_user_id (user_id),
            KEY idx_expires_at (expires_at),
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户余额表';
        ";
        
        // 6. 支付配置表
        $sql6 = "
        CREATE TABLE IF NOT EXISTS payment_config (
            id int(11) NOT NULL AUTO_INCREMENT,
            provider varchar(50) NOT NULL COMMENT '支付提供商',
            config_key varchar(100) NOT NULL COMMENT '配置键',
            config_value text COMMENT '配置值',
            is_active tinyint(1) DEFAULT 1 COMMENT '是否启用',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY unique_provider_key (provider, config_key),
            KEY idx_provider (provider),
            KEY idx_is_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付配置表';
        ";
        
        // 执行SQL语句
        $sqls = [$sql1, $sql2, $sql3, $sql4, $sql5, $sql6];
        $tableNames = ['users', 'user_usage', 'packages', 'user_orders', 'user_balance', 'payment_config'];
        
        foreach ($sqls as $index => $sql) {
            try {
                $pdo->exec($sql);
                echo "<div style='color: green;'>✅ 创建表 {$tableNames[$index]} 成功</div>";
            } catch (Exception $e) {
                echo "<div style='color: red;'>❌ 创建表 {$tableNames[$index]} 失败: " . $e->getMessage() . "</div>";
                throw $e;
            }
        }
        
        // 为旧版本MySQL创建触发器来维护date_used字段
        if ($mysqlVersion < 5.7) {
            try {
                $pdo->exec("DROP TRIGGER IF EXISTS tr_user_usage_insert");
                $pdo->exec("
                    CREATE TRIGGER tr_user_usage_insert 
                    BEFORE INSERT ON user_usage 
                    FOR EACH ROW 
                    SET NEW.date_used = DATE(NEW.used_at)
                ");
                
                $pdo->exec("DROP TRIGGER IF EXISTS tr_user_usage_update");
                $pdo->exec("
                    CREATE TRIGGER tr_user_usage_update 
                    BEFORE UPDATE ON user_usage 
                    FOR EACH ROW 
                    SET NEW.date_used = DATE(NEW.used_at)
                ");
                
                echo "<div style='color: green;'>✅ 创建触发器成功（兼容旧版本MySQL）</div>";
            } catch (Exception $e) {
                echo "<div style='color: orange;'>⚠️ 触发器创建失败，需要手动维护date_used字段: " . $e->getMessage() . "</div>";
            }
        }
        
        // 插入默认套餐数据
        $defaultPackages = [
            [
                'name' => '体验套餐',
                'description' => '3次分析机会，体验AI八字分析的强大功能',
                'price' => 9.90,
                'analysis_count' => 3,
                'valid_days' => 7,
                'features' => '["基础分析", "7天有效期"]',
                'sort_order' => 1
            ],
            [
                'name' => '标准套餐',
                'description' => '10次分析机会，适合个人深度了解命理',
                'price' => 29.90,
                'analysis_count' => 10,
                'valid_days' => 30,
                'features' => '["完整分析", "30天有效期", "优先客服"]',
                'sort_order' => 2
            ],
            [
                'name' => '专业套餐',
                'description' => '30次分析机会，专业命理师级别的详细分析',
                'price' => 79.90,
                'analysis_count' => 30,
                'valid_days' => 90,
                'features' => '["专业分析", "90天有效期", "专属客服", "运势提醒"]',
                'sort_order' => 3
            ],
            [
                'name' => '至尊套餐',
                'description' => '100次分析机会，一年有效期，尊享服务',
                'price' => 199.90,
                'analysis_count' => 100,
                'valid_days' => 365,
                'features' => '["顶级分析", "365天有效期", "一对一服务", "定制报告"]',
                'sort_order' => 4
            ]
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO packages (name, description, price, analysis_count, valid_days, features, sort_order) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        foreach ($defaultPackages as $package) {
            $stmt->execute([
                $package['name'],
                $package['description'],
                $package['price'],
                $package['analysis_count'],
                $package['valid_days'],
                $package['features'],
                $package['sort_order']
            ]);
        }
        
        echo "<div style='color: green; margin-top: 20px;'>✅ 插入默认套餐数据成功</div>";
        
        // 插入默认支付配置
        $defaultConfigs = [
            ['provider' => 'alipay', 'config_key' => 'app_id', 'config_value' => ''],
            ['provider' => 'alipay', 'config_key' => 'private_key', 'config_value' => ''],
            ['provider' => 'alipay', 'config_key' => 'public_key', 'config_value' => ''],
            ['provider' => 'alipay', 'config_key' => 'notify_url', 'config_value' => ''],
            ['provider' => 'alipay', 'config_key' => 'return_url', 'config_value' => ''],
            ['provider' => 'wxpay', 'config_key' => 'app_id', 'config_value' => ''],
            ['provider' => 'wxpay', 'config_key' => 'mch_id', 'config_value' => ''],
            ['provider' => 'wxpay', 'config_key' => 'api_key', 'config_value' => ''],
            ['provider' => 'wxpay', 'config_key' => 'notify_url', 'config_value' => '']
        ];
        
        $stmt = $pdo->prepare("
            INSERT IGNORE INTO payment_config (provider, config_key, config_value) 
            VALUES (?, ?, ?)
        ");
        
        foreach ($defaultConfigs as $config) {
            $stmt->execute([$config['provider'], $config['config_key'], $config['config_value']]);
        }
        
        echo "<div style='color: green;'>✅ 插入默认支付配置成功</div>";
        
        echo "<div style='color: blue; margin-top: 20px; padding: 15px; background: #e3f2fd; border-radius: 8px;'>";
        echo "<strong>🎉 付费系统数据库创建完成！</strong><br>";
        echo "- 6个数据库表已创建<br>";
        echo "- 4个默认套餐已添加<br>";
        echo "- 支付配置模板已创建<br>";
        if ($mysqlVersion < 5.7) {
            echo "- 已为旧版本MySQL创建兼容性触发器<br>";
        }
        echo "<br>下一步：配置支付接口参数";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='color: red; padding: 15px; background: #ffebee; border-radius: 8px;'>";
        echo "❌ 创建失败：" . $e->getMessage();
        echo "<br><br><strong>解决方案：</strong>";
        echo "<br>1. 检查MySQL版本是否支持";
        echo "<br>2. 确保数据库用户有足够权限";
        echo "<br>3. 检查表名是否冲突";
        echo "</div>";
    }
}

// 检查表是否存在
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    $tablesExist = $stmt->rowCount() > 0;
    
    if ($tablesExist) {
        // 检查已存在的表
        $existingTables = [];
        $checkTables = ['users', 'user_usage', 'packages', 'user_orders', 'user_balance', 'payment_config'];
        
        foreach ($checkTables as $table) {
            $stmt = $pdo->query("SHOW TABLES LIKE '{$table}'");
            if ($stmt->rowCount() > 0) {
                $existingTables[] = $table;
            }
        }
        
        echo "<div style='color: blue; padding: 15px; background: #cce7ff; border-radius: 8px; margin: 15px 0;'>";
        echo "ℹ️ 以下表已存在：" . implode(', ', $existingTables);
        echo "</div>";
    }
} catch (Exception $e) {
    $tablesExist = false;
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建付费系统表</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; }
        button { padding: 12px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; }
        button:hover { background: #0056b3; }
        .info-box { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #007bff; }
        .warning-box { background: #fff3cd; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 4px solid #ffc107; }
    </style>
</head>
<body>

<div class="info-box">
    <h3>📊 付费系统数据库表结构</h3>
    <ul>
        <li><strong>users</strong> - 用户基础信息表</li>
        <li><strong>user_usage</strong> - 用户使用记录表（追踪每日使用情况）</li>
        <li><strong>packages</strong> - 套餐配置表</li>
        <li><strong>user_orders</strong> - 用户订单表</li>
        <li><strong>user_balance</strong> - 用户余额表（剩余分析次数）</li>
        <li><strong>payment_config</strong> - 支付配置表</li>
    </ul>
    <p><strong>功能特性：</strong></p>
    <ul>
        <li>✅ 游客每日限制1次免费使用</li>
        <li>✅ 注册用户管理和认证</li>
        <li>✅ 灵活的套餐配置系统</li>
        <li>✅ 完整的订单和支付流程</li>
        <li>✅ 支付宝/微信支付集成准备</li>
        <li>✅ 兼容旧版本MySQL</li>
    </ul>
</div>

<div class="warning-box">
    <strong>⚠️ 注意事项：</strong><br>
    - 此操作会创建新的数据库表<br>
    - 如果表已存在，将跳过创建<br>
    - 建议在创建前备份数据库<br>
    - 确保数据库用户有CREATE、ALTER权限
</div>

<?php if (!$tablesExist): ?>
<form method="post">
    <p>点击下方按钮创建付费系统所需的数据库表：</p>
    <button type="submit" name="create_tables">创建付费系统数据库表</button>
</form>
<?php else: ?>
<div style="color: green; padding: 15px; background: #d4edda; border-radius: 8px;">
    ✅ 付费系统数据库表已存在！<br>
    <a href="index.php?page=users">管理用户</a> | 
    <a href="index.php?page=packages">管理套餐</a> | 
    <a href="index.php?page=payment">支付配置</a>
</div>

<div style="margin-top: 20px;">
    <h4>手动创建表（如果自动创建失败）：</h4>
    <p>如果自动创建失败，您可以手动执行SQL语句。</p>
    <button onclick="showSQL()">显示SQL语句</button>
    <div id="sqlContent" style="display: none; margin-top: 15px; background: #f1f1f1; padding: 15px; border-radius: 5px;">
        <h5>复制以下SQL到数据库中执行：</h5>
        <textarea style="width: 100%; height: 300px; font-family: monospace; font-size: 12px;">
-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id int(11) NOT NULL AUTO_INCREMENT,
    phone varchar(20) NOT NULL COMMENT '手机号',
    password varchar(255) NOT NULL COMMENT '密码哈希',
    name varchar(100) DEFAULT NULL COMMENT '真实姓名',
    email varchar(100) DEFAULT NULL COMMENT '邮箱',
    status enum('active','inactive','banned') DEFAULT 'active',
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY unique_phone (phone)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 其他表的SQL语句...
        </textarea>
    </div>
</div>

<script>
function showSQL() {
    const content = document.getElementById('sqlContent');
    content.style.display = content.style.display === 'none' ? 'block' : 'none';
}
</script>
<?php endif; ?>

<div style="margin-top: 30px;">
    <a href="index.php">返回管理后台</a>
</div>

</body>
</html>
