<?php
session_start();
require_once '../config/database.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 检查登录状态
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    echo json_encode([
        'success' => false,
        'error' => '未登录或登录已过期'
    ]);
    exit();
}

// 检查参数
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo json_encode([
        'success' => false,
        'error' => '无效的记录ID'
    ]);
    exit();
}

$recordId = intval($_GET['id']);

try {
    // 查询记录详情 - 兼容新旧字段结构
    $stmt = $pdo->prepare("
        SELECT 
            *,
            COALESCE(user_name, name, '未提供') as display_name,
            COALESCE(birth_info, CONCAT(
                COALESCE(name, '未知'), ' ',
                COALESCE(gender, '未知'), ' ',
                COALESCE(birth_year, ''), '年',
                COALESCE(birth_month, ''), '月',
                COALESCE(birth_day, ''), '日 ',
                COALESCE(birth_hour, ''), ':',
                LPAD(COALESCE(birth_minute, 0), 2, '0')
            )) as birth_info_display,
            COALESCE(prompt_content, CONCAT(
                '用户问题：', COALESCE(question, '未提供问题'), '\n\n',
                '姓名：', COALESCE(COALESCE(user_name, name), '未知'), '\n',
                '性别：', COALESCE(gender, '未知'), '\n',
                '出生年份：', COALESCE(birth_year, '未知'), '\n',
                '出生月份：', COALESCE(birth_month, '未知'), '\n',
                '出生日期：', COALESCE(birth_day, '未知'), '\n',
                '出生时辰：', COALESCE(birth_hour, '未知'), ':', LPAD(COALESCE(birth_minute, 0), 2, '0'), '\n\n',
                '请进行专业的八字命理分析。'
            )) as prompt_display,
            COALESCE(ai_response, ai_analysis, '暂无AI回复') as response_display,
            COALESCE(ai_model, model_used, '未知') as model_display,
            COALESCE(analysis_type, 'basic') as type_display
        FROM analysis_records 
        WHERE id = ?
    ");
    
    $stmt->execute([$recordId]);
    $record = $stmt->fetch();
    
    if (!$record) {
        echo json_encode([
            'success' => false,
            'error' => '记录不存在'
        ]);
        exit();
    }
    
    // 生成详情HTML
    $html = generateDetailHtml($record);
    
    echo json_encode([
        'success' => true,
        'html' => $html,
        'record' => $record
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => '查询失败：' . $e->getMessage()
    ]);
}

function generateDetailHtml($record) {
    $html = '<div class="record-detail">';
    
    // 基本信息
    $html .= '<div class="row mb-4">';
    $html .= '<div class="col-md-6">';
    $html .= '<h6><i class="bi bi-info-circle"></i> 基本信息</h6>';
    $html .= '<table class="table table-sm table-borderless">';
    $html .= '<tr><td><strong>记录ID:</strong></td><td>#' . htmlspecialchars($record['id']) . '</td></tr>';
    $html .= '<tr><td><strong>用户姓名:</strong></td><td>' . htmlspecialchars($record['display_name']) . '</td></tr>';
    $html .= '<tr><td><strong>AI模型:</strong></td><td>' . htmlspecialchars($record['model_display']) . '</td></tr>';
    $html .= '<tr><td><strong>分析类型:</strong></td><td>' . htmlspecialchars($record['type_display']) . '</td></tr>';
    $html .= '<tr><td><strong>IP地址:</strong></td><td>' . htmlspecialchars($record['ip_address'] ?: '未知') . '</td></tr>';
    if ($record['session_id']) {
        $html .= '<tr><td><strong>会话ID:</strong></td><td>' . htmlspecialchars($record['session_id']) . '</td></tr>';
    }
    $html .= '<tr><td><strong>创建时间:</strong></td><td>' . htmlspecialchars($record['created_at']) . '</td></tr>';
    if ($record['request_time']) {
        $html .= '<tr><td><strong>请求耗时:</strong></td><td>' . htmlspecialchars($record['request_time']) . '秒</td></tr>';
    }
    $html .= '</table>';
    $html .= '</div>';
    
    // 出生信息
    $html .= '<div class="col-md-6">';
    $html .= '<h6><i class="bi bi-calendar3"></i> 出生信息</h6>';
    $html .= '<div class="bg-light p-3 rounded">';
    $html .= '<p class="mb-1"><strong>完整信息:</strong></p>';
    $html .= '<p class="mb-0">' . htmlspecialchars($record['birth_info_display']) . '</p>';
    
    // 如果有详细的出生信息字段，也显示出来
    if ($record['birth_year'] || $record['name'] || $record['gender']) {
        $html .= '<hr class="my-2">';
        $html .= '<small class="text-muted">';
        if ($record['name']) $html .= '<strong>姓名:</strong> ' . htmlspecialchars($record['name']) . ' ';
        if ($record['gender']) $html .= '<strong>性别:</strong> ' . htmlspecialchars($record['gender']) . ' ';
        if ($record['birth_year']) {
            $html .= '<br><strong>详细生辰:</strong> ' . 
                     htmlspecialchars($record['birth_year']) . '年' .
                     htmlspecialchars($record['birth_month'] ?: '?') . '月' .
                     htmlspecialchars($record['birth_day'] ?: '?') . '日 ' .
                     htmlspecialchars($record['birth_hour'] ?: '?') . ':' .
                     str_pad($record['birth_minute'] ?: 0, 2, '0', STR_PAD_LEFT);
        }
        $html .= '</small>';
    }
    $html .= '</div>';
    $html .= '</div>';
    $html .= '</div>';
    
    // 用户问题/提示词
    $html .= '<div class="mb-4">';
    $html .= '<h6><i class="bi bi-chat-quote"></i> 完整提示词</h6>';
    $html .= '<div style="max-height: 300px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6;">';
    $html .= '<pre style="white-space: pre-wrap; margin: 0; font-family: inherit; font-size: 14px;">' . htmlspecialchars($record['prompt_display']) . '</pre>';
    $html .= '</div>';
    
    // 如果有原始问题字段，也显示
    if ($record['question'] && $record['question'] !== $record['prompt_display']) {
        $html .= '<div class="mt-2">';
        $html .= '<small class="text-muted"><strong>原始问题:</strong> ' . htmlspecialchars($record['question']) . '</small>';
        $html .= '</div>';
    }
    $html .= '</div>';
    
    // AI回复
    $html .= '<div class="mb-4">';
    $html .= '<h6><i class="bi bi-robot"></i> 完整AI回复</h6>';
    $html .= '<div style="max-height: 400px; overflow-y: auto; background: #e8f5e8; padding: 15px; border-radius: 8px; border-left: 4px solid #28a745;">';
    $html .= '<div style="white-space: pre-wrap; font-size: 14px; line-height: 1.6;">' . htmlspecialchars($record['response_display']) . '</div>';
    $html .= '</div>';
    $html .= '</div>';
    
    // 技术信息
    if ($record['bazi_result'] || $record['usage_info'] || $record['user_agent']) {
        $html .= '<div class="mb-3">';
        $html .= '<h6><i class="bi bi-gear"></i> 技术信息</h6>';
        $html .= '<div class="accordion" id="techAccordion">';
        
        // 八字结果
        if ($record['bazi_result']) {
            $html .= '<div class="accordion-item">';
            $html .= '<h2 class="accordion-header">';
            $html .= '<button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#baziCollapse">';
            $html .= '八字计算结果';
            $html .= '</button>';
            $html .= '</h2>';
            $html .= '<div id="baziCollapse" class="accordion-collapse collapse" data-bs-parent="#techAccordion">';
            $html .= '<div class="accordion-body">';
            $html .= '<pre style="font-size: 12px; max-height: 200px; overflow-y: auto;">' . htmlspecialchars($record['bazi_result']) . '</pre>';
            $html .= '</div>';
            $html .= '</div>';
            $html .= '</div>';
        }
        
        // 使用信息
        if ($record['usage_info']) {
            $html .= '<div class="accordion-item">';
            $html .= '<h2 class="accordion-header">';
            $html .= '<button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#usageCollapse">';
            $html .= 'API使用信息';
            $html .= '</button>';
            $html .= '</h2>';
            $html .= '<div id="usageCollapse" class="accordion-collapse collapse" data-bs-parent="#techAccordion">';
            $html .= '<div class="accordion-body">';
            $html .= '<pre style="font-size: 12px;">' . htmlspecialchars($record['usage_info']) . '</pre>';
            $html .= '</div>';
            $html .= '</div>';
            $html .= '</div>';
        }
        
        // 用户代理
        if ($record['user_agent']) {
            $html .= '<div class="accordion-item">';
            $html .= '<h2 class="accordion-header">';
            $html .= '<button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#agentCollapse">';
            $html .= '用户代理信息';
            $html .= '</button>';
            $html .= '</h2>';
            $html .= '<div id="agentCollapse" class="accordion-collapse collapse" data-bs-parent="#techAccordion">';
            $html .= '<div class="accordion-body">';
            $html .= '<small style="word-break: break-all;">' . htmlspecialchars($record['user_agent']) . '</small>';
            $html .= '</div>';
            $html .= '</div>';
            $html .= '</div>';
        }
        
        $html .= '</div>';
        $html .= '</div>';
    }
    
    $html .= '</div>';
    
    return $html;
}
?>
