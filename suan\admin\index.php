<?php
session_start();

// 添加缺失的函数定义
function escape($data) {
    return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
}

// 检查管理员登录状态
function checkLogin() {
    if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
        header('Location: login.php');
        exit();
    }
}

// 检查数据库配置
if (!file_exists('../config/database.php')) {
    die('数据库配置文件不存在，请先运行安装程序');
}

require_once '../config/database.php';

// 初始化管理员账户表
try {
    $pdo->exec("CREATE TABLE IF NOT EXISTS admin_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    
    // 检查是否存在默认管理员账户
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM admin_users WHERE username = 'admin'");
    $stmt->execute();
    if ($stmt->fetchColumn() == 0) {
        // 创建默认管理员账户
        $stmt = $pdo->prepare("INSERT INTO admin_users (username, password_hash) VALUES ('admin', ?)");
        $stmt->execute([password_hash('password', PASSWORD_DEFAULT)]);
    }
} catch (Exception $e) {
    // 如果表创建失败，使用硬编码验证
    error_log("Admin table creation failed: " . $e->getMessage());
}

// 处理登录 - 修复版本
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    $username = trim($_POST['username'] ?? '');
    $password = trim($_POST['password'] ?? '');
    
    if (empty($username) || empty($password)) {
        $error = '用户名和密码不能为空';
    } else {
        $loginSuccess = false;
        $userId = null;
        
        try {
            // 首先尝试数据库验证
            $stmt = $pdo->prepare("SELECT id, username, password_hash FROM admin_users WHERE username = ?");
            $stmt->execute([$username]);
            $user = $stmt->fetch();
            
            if ($user && password_verify($password, $user['password_hash'])) {
                // 数据库验证成功
                $loginSuccess = true;
                $userId = $user['id'];
                error_log("Database login successful for user: {$username}");
            }
        } catch (Exception $e) {
            error_log("Database login failed: " . $e->getMessage());
        }
        
        // 如果数据库验证失败，尝试硬编码验证
        if (!$loginSuccess && $username === 'admin' && $password === 'password') {
            $loginSuccess = true;
            $userId = 1;
            error_log("Hardcoded login successful for user: {$username}");
            
            // 尝试在数据库中创建记录
            try {
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("INSERT IGNORE INTO admin_users (username, password_hash) VALUES (?, ?)");
                $stmt->execute([$username, $hashedPassword]);
                if ($pdo->lastInsertId()) {
                    $userId = $pdo->lastInsertId();
                    error_log("Created database record for user: {$username}");
                }
            } catch (Exception $e) {
                error_log("Failed to create admin record: " . $e->getMessage());
            }
        }
        
        if ($loginSuccess) {
            // 确保正确设置所有session变量
            $_SESSION['admin_logged_in'] = true;
            $_SESSION['admin_username'] = $username;  // 重要：确保用户名被设置
            $_SESSION['admin_id'] = $userId;
            
            error_log("Login successful - Session set: username={$username}, id={$userId}");
            
            // 重定向到管理页面
            header('Location: index.php');
            exit();
        } else {
            $error = '用户名或密码错误';
            error_log("Login failed for user: {$username}");
        }
    }
}


// 处理登出
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: login.php');
    exit();
}

// 如果未登录，显示登录页面
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    include 'login.php';
    exit();
}

// 处理密码修改
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['change_password'])) {
    $currentPassword = $_POST['current_password'] ?? '';
    $newPassword = $_POST['new_password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    
    if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
        $error = '所有密码字段都不能为空';
    } elseif ($newPassword !== $confirmPassword) {
        $error = '新密码和确认密码不匹配';
    } elseif (strlen($newPassword) < 6) {
        $error = '新密码长度至少为6位';
    } else {
        try {
            $currentUsername = $_SESSION['admin_username'];
            $stmt = $pdo->prepare("SELECT id, password_hash FROM admin_users WHERE username = ?");
            $stmt->execute([$currentUsername]);
            $user = $stmt->fetch();
            
            if (!$user) {
                // 数据库中没有用户记录，尝试硬编码验证
                if ($currentUsername === 'admin' && $currentPassword === 'password') {
                    // 创建数据库记录并更新密码
                    $newHash = password_hash($newPassword, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("INSERT INTO admin_users (username, password_hash) VALUES (?, ?) ON DUPLICATE KEY UPDATE password_hash = ?");
                    $stmt->execute(['admin', $newHash, $newHash]);
                    $_SESSION['admin_id'] = $pdo->lastInsertId() ?: 1;
                    $success = '密码修改成功！数据库记录已创建。';
                } else {
                    $error = '当前密码不正确。如果是首次修改密码，请输入默认密码：password';
                }
            } else {
                // 验证数据库中的密码
                if (password_verify($currentPassword, $user['password_hash'])) {
                    // 更新密码
                    $newHash = password_hash($newPassword, PASSWORD_DEFAULT);
                    $stmt = $pdo->prepare("UPDATE admin_users SET password_hash = ?, updated_at = NOW() WHERE id = ?");
                    $stmt->execute([$newHash, $user['id']]);
                    $success = '密码修改成功！';
                } else {
                    $error = '当前密码不正确';
                }
            }
        } catch (Exception $e) {
            $error = '密码修改失败: ' . $e->getMessage();
        }
    }
}

// 处理配置更新
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_config'])) {
    try {
        $configs = [
            'deepseek_api_url' => trim($_POST['deepseek_api_url']),
            'deepseek_api_key' => trim($_POST['deepseek_api_key']),
            'deepseek_model_name' => trim($_POST['deepseek_model_name']),
            'qwen_api_url' => trim($_POST['qwen_api_url']),
            'qwen_api_key' => trim($_POST['qwen_api_key']),
            'qwen_model_name' => trim($_POST['qwen_model_name']),
            'default_model' => trim($_POST['default_model']),
            'api_timeout' => intval($_POST['api_timeout']),
            'max_tokens' => intval($_POST['max_tokens']),
            'temperature' => floatval($_POST['temperature'])
        ];
        
        foreach ($configs as $key => $value) {
            $stmt = $pdo->prepare("
                INSERT INTO system_config (config_key, config_value, updated_at) 
                VALUES (?, ?, NOW()) 
                ON DUPLICATE KEY UPDATE config_value = ?, updated_at = NOW()
            ");
            $stmt->execute([$key, $value, $value]);
        }
        
        $success = '配置已成功更新！';
    } catch (Exception $e) {
        $error = '配置更新失败: ' . $e->getMessage();
    }
}

// 获取当前配置
try {
    $stmt = $pdo->prepare("SELECT config_key, config_value FROM system_config");
    $stmt->execute();
    $currentConfig = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
} catch (Exception $e) {
    $currentConfig = [];
}

// 设置默认值
$defaultConfig = [
    'deepseek_api_url' => '',
    'deepseek_api_key' => '',
    'deepseek_model_name' => 'deepseek-chat',
    'qwen_api_url' => '',
    'qwen_api_key' => '',
    'qwen_model_name' => 'qwen-turbo',
    'default_model' => 'deepseek',
    'api_timeout' => 60,
    'max_tokens' => 2000,
    'temperature' => 0.7
];

foreach ($defaultConfig as $key => $value) {
    if (!isset($currentConfig[$key])) {
        $currentConfig[$key] = $value;
    }
}

// 获取分析记录统计
try {
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM analysis_records");
    $stmt->execute();
    $totalRecords = $stmt->fetch()['total'];
    
    $stmt = $pdo->prepare("SELECT COUNT(*) as today FROM analysis_records WHERE DATE(created_at) = CURDATE()");
    $stmt->execute();
    $todayRecords = $stmt->fetch()['today'];
    
    $stmt = $pdo->prepare("SELECT model_used, COUNT(*) as count FROM analysis_records WHERE model_used IS NOT NULL GROUP BY model_used");
    $stmt->execute();
    $modelStats = $stmt->fetchAll();
} catch (Exception $e) {
    $totalRecords = 0;
    $todayRecords = 0;
    $modelStats = [];
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - AI八字命理分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .nav-link {
            color: rgba(255,255,255,0.8);
            border-radius: 8px;
            margin: 2px 0;
        }
        .nav-link:hover, .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.1);
        }
        .card-custom {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .model-config {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            transition: border-color 0.3s;
        }
        .model-config:hover {
            border-color: #667eea;
        }
        .password-strength {
            height: 5px;
            border-radius: 3px;
            transition: all 0.3s;
        }
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
            font-size: 12px;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse px-3 py-4">
                <div class="position-sticky">
                    <h4 class="text-white mb-4">
                        <i class="bi bi-gear"></i> 管理后台
                    </h4>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="#dashboard" onclick="showTab('dashboard')">
                                <i class="bi bi-speedometer2"></i> 控制台
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#config" onclick="showTab('config')">
                                <i class="bi bi-gear"></i> API配置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#password" onclick="showTab('password')">
                                <i class="bi bi-key"></i> 修改密码
                            </a>
                        </li>
                        <li class="nav-item">
                           <a class="nav-link" href="analysis_records.php">
                           <i class="bi bi-list-ul"></i>Ai分析记录</a></li>

                        <li class="nav-item">
                            <a class="nav-link" href="#records" onclick="showTab('records')">
                                <i class="bi bi-list"></i> 分析记录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#test" onclick="showTab('test')">
                                <i class="bi bi-check-circle"></i> API测试
                            </a>
                        </li>
                        <li class="nav-item mt-4">
                            <a class="nav-link text-warning" href="?logout=1">
                                <i class="bi bi-box-arrow-right"></i> 退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4">
                <!-- 顶部警告/成功消息 -->
                <?php if (isset($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle"></i> <?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>
                
                <?php if (isset($success)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle"></i> <?php echo escape($success); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php endif; ?>

                <!-- 控制台页面 -->
                <div id="dashboard" class="tab-content">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
                        <h1 class="h2">控制台</h1>
                        <div class="text-muted">
                            欢迎回来，<?php echo escape($_SESSION['admin_username'] ?? 'Admin'); ?>
                        </div>
                    </div>

                    <!-- 统计卡片 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card card-custom stat-card">
                                <div class="card-body text-center">
                                    <h2 class="fw-bold"><?php echo $totalRecords; ?></h2>
                                    <p class="mb-0">总分析次数</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card card-custom stat-card">
                                <div class="card-body text-center">
                                    <h2 class="fw-bold"><?php echo $todayRecords; ?></h2>
                                    <p class="mb-0">今日分析次数</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card card-custom bg-success">
                                <div class="card-body text-center text-white">
                                    <h2 class="fw-bold">
                                        <i class="bi bi-check-circle"></i>
                                    </h2>
                                    <p class="mb-0">系统状态</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card card-custom bg-info">
                                <div class="card-body text-center text-white">
                                    <h2 class="fw-bold">v1.0</h2>
                                    <p class="mb-0">系统版本</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 模型使用统计 -->
                    <?php if (!empty($modelStats)): ?>
                    <div class="card card-custom mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">AI模型使用统计</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php foreach ($modelStats as $stat): ?>
                                <div class="col-md-4 mb-2">
                                    <div class="d-flex justify-content-between">
                                        <span><?php echo escape($stat['model_used']); ?></span>
                                        <span class="badge bg-primary"><?php echo $stat['count']; ?>次</span>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- 快速操作 -->
                    <div class="card card-custom">
                        <div class="card-header">
                            <h5 class="mb-0">快速操作</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <a href="#" onclick="showTab('config')" class="btn btn-primary w-100 mb-2">
                                        <i class="bi bi-gear"></i> 配置API服务
                                    </a>
                                    <a href="#" onclick="showTab('test')" class="btn btn-success w-100 mb-2">
                                        <i class="bi bi-check-circle"></i> 测试API连接
                                    </a>
                                </div>
                                <div class="col-md-6">
                                    <a href="#" onclick="showTab('password')" class="btn btn-warning w-100 mb-2">
                                        <i class="bi bi-key"></i> 修改管理员密码
                                    </a>
                                    <a href="../" class="btn btn-outline-primary w-100 mb-2" target="_blank">
                                        <i class="bi bi-box-arrow-up-right"></i> 访问前台
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- API配置页面 -->
                <div id="config" class="tab-content" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
                        <h1 class="h2">AI模型配置</h1>
                        <small class="text-muted">支持多种AI模型，可自定义模型名称</small>
                    </div>

                    <form method="post">
                        <div class="row">
                            <!-- DeepSeek配置 -->
                            <div class="col-md-6">
                                <div class="card card-custom model-config">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0">
                                            <i class="bi bi-robot"></i> DeepSeek API 配置
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label class="form-label">API 地址</label>
                                            <input type="url" class="form-control" name="deepseek_api_url" 
                                                   value="<?php echo escape($currentConfig['deepseek_api_url']); ?>"
                                                   placeholder="https://api.deepseek.com/v1">
                                            <div class="form-text">请填写完整的API地址，包含协议和端口</div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">API 密钥</label>
                                            <input type="password" class="form-control" name="deepseek_api_key"
                                                   value="<?php echo escape($currentConfig['deepseek_api_key']); ?>"
                                                   placeholder="sk-your-deepseek-api-key">
                                            <div class="form-text">请填写有效的API密钥</div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">模型名称</label>
                                            <input type="text" class="form-control" name="deepseek_model_name"
                                                   value="<?php echo escape($currentConfig['deepseek_model_name']); ?>"
                                                   placeholder="deepseek-chat">
                                            <div class="form-text">支持: deepseek-chat, deepseek-coder 等</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 通义千问配置 -->
                            <div class="col-md-6">
                                <div class="card card-custom model-config">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0">
                                            <i class="bi bi-chat-dots"></i> 通义千问 API 配置
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label class="form-label">API 地址</label>
                                            <input type="url" class="form-control" name="qwen_api_url"
                                                   value="<?php echo escape($currentConfig['qwen_api_url']); ?>"
                                                   placeholder="https://dashscope.aliyuncs.com/compatible-mode/v1">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">API 密钥</label>
                                            <input type="password" class="form-control" name="qwen_api_key"
                                                   value="<?php echo escape($currentConfig['qwen_api_key']); ?>"
                                                   placeholder="sk-your-qwen-api-key">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">模型名称</label>
                                            <input type="text" class="form-control" name="qwen_model_name"
                                                   value="<?php echo escape($currentConfig['qwen_model_name']); ?>"
                                                   placeholder="qwen-turbo">
                                            <div class="form-text">支持: qwen-turbo, qwen-plus, qwen-max 等</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 全局设置 -->
                        <div class="card card-custom mt-3">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-sliders"></i> 全局设置
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">默认AI模型</label>
                                            <select class="form-control" name="default_model">
                                                <option value="deepseek" <?php echo $currentConfig['default_model'] === 'deepseek' ? 'selected' : ''; ?>>DeepSeek</option>
                                                <option value="qwen" <?php echo $currentConfig['default_model'] === 'qwen' ? 'selected' : ''; ?>>通义千问</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">API超时时间 (秒)</label>
                                            <input type="number" class="form-control" name="api_timeout"
                                                   value="<?php echo escape($currentConfig['api_timeout']); ?>"
                                                   min="10" max="300" step="1">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">最大Token数</label>
                                            <input type="number" class="form-control" name="max_tokens"
                                                   value="<?php echo escape($currentConfig['max_tokens']); ?>"
                                                   min="100" max="4000" step="100">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label class="form-label">Temperature</label>
                                            <input type="number" class="form-control" name="temperature"
                                                   value="<?php echo escape($currentConfig['temperature']); ?>"
                                                   min="0" max="2" step="0.1">
                                            <div class="form-text">控制回答的随机性 (0-2)</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <button type="submit" name="update_config" class="btn btn-primary btn-lg">
                                        <i class="bi bi-save"></i> 保存配置
                                    </button>
                                    <button type="button" class="btn btn-outline-info" onclick="showConfigHelp()">
                                        <i class="bi bi-question-circle"></i> 配置帮助
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 配置帮助 -->
                        <div id="configHelp" class="card card-custom mt-3" style="display: none;">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">配置说明</h5>
                            </div>
                            <div class="card-body">
                                <h6>支持的AI服务商：</h6>
                                <ul>
                                    <li><strong>DeepSeek官方：</strong> https://api.deepseek.com/v1</li>
                                    <li><strong>阿里云通义千问：</strong> https://dashscope.aliyuncs.com/compatible-mode/v1</li>
                                    <li><strong>OpenAI：</strong> https://api.openai.com/v1</li>
                                    <li><strong>自建API服务：</strong> 支持vLLM, FastChat, Ollama等兼容OpenAI格式的服务</li>
                                </ul>
                                
                                <h6>常用模型名称：</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>DeepSeek模型：</strong>
                                        <ul>
                                            <li>deepseek-chat（通用对话）</li>
                                            <li>deepseek-coder（代码生成）</li>
                                        </ul>
                                    </div>
                                    <div class="col-md-6">
                                        <strong>通义千问模型：</strong>
                                        <ul>
                                            <li>qwen-turbo（快速版）</li>
                                            <li>qwen-plus（增强版）</li>
                                            <li>qwen-max（顶级版）</li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <h6>参数说明：</h6>
                                <ul>
                                    <li><strong>Temperature：</strong> 0=更确定性的回答，1=更有创造性的回答</li>
                                    <li><strong>Max Tokens：</strong> 控制回复的最大长度</li>
                                    <li><strong>Timeout：</strong> API请求的超时时间，建议30-60秒</li>
                                </ul>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- 密码修改页面 -->
                <div id="password" class="tab-content" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
                        <h1 class="h2">修改管理员密码</h1>
                    </div>

                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <div class="card card-custom">
                                <div class="card-header">
                                    <h5 class="mb-0">
                                        <i class="bi bi-key"></i> 安全设置
                                    </h5>
                                </div>
                                <div class="card-body">
                                    <form method="post">
                                        <div class="mb-3">
                                            <label class="form-label">当前密码</label>
                                            <input type="password" class="form-control" name="current_password" required>
                                            <div class="form-text">如果是首次修改密码，请输入默认密码：password</div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">新密码</label>
                                            <input type="password" class="form-control" name="new_password" 
                                                   id="newPassword" minlength="6" required onkeyup="checkPasswordStrength()">
                                            <div class="password-strength mt-2" id="passwordStrength"></div>
                                            <div class="form-text">密码长度至少6位，建议包含字母、数字和特殊字符</div>
                                        </div>
                                        <div class="mb-4">
                                            <label class="form-label">确认新密码</label>
                                            <input type="password" class="form-control" name="confirm_password" 
                                                   id="confirmPassword" required onkeyup="checkPasswordMatch()">
                                            <div id="passwordMatch" class="form-text"></div>
                                        </div>
                                        <button type="submit" name="change_password" class="btn btn-warning btn-lg w-100">
                                            <i class="bi bi-key"></i> 修改密码
                                        </button>
                                    </form>
                                </div>
                            </div>

                            <div class="card card-custom mt-3">
                                <div class="card-header">
                                    <h5 class="mb-0">调试工具</h5>
                                </div>
                                <div class="card-body">
                                    <p>如果密码验证遇到问题，可以使用以下工具：</p>
                                    <a href="password_debug.php" target="_blank" class="btn btn-info me-2">
                                        <i class="bi bi-bug"></i> 密码调试工具
                                    </a>
                                    <a href="init_admin.php" target="_blank" class="btn btn-warning">
                                        <i class="bi bi-arrow-clockwise"></i> 重新初始化账户
                                    </a>
                                </div>
                            </div>

                            <div class="card card-custom mt-3">
                                <div class="card-header">
                                    <h5 class="mb-0">安全建议</h5>
                                </div>
                                <div class="card-body">
                                    <ul class="list-unstyled">
                                        <li><i class="bi bi-check-circle text-success"></i> 定期更换管理员密码</li>
                                        <li><i class="bi bi-check-circle text-success"></i> 使用足够复杂的密码</li>
                                        <li><i class="bi bi-check-circle text-success"></i> 不要在公共场所登录管理后台</li>
                                        <li><i class="bi bi-check-circle text-success"></i> 退出时记得点击"退出登录"</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分析记录页面 -->
                <div id="records" class="tab-content" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
                        <h1 class="h2">分析记录</h1>
                    </div>
                    
                    <div class="card card-custom">
                        <div class="card-body">
                            <p>最近的分析记录会在这里显示...</p>
                            <div class="row">
                                <div class="col-md-6">
                                    <a href="../debug.log" target="_blank" class="btn btn-outline-primary w-100 mb-2">
                                        <i class="bi bi-file-text"></i> 查看调试日志
                                    </a>
                                </div>
                                <div class="col-md-6">
                                    <a href="../api_test.php" target="_blank" class="btn btn-outline-info w-100 mb-2">
                                        <i class="bi bi-bug"></i> API调试页面
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- API测试页面 -->
                <div id="test" class="tab-content" style="display: none;">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
                        <h1 class="h2">API测试</h1>
                    </div>
                    
                    <div class="card card-custom">
                        <div class="card-body">
                            <p>您可以在这里测试API连接状态和功能完整性</p>
                            <div class="row">
                                <div class="col-md-4">
                                    <a href="../api_test.php" target="_blank" class="btn btn-primary w-100 mb-2">
                                        <i class="bi bi-check-circle"></i> API连接测试
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="../test_full_analysis.php" target="_blank" class="btn btn-success w-100 mb-2">
                                        <i class="bi bi-gear"></i> 完整流程测试
                                    </a>
                                </div>
                                <div class="col-md-4">
                                    <a href="../simple_test.html" target="_blank" class="btn btn-info w-100 mb-2">
                                        <i class="bi bi-play-circle"></i> 前端功能测试
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showTab(tabName) {
            // 隐藏所有标签页
            const tabs = document.querySelectorAll('.tab-content');
            tabs.forEach(tab => tab.style.display = 'none');
            
            // 显示指定标签页
            document.getElementById(tabName).style.display = 'block';
            
            // 更新导航状态
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => link.classList.remove('active'));
            event.target.classList.add('active');
        }

        function showConfigHelp() {
            const helpDiv = document.getElementById('configHelp');
            helpDiv.style.display = helpDiv.style.display === 'none' ? 'block' : 'none';
        }

        function checkPasswordStrength() {
            const password = document.getElementById('newPassword').value;
            const strengthBar = document.getElementById('passwordStrength');
            
            let strength = 0;
            if (password.length >= 6) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;
            
            const colors = ['#dc3545', '#fd7e14', '#ffc107', '#20c997', '#28a745'];
            const texts = ['很弱', '弱', '一般', '强', '很强'];
            
            strengthBar.style.width = (strength * 20) + '%';
            strengthBar.style.backgroundColor = colors[strength - 1] || '#e9ecef';
            strengthBar.textContent = texts[strength - 1] || '';
            strengthBar.style.color = 'white';
            strengthBar.style.textAlign = 'center';
            strengthBar.style.fontSize = '12px';
        }

        function checkPasswordMatch() {
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            const matchDiv = document.getElementById('passwordMatch');
            
            if (confirmPassword === '') {
                matchDiv.textContent = '';
                return;
            }
            
            if (newPassword === confirmPassword) {
                matchDiv.textContent = '✓ 密码匹配';
                matchDiv.className = 'form-text text-success';
            } else {
                matchDiv.textContent = '✗ 密码不匹配';
                matchDiv.className = 'form-text text-danger';
            }
        }
    </script>
</body>
</html>
