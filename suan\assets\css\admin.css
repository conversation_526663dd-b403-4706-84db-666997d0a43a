/* 管理后台样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #6366f1;
    --secondary-color: #8b5cf6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --bg-light: #f9fafb;
    --bg-dark: #111827;
    --white: #ffffff;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-light);
    min-height: 100vh;
}

.admin-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1.5rem;
}

/* 头部样式 */
.admin-header {
    background: var(--white);
    padding: 1.5rem 2rem;
    border-radius: 12px;
    box-shadow: var(--shadow-sm);
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid var(--border-color);
}

.admin-header h1 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.admin-nav {
    display: flex;
    gap: 0.5rem;
}

.admin-nav a {
    padding: 0.5rem 1rem;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.2s ease;
    font-weight: 500;
    font-size: 0.875rem;
}

.admin-nav a:hover,
.admin-nav a.active {
    background: var(--primary-color);
    color: var(--white);
}

/* 主要内容区域 */
.admin-main {
    background: var(--white);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--white);
    padding: 1.5rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: var(--shadow-md);
}

.stat-card h3 {
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    opacity: 0.9;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-trend {
    font-size: 0.75rem;
    opacity: 0.8;
}

/* 表格样式 */
.recent-section {
    margin-top: 2rem;
}

.recent-section h2 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    background: var(--white);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.admin-table th,
.admin-table td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.875rem;
}

.admin-table th {
    background: var(--bg-light);
    font-weight: 600;
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: 0.75rem;
}

.admin-table td {
    color: var(--text-secondary);
}

.admin-table tr:hover {
    background: var(--bg-light);
}

/* 配置表单 */
.config-form {
    max-width: 800px;
}

.config-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    background: var(--bg-light);
}

.config-section h2 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
    background: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* 按钮样式 */
.save-btn,
.login-btn,
.action-btn {
    padding: 0.75rem 1.5rem;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.save-btn:hover,
.login-btn:hover,
.action-btn:hover {
    background: #5855eb;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.action-btn.danger {
    background: var(--error-color);
}

.action-btn.danger:hover {
    background: #dc2626;
}

/* 登录页面 */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    padding: 1rem;
}

.login-box {
    background: var(--white);
    padding: 2rem;
    border-radius: 16px;
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 400px;
}

.login-box h2 {
    text-align: center;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* 提示信息 */
.alert {
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 500;
}

.alert-success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.alert-error {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

.alert-warning {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fde68a;
}

.alert-info {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #93c5fd;
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1.5rem;
}

.pagination a,
.pagination span {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    text-decoration: none;
    color: var(--text-secondary);
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.pagination a:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.pagination .current {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

/* 搜索和过滤 */
.search-filter {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.search-input {
    flex: 1;
    min-width: 200px;
}

.filter-select {
    min-width: 120px;
}

/* 操作按钮组 */
.actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.btn-group {
    display: flex;
    gap: 0.25rem;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
}

/* 加载状态 */
.loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top-color: var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .admin-container {
        padding: 1rem;
    }
    
    .admin-header {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }
    
    .admin-nav {
        width: 100%;
        justify-content: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .config-section {
        padding: 1rem;
    }
    
    .search-filter {
        flex-direction: column;
    }
    
    .actions {
        flex-direction: column;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .admin-table {
        font-size: 0.75rem;
    }
    
    .admin-table th,
    .admin-table td {
        padding: 0.5rem;
    }
}

@media (max-width: 480px) {
    .login-box {
        margin: 1rem;
        padding: 1.5rem;
    }
    
    .admin-main {
        padding: 1rem;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #f9fafb;
        --text-secondary: #d1d5db;
        --bg-light: #1f2937;
        --white: #111827;
        --border-color: #374151;
    }
    
    body {
        background: var(--bg-dark);
        color: var(--text-primary);
    }
}

/* 数据表格增强 */
.table-responsive {
    overflow-x: auto;
}

.table-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.bulk-actions {
    display: none;
}

.bulk-actions.active {
    display: flex;
    gap: 0.5rem;
}

/* 图表样式 */
.chart-container {
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.chart-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}
