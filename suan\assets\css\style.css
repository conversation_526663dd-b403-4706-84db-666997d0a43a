/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #6366f1;
    --secondary-color: #8b5cf6;
    --accent-color: #06b6d4;
    --bg-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --bg-light: #f9fafb;
    --white: #ffffff;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-gradient);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.main-header {
    text-align: center;
    margin-bottom: 3rem;
    color: var(--white);
}

.main-header h1 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.subtitle {
    font-size: clamp(1rem, 2vw, 1.25rem);
    margin-bottom: 1rem;
    opacity: 0.95;
    font-weight: 500;
}

.description {
    font-size: 1rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* 输入区域样式 */
.input-section {
    background: var(--white);
    border-radius: 24px;
    padding: 2.5rem;
    margin-bottom: 3rem;
    box-shadow: var(--shadow-xl);
    backdrop-filter: blur(10px);
}

.input-section h2 {
    text-align: center;
    margin-bottom: 2rem;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.input-grid, .date-time-grid {
    display: grid;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.input-grid {
    grid-template-columns: 2fr 1fr;
}

.date-time-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.form-group input,
.form-group select {
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: var(--white);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.gender-group .radio-group {
    display: flex;
    gap: 1rem;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-weight: 500;
}

.radio-label input[type="radio"] {
    width: auto;
    margin: 0;
}

.submit-btn {
    width: 100%;
    padding: 1rem 2rem;
    background: var(--bg-gradient);
    color: var(--white);
    border: none;
    border-radius: 16px;
    font-size: 1.125rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.submit-btn:active {
    transform: translateY(0);
}

.submit-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* 问题模板区域样式 */
.questions-section {
    background: var(--white);
    border-radius: 24px;
    padding: 2.5rem;
    margin-bottom: 3rem;
    box-shadow: var(--shadow-xl);
}

.questions-section h2 {
    text-align: center;
    margin-bottom: 1rem;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.section-desc {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--text-secondary);
    font-size: 1rem;
}

.questions-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.question-card {
    background: var(--bg-light);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.question-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.question-card.selected {
    border-color: var(--primary-color);
    background: rgba(99, 102, 241, 0.05);
}

.question-card h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.question-text {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
    line-height: 1.5;
}

.category-tag {
    display: inline-block;
    background: var(--primary-color);
    color: var(--white);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.copy-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--accent-color);
    color: var(--white);
    border: none;
    border-radius: 8px;
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    opacity: 0;
    transform: translateY(-10px);
}

.question-card:hover .copy-btn {
    opacity: 1;
    transform: translateY(0);
}

.copy-btn:hover {
    background: #0891b2;
}

/* 特色功能区域样式 */
.features-section {
    background: var(--white);
    border-radius: 24px;
    padding: 2.5rem;
    margin-bottom: 3rem;
    box-shadow: var(--shadow-xl);
}

.features-section h2 {
    text-align: center;
    margin-bottom: 2rem;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    text-align: center;
    padding: 2rem;
    border: 1px solid var(--border-color);
    border-radius: 16px;
    background: var(--bg-light);
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* 结果页面样式 */
.result-page {
    background: var(--white);
    border-radius: 24px;
    padding: 2.5rem;
    box-shadow: var(--shadow-xl);
}

.result-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.result-header h2 {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.birth-info {
    color: var(--text-secondary);
    font-size: 1rem;
}

.back-link {
    color: var(--white);
    text-decoration: none;
    font-size: 1rem;
    margin-bottom: 1rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    transition: all 0.2s ease;
}

.back-link:hover {
    background: rgba(255, 255, 255, 0.3);
}

.bazi-section, .analysis-section {
    margin-bottom: 2rem;
}

.section-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.bazi-chart {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.bazi-column {
    text-align: center;
    padding: 1rem;
    border: 2px solid var(--primary-color);
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
}

.bazi-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.bazi-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.bazi-element {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.analysis-content {
    background: var(--bg-light);
    padding: 2rem;
    border-radius: 16px;
    border-left: 4px solid var(--primary-color);
    line-height: 1.8;
    font-size: 1rem;
}

.actions {
    text-align: center;
    margin-top: 2rem;
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--bg-light);
    color: var(--text-primary);
    text-decoration: none;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn.primary {
    background: var(--bg-gradient);
    color: var(--white);
    border-color: transparent;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* 页脚样式 */
footer {
    text-align: center;
    margin-top: 3rem;
    padding: 2rem;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .input-grid {
        grid-template-columns: 1fr;
    }
    
    .date-time-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .questions-container {
        grid-template-columns: 1fr;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .bazi-chart {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .actions {
        flex-direction: column;
        align-items: center;
    }
    
    .action-btn {
        width: 100%;
        max-width: 200px;
    }
}

@media (max-width: 480px) {
    .input-section,
    .questions-section,
    .features-section,
    .result-page {
        padding: 1.5rem;
    }
    
    .date-time-grid {
        grid-template-columns: 1fr;
    }
    
    .main-header h1 {
        font-size: 1.75rem;
    }
    
    .subtitle {
        font-size: 1rem;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.input-section,
.questions-section,
.features-section {
    animation: fadeInUp 0.6s ease-out;
}

.questions-section {
    animation-delay: 0.2s;
}

.features-section {
    animation-delay: 0.4s;
}

/* 加载状态 */
.loading .submit-btn {
    position: relative;
    color: transparent;
}

.loading .submit-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top-color: white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

