document.addEventListener('DOMContentLoaded', function() {
    initializeForm();
    initializeQuestionCards();
    initializeDateValidation();
});

function initializeForm() {
    const form = document.getElementById('baziForm');
    const submitBtn = document.getElementById('submitBtn');
    
    if (form) {
        form.addEventListener('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
                return false;
            }
            
            // 显示加载状态
            document.body.classList.add('loading');
            submitBtn.disabled = true;
            submitBtn.textContent = '正在分析中...';
            
            // 防止重复提交
            setTimeout(() => {
                submitBtn.style.pointerEvents = 'none';
            }, 100);
        });
    }
}

function initializeQuestionCards() {
    const questionCards = document.querySelectorAll('.question-card');
    const selectedQuestionInput = document.getElementById('selectedQuestion');
    const submitBtn = document.getElementById('submitBtn');
    
    questionCards.forEach(card => {
        const copyBtn = card.querySelector('.copy-btn');
        const questionText = card.dataset.question;
        
        // 点击卡片选择问题
        card.addEventListener('click', function(e) {
            if (e.target === copyBtn) return;
            
            // 移除其他卡片的选中状态
            questionCards.forEach(c => c.classList.remove('selected'));
            
            // 选中当前卡片
            this.classList.add('selected');
            
            // 设置问题到隐藏字段
            selectedQuestionInput.value = questionText;
            
            // 更新提交按钮文字
            submitBtn.textContent = '使用此问题开始分析';
            
            // 添加视觉反馈
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
        
        // 复制按钮功能
        copyBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(questionText).then(() => {
                    showToast('问题已复制到剪贴板');
                    this.textContent = '已复制';
                    setTimeout(() => {
                        this.textContent = '点击复制';
                    }, 2000);
                });
            } else {
                // 兼容性方案
                const textarea = document.createElement('textarea');
                textarea.value = questionText;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                showToast('问题已复制到剪贴板');
            }
        });
    });
}

function initializeDateValidation() {
    const monthSelect = document.getElementById('month');
    const daySelect = document.getElementById('day');
    const yearSelect = document.getElementById('year');
    
    if (monthSelect && daySelect && yearSelect) {
        [monthSelect, yearSelect].forEach(select => {
            select.addEventListener('change', updateDayOptions);
        });
        
        function updateDayOptions() {
            const month = parseInt(monthSelect.value);
            const year = parseInt(yearSelect.value) || 2024;
            
            if (!month) return;
            
            let maxDays = 31;
            if ([4, 6, 9, 11].includes(month)) {
                maxDays = 30;
            } else if (month === 2) {
                maxDays = isLeapYear(year) ? 29 : 28;
            }
            
            const currentDay = parseInt(daySelect.value);
            daySelect.innerHTML = '<option value="">选择日期</option>';
            
            for (let day = 1; day <= maxDays; day++) {
                const option = document.createElement('option');
                option.value = day;
                option.textContent = day + '日';
                if (day === currentDay && day <= maxDays) {
                    option.selected = true;
                }
                daySelect.appendChild(option);
            }
        }
        
        function isLeapYear(year) {
            return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
        }
    }
}

function validateForm() {
    const requiredFields = [
        { id: 'name', message: '请输入姓名' },
        { name: 'gender', message: '请选择性别', type: 'radio' },
        { id: 'year', message: '请选择出生年份' },
        { id: 'month', message: '请选择出生月份' },
        { id: 'day', message: '请选择出生日期' },
        { id: 'hour', message: '请选择出生小时' }
    ];
    
    for (let field of requiredFields) {
        let element;
        
        if (field.type === 'radio') {
            element = document.querySelector(`input[name="${field.name}"]:checked`);
        } else {
            element = document.getElementById(field.id);
        }
        
        if (!element || !element.value) {
            showToast(field.message);
            if (element && element.focus) {
                element.focus();
            }
            return false;
        }
    }
    
    // 验证姓名长度
    const nameLength = document.getElementById('name').value.trim().length;
    if (nameLength < 2 || nameLength > 20) {
        showToast('姓名长度应在2-20个字符之间');
        return false;
    }
    
    return true;
}

function showToast(message, type = 'info') {
    // 移除现有的toast
    const existingToast = document.querySelector('.toast');
    if (existingToast) {
        existingToast.remove();
    }
    
    // 创建新的toast
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    // 添加样式
    Object.assign(toast.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        background: type === 'error' ? '#ef4444' : '#06b6d4',
        color: 'white',
        padding: '12px 20px',
        borderRadius: '8px',
        fontWeight: '500',
        fontSize: '14px',
        zIndex: '10000',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        transform: 'translateX(350px)',
        transition: 'transform 0.3s ease',
        maxWidth: '300px',
        wordWrap: 'break-word'
    });
    
    document.body.appendChild(toast);
    
    // 动画显示
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        toast.style.transform = 'translateX(350px)';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// 结果页面的功能
function shareResult() {
    if (navigator.share) {
        navigator.share({
            title: '我的八字AI分析结果',
            text: '快来看看我的专业八字分析报告！',
            url: window.location.href
        }).catch(err => {
            console.log('分享失败:', err);
            fallbackShare();
        });
    } else {
        fallbackShare();
    }
}

function fallbackShare() {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(window.location.href).then(() => {
            showToast('链接已复制到剪贴板，可以分享给朋友了！');
        });
    } else {
        const textarea = document.createElement('textarea');
        textarea.value = window.location.href;
        document.body.appendChild(textarea);
        textarea.select();
        document.execCommand('copy');
        document.body.removeChild(textarea);
        showToast('链接已复制到剪贴板，可以分享给朋友了！');
    }
}

// 平滑滚动
function smoothScroll(target) {
    const element = document.querySelector(target);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// 打印功能优化
function printResult() {
    const printStyles = `
        <style>
            @media print {
                body * { visibility: hidden; }
                .result-page, .result-page * { visibility: visible; }
                .result-page { position: absolute; left: 0; top: 0; width: 100%; }
                .actions, .back-link { display: none !important; }
                .analysis-content { background: white !important; }
            }
        </style>
    `;
    
    const head = document.head;
    const style = document.createElement('style');
    style.innerHTML = printStyles;
    head.appendChild(style);
    
    window.print();
    
    setTimeout(() => {
        head.removeChild(style);
    }, 1000);
}

// 表单自动保存和恢复
function saveFormData() {
    const formData = {
        name: document.getElementById('name')?.value || '',
        gender: document.querySelector('input[name="gender"]:checked')?.value || '',
        year: document.getElementById('year')?.value || '',
        month: document.getElementById('month')?.value || '',
        day: document.getElementById('day')?.value || '',
        hour: document.getElementById('hour')?.value || '',
        minute: document.getElementById('minute')?.value || '',
        question: document.getElementById('selectedQuestion')?.value || ''
    };
    
    localStorage.setItem('baziFormData', JSON.stringify(formData));
}

function restoreFormData() {
    const savedData = localStorage.getItem('baziFormData');
    if (!savedData) return;
    
    try {
        const formData = JSON.parse(savedData);
        
        Object.keys(formData).forEach(key => {
            const element = document.getElementById(key);
            if (element && formData[key]) {
                element.value = formData[key];
            } else if (key === 'gender' && formData[key]) {
                const radioElement = document.querySelector(`input[name="gender"][value="${formData[key]}"]`);
                if (radioElement) {
                    radioElement.checked = true;
                }
            } else if (key === 'question' && formData[key]) {
                const questionInput = document.getElementById('selectedQuestion');
                if (questionInput) {
                    questionInput.value = formData[key];
                    
                    // 找到对应的问题卡片并标记为选中
                    const questionCards = document.querySelectorAll('.question-card');
                    questionCards.forEach(card => {
                        if (card.dataset.question === formData[key]) {
                            card.classList.add('selected');
                        }
                    });
                }
            }
        });
    } catch (e) {
        console.error('恢复表单数据失败:', e);
    }
}

// 页面加载时恢复表单数据
document.addEventListener('DOMContentLoaded', function() {
    restoreFormData();
    
    // 监听表单变化并自动保存
    const form = document.getElementById('baziForm');
    if (form) {
        const inputs = form.querySelectorAll('input, select');
        inputs.forEach(input => {
            input.addEventListener('change', saveFormData);
        });
    }
});

// 页面卸载时清除保存的数据（如果表单已提交）
window.addEventListener('beforeunload', function() {
    if (document.body.classList.contains('loading')) {
        localStorage.removeItem('baziFormData');
    }
});
