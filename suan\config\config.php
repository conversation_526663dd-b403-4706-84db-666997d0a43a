<?php
require_once __DIR__ . '/database.php';

class Config {
    private static $config = null;
    
    public static function get($key) {
        if (self::$config === null) {
            self::loadConfig();
        }
        return isset(self::$config[$key]) ? self::$config[$key] : null;
    }
    
    public static function set($key, $value) {
        global $pdo;
        $stmt = $pdo->prepare("INSERT INTO system_config (config_key, config_value) VALUES (?, ?) ON DUPLICATE KEY UPDATE config_value = ?");
        $stmt->execute([$key, $value, $value]);
        self::$config[$key] = $value;
    }
    
    private static function loadConfig() {
        global $pdo;
        $stmt = $pdo->query("SELECT config_key, config_value FROM system_config");
        self::$config = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            self::$config[$row['config_key']] = $row['config_value'];
        }
    }
    
    public static function getQuestionTemplates($category = null) {
        global $pdo;
        $sql = "SELECT * FROM question_templates WHERE is_active = 1";
        if ($category) {
            $sql .= " AND category = :category";
        }
        $sql .= " ORDER BY sort_order ASC";
        
        $stmt = $pdo->prepare($sql);
        if ($category) {
            $stmt->bindParam(':category', $category);
        }
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>
