<?php
require_once 'config.php';
require_once '../includes/AIClient.php';

echo "=== 系统配置测试 ===\n\n";

// 测试数据库连接
try {
    global $pdo;
    $pdo->query('SELECT 1');
    echo "✅ 数据库连接正常\n";
} catch (Exception $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
}

// 测试DeepSeek API
try {
    $deepseekClient = new AIClient('deepseek');
    echo "✅ DeepSeek API配置已加载\n";
    echo "   API地址: " . Config::get('deepseek_api_url') . "\n";
    echo "   API密钥: " . substr(Config::get('deepseek_api_key'), 0, 10) . "...\n";
} catch (Exception $e) {
    echo "❌ DeepSeek API配置错误: " . $e->getMessage() . "\n";
}

// 测试Qwen API
try {
    $qwenClient = new AIClient('qwen');
    echo "✅ Qwen API配置已加载\n";
    echo "   API地址: " . Config::get('qwen_api_url') . "\n";
    echo "   API密钥: " . substr(Config::get('qwen_api_key'), 0, 10) . "...\n";
} catch (Exception $e) {
    echo "❌ Qwen API配置错误: " . $e->getMessage() . "\n";
}

// 测试目录权限
$directories = ['logs', 'config'];
foreach ($directories as $dir) {
    $path = __DIR__ . '/../' . $dir;
    if (is_writable($path)) {
        echo "✅ {$dir}/ 目录可写\n";
    } else {
        echo "❌ {$dir}/ 目录不可写\n";
    }
}

echo "\n=== 测试完成 ===\n";
?>
