[2025-05-28 13:20:35] === result.php start ===
[2025-05-28 13:20:35] Request method - Data: "POST"
[2025-05-28 13:20:35] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"144","Host":"test.linn.pro"}
[2025-05-28 13:20:35] Starting main processing...
[2025-05-28 13:20:35] Raw input length - Data: 144
[2025-05-28 13:20:35] Raw input preview - Data: "{\"name\":\"王欢\",\"gender\":\"女\",\"year\":1986,\"month\":2,\"day\":1,\"hour\":13,\"minute\":45,\"question\":\"什么时候适合结婚？\",\"model\":\"deepseek\"}"
[2025-05-28 13:20:35] Decoded input - Data: {"name":"王欢","gender":"女","year":1986,"month":2,"day":1,"hour":13,"minute":45,"question":"什么时候适合结婚？","model":"deepseek"}
[2025-05-28 13:20:35] Field validation passed
[2025-05-28 13:20:35] Extracted parameters - Data: {"name":"王欢","gender":"女","birth":"1986-2-1 13:45","question_length":27,"model":"deepseek"}
[2025-05-28 13:20:35] Parameter validation passed
[2025-05-28 13:20:35] Files exist, loading...
[2025-05-28 13:20:35] Files loaded successfully
[2025-05-28 13:20:35] Starting bazi calculation
[2025-05-28 13:20:35] Bazi calculation successful
[2025-05-28 13:20:35] Building analysis prompt
[2025-05-28 13:20:35] Prompt built, length - Data: 541
[2025-05-28 13:20:35] Starting AI analysis with model: deepseek
[2025-05-28 13:21:35] AI analysis completed - Data: {"has_error":true,"content_length":726}
[2025-05-28 13:21:35] AI analysis has error - Data: "网络请求失败: Operation timed out after 60001 milliseconds with 0 bytes received"
[2025-05-28 13:21:35] Preparing final response
[2025-05-28 13:21:35] Saving to database
[2025-05-28 13:21:35] Database save successful, record ID: 9
[2025-05-28 13:21:35] Preparing success response
[2025-05-28 13:21:35] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 13:21:35] === result.php end ===
[2025-05-28 13:22:16] === result.php start ===
[2025-05-28 13:22:16] Request method - Data: "POST"
[2025-05-28 13:22:16] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"145","Host":"test.linn.pro"}
[2025-05-28 13:22:16] Starting main processing...
[2025-05-28 13:22:16] Raw input length - Data: 145
[2025-05-28 13:22:16] Raw input preview - Data: "{\"name\":\"王欢\",\"gender\":\"男\",\"year\":1986,\"month\":11,\"day\":1,\"hour\":16,\"minute\":50,\"question\":\"什么时候适合结婚？\",\"model\":\"deepseek\"}"
[2025-05-28 13:22:16] Decoded input - Data: {"name":"王欢","gender":"男","year":1986,"month":11,"day":1,"hour":16,"minute":50,"question":"什么时候适合结婚？","model":"deepseek"}
[2025-05-28 13:22:16] Field validation passed
[2025-05-28 13:22:16] Extracted parameters - Data: {"name":"王欢","gender":"男","birth":"1986-11-1 16:50","question_length":27,"model":"deepseek"}
[2025-05-28 13:22:16] Parameter validation passed
[2025-05-28 13:22:16] Files exist, loading...
[2025-05-28 13:22:16] Files loaded successfully
[2025-05-28 13:22:16] Starting bazi calculation
[2025-05-28 13:22:16] Bazi calculation successful
[2025-05-28 13:22:16] Building analysis prompt
[2025-05-28 13:22:16] Prompt built, length - Data: 539
[2025-05-28 13:22:16] Starting AI analysis with model: deepseek
[2025-05-28 13:22:38] AI analysis completed - Data: {"has_error":false,"content_length":5002}
[2025-05-28 13:22:38] Preparing final response
[2025-05-28 13:22:38] Saving to database
[2025-05-28 13:22:38] Database save successful, record ID: 10
[2025-05-28 13:22:38] Preparing success response
[2025-05-28 13:22:38] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 13:22:38] === result.php end ===
[2025-05-28 13:22:59] === result.php start ===
[2025-05-28 13:22:59] Request method - Data: "POST"
[2025-05-28 13:22:59] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"141","Host":"test.linn.pro"}
[2025-05-28 13:22:59] Starting main processing...
[2025-05-28 13:22:59] Raw input length - Data: 141
[2025-05-28 13:22:59] Raw input preview - Data: "{\"name\":\"王欢\",\"gender\":\"男\",\"year\":1986,\"month\":11,\"day\":1,\"hour\":16,\"minute\":50,\"question\":\"什么时候适合结婚？\",\"model\":\"qwen\"}"
[2025-05-28 13:22:59] Decoded input - Data: {"name":"王欢","gender":"男","year":1986,"month":11,"day":1,"hour":16,"minute":50,"question":"什么时候适合结婚？","model":"qwen"}
[2025-05-28 13:22:59] Field validation passed
[2025-05-28 13:22:59] Extracted parameters - Data: {"name":"王欢","gender":"男","birth":"1986-11-1 16:50","question_length":27,"model":"qwen"}
[2025-05-28 13:22:59] Parameter validation passed
[2025-05-28 13:22:59] Files exist, loading...
[2025-05-28 13:22:59] Files loaded successfully
[2025-05-28 13:22:59] Starting bazi calculation
[2025-05-28 13:22:59] Bazi calculation successful
[2025-05-28 13:22:59] Building analysis prompt
[2025-05-28 13:22:59] Prompt built, length - Data: 539
[2025-05-28 13:22:59] Starting AI analysis with model: qwen
[2025-05-28 13:23:05] AI analysis completed - Data: {"has_error":true,"content_length":868}
[2025-05-28 13:23:05] AI analysis has error - Data: "API请求失败: HTTP 400 - {\"error\":{\"code\":\"invalid_parameter_error\",\"message\":\"Provider API error: parameter.enable_thinking must be set to false for non-streaming calls (request id: 20250528132300498208053MDXOLmZA)\",\"type\":\""
[2025-05-28 13:23:05] Preparing final response
[2025-05-28 13:23:05] Saving to database
[2025-05-28 13:23:05] Database save successful, record ID: 11
[2025-05-28 13:23:05] Preparing success response
[2025-05-28 13:23:05] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 13:23:05] === result.php end ===
[2025-05-28 13:29:59] === result.php start ===
[2025-05-28 13:29:59] Request method - Data: "POST"
[2025-05-28 13:29:59] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"141","Host":"test.linn.pro"}
[2025-05-28 13:29:59] Starting main processing...
[2025-05-28 13:29:59] Raw input length - Data: 141
[2025-05-28 13:29:59] Raw input preview - Data: "{\"name\":\"沈万三\",\"gender\":\"男\",\"year\":1987,\"month\":1,\"day\":4,\"hour\":0,\"minute\":0,\"question\":\"什么时候适合结婚？\",\"model\":\"qwen\"}"
[2025-05-28 13:29:59] Decoded input - Data: {"name":"沈万三","gender":"男","year":1987,"month":1,"day":4,"hour":0,"minute":0,"question":"什么时候适合结婚？","model":"qwen"}
[2025-05-28 13:29:59] Field validation passed
[2025-05-28 13:29:59] Extracted parameters - Data: {"name":"沈万三","gender":"男","birth":"1987-1-4 0:0","question_length":27,"model":"qwen"}
[2025-05-28 13:29:59] Parameter validation passed
[2025-05-28 13:29:59] Files exist, loading...
[2025-05-28 13:29:59] Files loaded successfully
[2025-05-28 13:29:59] Starting bazi calculation
[2025-05-28 13:29:59] Bazi calculation successful
[2025-05-28 13:29:59] Building analysis prompt
[2025-05-28 13:29:59] Prompt built, length - Data: 544
[2025-05-28 13:29:59] Starting AI analysis with model: qwen
[2025-05-28 13:30:04] AI analysis completed - Data: {"has_error":true,"content_length":868}
[2025-05-28 13:30:04] AI analysis has error - Data: "API请求失败: HTTP 400 - {\"error\":{\"code\":\"invalid_parameter_error\",\"message\":\"Provider API error: parameter.enable_thinking must be set to false for non-streaming calls (request id: 2025052813300077941206GCsMvYf7)\",\"type\":\"i"
[2025-05-28 13:30:04] Preparing final response
[2025-05-28 13:30:04] Saving to database
[2025-05-28 13:30:04] Database save successful, record ID: 12
[2025-05-28 13:30:04] Preparing success response
[2025-05-28 13:30:04] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 13:30:04] === result.php end ===
[2025-05-28 13:34:00] === result.php start ===
[2025-05-28 13:34:00] Request method - Data: "POST"
[2025-05-28 13:34:00] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"141","Host":"test.linn.pro"}
[2025-05-28 13:34:00] Starting main processing...
[2025-05-28 13:34:00] Raw input length - Data: 141
[2025-05-28 13:34:00] Raw input preview - Data: "{\"name\":\"沈万三\",\"gender\":\"男\",\"year\":1986,\"month\":1,\"day\":1,\"hour\":0,\"minute\":0,\"question\":\"什么时候适合结婚？\",\"model\":\"qwen\"}"
[2025-05-28 13:34:00] Decoded input - Data: {"name":"沈万三","gender":"男","year":1986,"month":1,"day":1,"hour":0,"minute":0,"question":"什么时候适合结婚？","model":"qwen"}
[2025-05-28 13:34:00] Field validation passed
[2025-05-28 13:34:00] Extracted parameters - Data: {"name":"沈万三","gender":"男","birth":"1986-1-1 0:0","question_length":27,"model":"qwen"}
[2025-05-28 13:34:00] Parameter validation passed
[2025-05-28 13:34:00] Files exist, loading...
[2025-05-28 13:34:00] Fatal error caught - Data: {"message":"syntax error, unexpected 'if' (T_IF), expecting function (T_FUNCTION) or const (T_CONST)","file":"\/www\/wwwroot\/test.linn.pro\/includes\/AIAnalyzer.php","line":13}
[2025-05-28 13:34:00] Sending response - Data: {"success":false,"error":"系统发生严重错误","statusCode":500,"has_data":false}
[2025-05-28 13:34:00] === result.php end ===
[2025-05-28 13:34:03] === result.php start ===
[2025-05-28 13:34:03] Request method - Data: "POST"
[2025-05-28 13:34:03] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"141","Host":"test.linn.pro"}
[2025-05-28 13:34:03] Starting main processing...
[2025-05-28 13:34:03] Raw input length - Data: 141
[2025-05-28 13:34:03] Raw input preview - Data: "{\"name\":\"沈万三\",\"gender\":\"男\",\"year\":1986,\"month\":1,\"day\":1,\"hour\":0,\"minute\":0,\"question\":\"什么时候适合结婚？\",\"model\":\"qwen\"}"
[2025-05-28 13:34:03] Decoded input - Data: {"name":"沈万三","gender":"男","year":1986,"month":1,"day":1,"hour":0,"minute":0,"question":"什么时候适合结婚？","model":"qwen"}
[2025-05-28 13:34:03] Field validation passed
[2025-05-28 13:34:03] Extracted parameters - Data: {"name":"沈万三","gender":"男","birth":"1986-1-1 0:0","question_length":27,"model":"qwen"}
[2025-05-28 13:34:03] Parameter validation passed
[2025-05-28 13:34:03] Files exist, loading...
[2025-05-28 13:34:03] Fatal error caught - Data: {"message":"syntax error, unexpected 'if' (T_IF), expecting function (T_FUNCTION) or const (T_CONST)","file":"\/www\/wwwroot\/test.linn.pro\/includes\/AIAnalyzer.php","line":13}
[2025-05-28 13:34:03] Sending response - Data: {"success":false,"error":"系统发生严重错误","statusCode":500,"has_data":false}
[2025-05-28 13:34:03] === result.php end ===
[2025-05-28 13:34:20] === result.php start ===
[2025-05-28 13:34:20] Request method - Data: "POST"
[2025-05-28 13:34:20] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"141","Host":"test.linn.pro"}
[2025-05-28 13:34:20] Starting main processing...
[2025-05-28 13:34:20] Raw input length - Data: 141
[2025-05-28 13:34:20] Raw input preview - Data: "{\"name\":\"沈万三\",\"gender\":\"男\",\"year\":1986,\"month\":1,\"day\":1,\"hour\":0,\"minute\":0,\"question\":\"什么时候适合结婚？\",\"model\":\"qwen\"}"
[2025-05-28 13:34:20] Decoded input - Data: {"name":"沈万三","gender":"男","year":1986,"month":1,"day":1,"hour":0,"minute":0,"question":"什么时候适合结婚？","model":"qwen"}
[2025-05-28 13:34:20] Field validation passed
[2025-05-28 13:34:20] Extracted parameters - Data: {"name":"沈万三","gender":"男","birth":"1986-1-1 0:0","question_length":27,"model":"qwen"}
[2025-05-28 13:34:20] Parameter validation passed
[2025-05-28 13:34:20] Files exist, loading...
[2025-05-28 13:34:20] Fatal error caught - Data: {"message":"syntax error, unexpected 'if' (T_IF), expecting function (T_FUNCTION) or const (T_CONST)","file":"\/www\/wwwroot\/test.linn.pro\/includes\/AIAnalyzer.php","line":13}
[2025-05-28 13:34:20] Sending response - Data: {"success":false,"error":"系统发生严重错误","statusCode":500,"has_data":false}
[2025-05-28 13:34:20] === result.php end ===
[2025-05-28 13:34:52] === result.php start ===
[2025-05-28 13:34:52] Request method - Data: "POST"
[2025-05-28 13:34:52] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"141","Host":"test.linn.pro"}
[2025-05-28 13:34:52] Starting main processing...
[2025-05-28 13:34:52] Raw input length - Data: 141
[2025-05-28 13:34:52] Raw input preview - Data: "{\"name\":\"沈万三\",\"gender\":\"男\",\"year\":1986,\"month\":1,\"day\":1,\"hour\":0,\"minute\":0,\"question\":\"什么时候适合结婚？\",\"model\":\"qwen\"}"
[2025-05-28 13:34:52] Decoded input - Data: {"name":"沈万三","gender":"男","year":1986,"month":1,"day":1,"hour":0,"minute":0,"question":"什么时候适合结婚？","model":"qwen"}
[2025-05-28 13:34:52] Field validation passed
[2025-05-28 13:34:52] Extracted parameters - Data: {"name":"沈万三","gender":"男","birth":"1986-1-1 0:0","question_length":27,"model":"qwen"}
[2025-05-28 13:34:52] Parameter validation passed
[2025-05-28 13:34:52] Files exist, loading...
[2025-05-28 13:34:52] Files loaded successfully
[2025-05-28 13:34:52] Starting bazi calculation
[2025-05-28 13:34:52] Bazi calculation successful
[2025-05-28 13:34:52] Building analysis prompt
[2025-05-28 13:34:52] Prompt built, length - Data: 542
[2025-05-28 13:34:52] Starting AI analysis with model: qwen
[2025-05-28 13:34:57] AI analysis completed - Data: {"has_error":true,"content_length":868}
[2025-05-28 13:34:57] AI analysis has error - Data: "API请求失败: HTTP 400 - {\"error\":{\"code\":\"invalid_parameter_error\",\"message\":\"Provider API error: parameter.enable_thinking must be set to false for non-streaming calls (request id: 20250528133453537136116mWDUEpse)\",\"type\":\""
[2025-05-28 13:34:57] Preparing final response
[2025-05-28 13:34:57] Saving to database
[2025-05-28 13:34:57] Database save successful, record ID: 13
[2025-05-28 13:34:57] Preparing success response
[2025-05-28 13:34:57] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 13:34:57] === result.php end ===
[2025-05-28 13:38:44] === result.php start ===
[2025-05-28 13:38:44] Request method - Data: "POST"
[2025-05-28 13:38:44] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"141","Host":"test.linn.pro"}
[2025-05-28 13:38:44] Starting main processing...
[2025-05-28 13:38:44] Raw input length - Data: 141
[2025-05-28 13:38:44] Raw input preview - Data: "{\"name\":\"沈万三\",\"gender\":\"男\",\"year\":1986,\"month\":1,\"day\":1,\"hour\":0,\"minute\":0,\"question\":\"什么时候适合结婚？\",\"model\":\"qwen\"}"
[2025-05-28 13:38:44] Decoded input - Data: {"name":"沈万三","gender":"男","year":1986,"month":1,"day":1,"hour":0,"minute":0,"question":"什么时候适合结婚？","model":"qwen"}
[2025-05-28 13:38:44] Field validation passed
[2025-05-28 13:38:44] Extracted parameters - Data: {"name":"沈万三","gender":"男","birth":"1986-1-1 0:0","question_length":27,"model":"qwen"}
[2025-05-28 13:38:44] Parameter validation passed
[2025-05-28 13:38:44] Files exist, loading...
[2025-05-28 13:38:44] Files loaded successfully
[2025-05-28 13:38:44] Starting bazi calculation
[2025-05-28 13:38:44] Bazi calculation successful
[2025-05-28 13:38:44] Building analysis prompt
[2025-05-28 13:38:44] Prompt built, length - Data: 542
[2025-05-28 13:38:44] Starting AI analysis with model: qwen
[2025-05-28 13:39:21] AI analysis completed - Data: {"has_error":false,"content_length":2917}
[2025-05-28 13:39:21] Preparing final response
[2025-05-28 13:39:21] Saving to database
[2025-05-28 13:39:21] Database save successful, record ID: 14
[2025-05-28 13:39:21] Preparing success response
[2025-05-28 13:39:21] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 13:39:21] === result.php end ===
[2025-05-28 13:55:50] === result.php start ===
[2025-05-28 13:55:50] Request method - Data: "POST"
[2025-05-28 13:55:50] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"145","Host":"test.linn.pro"}
[2025-05-28 13:55:50] Starting main processing...
[2025-05-28 13:55:50] Raw input length - Data: 145
[2025-05-28 13:55:50] Raw input preview - Data: "{\"name\":\"沈万三\",\"gender\":\"男\",\"year\":1986,\"month\":1,\"day\":1,\"hour\":0,\"minute\":0,\"question\":\"什么时候适合结婚？\",\"model\":\"deepseek\"}"
[2025-05-28 13:55:50] Decoded input - Data: {"name":"沈万三","gender":"男","year":1986,"month":1,"day":1,"hour":0,"minute":0,"question":"什么时候适合结婚？","model":"deepseek"}
[2025-05-28 13:55:50] Field validation passed
[2025-05-28 13:55:50] Extracted parameters - Data: {"name":"沈万三","gender":"男","birth":"1986-1-1 0:0","question_length":27,"model":"deepseek"}
[2025-05-28 13:55:50] Parameter validation passed
[2025-05-28 13:55:50] Files exist, loading...
[2025-05-28 13:55:54] === result.php start ===
[2025-05-28 13:55:54] Request method - Data: "POST"
[2025-05-28 13:55:54] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"145","Host":"test.linn.pro"}
[2025-05-28 13:55:54] Starting main processing...
[2025-05-28 13:55:54] Raw input length - Data: 145
[2025-05-28 13:55:54] Raw input preview - Data: "{\"name\":\"沈万三\",\"gender\":\"男\",\"year\":1986,\"month\":1,\"day\":1,\"hour\":0,\"minute\":0,\"question\":\"什么时候适合结婚？\",\"model\":\"deepseek\"}"
[2025-05-28 13:55:54] Decoded input - Data: {"name":"沈万三","gender":"男","year":1986,"month":1,"day":1,"hour":0,"minute":0,"question":"什么时候适合结婚？","model":"deepseek"}
[2025-05-28 13:55:54] Field validation passed
[2025-05-28 13:55:54] Extracted parameters - Data: {"name":"沈万三","gender":"男","birth":"1986-1-1 0:0","question_length":27,"model":"deepseek"}
[2025-05-28 13:55:54] Parameter validation passed
[2025-05-28 13:55:54] Files exist, loading...
[2025-05-28 13:55:58] === result.php start ===
[2025-05-28 13:55:58] Request method - Data: "POST"
[2025-05-28 13:55:58] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"141","Host":"test.linn.pro"}
[2025-05-28 13:55:58] Starting main processing...
[2025-05-28 13:55:58] Raw input length - Data: 141
[2025-05-28 13:55:58] Raw input preview - Data: "{\"name\":\"沈万三\",\"gender\":\"男\",\"year\":1986,\"month\":1,\"day\":1,\"hour\":0,\"minute\":0,\"question\":\"什么时候适合结婚？\",\"model\":\"qwen\"}"
[2025-05-28 13:55:58] Decoded input - Data: {"name":"沈万三","gender":"男","year":1986,"month":1,"day":1,"hour":0,"minute":0,"question":"什么时候适合结婚？","model":"qwen"}
[2025-05-28 13:55:58] Field validation passed
[2025-05-28 13:55:58] Extracted parameters - Data: {"name":"沈万三","gender":"男","birth":"1986-1-1 0:0","question_length":27,"model":"qwen"}
[2025-05-28 13:55:58] Parameter validation passed
[2025-05-28 13:55:58] Files exist, loading...
[2025-05-28 13:56:24] === result.php start ===
[2025-05-28 13:56:24] Request method - Data: "POST"
[2025-05-28 13:56:24] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"141","Host":"test.linn.pro"}
[2025-05-28 13:56:24] Starting main processing...
[2025-05-28 13:56:24] Raw input length - Data: 141
[2025-05-28 13:56:24] Raw input preview - Data: "{\"name\":\"沈万三\",\"gender\":\"男\",\"year\":1986,\"month\":1,\"day\":1,\"hour\":0,\"minute\":0,\"question\":\"什么时候适合结婚？\",\"model\":\"qwen\"}"
[2025-05-28 13:56:24] Decoded input - Data: {"name":"沈万三","gender":"男","year":1986,"month":1,"day":1,"hour":0,"minute":0,"question":"什么时候适合结婚？","model":"qwen"}
[2025-05-28 13:56:24] Field validation passed
[2025-05-28 13:56:24] Extracted parameters - Data: {"name":"沈万三","gender":"男","birth":"1986-1-1 0:0","question_length":27,"model":"qwen"}
[2025-05-28 13:56:24] Parameter validation passed
[2025-05-28 13:56:24] Files exist, loading...
[2025-05-28 13:56:24] Files loaded successfully
[2025-05-28 13:56:24] Starting bazi calculation
[2025-05-28 13:56:24] Bazi calculation successful
[2025-05-28 13:56:24] Building analysis prompt
[2025-05-28 13:56:24] Prompt built, length - Data: 542
[2025-05-28 13:56:24] Starting AI analysis with model: qwen
[2025-05-28 13:57:11] AI analysis completed - Data: {"has_error":false,"content_length":5497}
[2025-05-28 13:57:11] Preparing final response
[2025-05-28 13:57:11] Saving to database
[2025-05-28 13:57:11] Database save successful, record ID: 15
[2025-05-28 13:57:11] Preparing success response
[2025-05-28 13:57:11] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 13:57:11] === result.php end ===
[2025-05-28 14:04:47] === result.php start ===
[2025-05-28 14:04:47] Request method - Data: "POST"
[2025-05-28 14:04:47] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"141","Host":"test.linn.pro"}
[2025-05-28 14:04:47] Starting main processing...
[2025-05-28 14:04:47] Raw input length - Data: 141
[2025-05-28 14:04:47] Raw input preview - Data: "{\"name\":\"沈万三\",\"gender\":\"男\",\"year\":1986,\"month\":1,\"day\":1,\"hour\":0,\"minute\":0,\"question\":\"什么时候适合结婚？\",\"model\":\"qwen\"}"
[2025-05-28 14:04:47] Decoded input - Data: {"name":"沈万三","gender":"男","year":1986,"month":1,"day":1,"hour":0,"minute":0,"question":"什么时候适合结婚？","model":"qwen"}
[2025-05-28 14:04:47] Field validation passed
[2025-05-28 14:04:47] Extracted parameters - Data: {"name":"沈万三","gender":"男","birth":"1986-1-1 0:0","question_length":27,"model":"qwen"}
[2025-05-28 14:04:47] Parameter validation passed
[2025-05-28 14:04:47] Files exist, loading...
[2025-05-28 14:04:47] Files loaded successfully
[2025-05-28 14:04:47] Starting bazi calculation
[2025-05-28 14:04:47] Bazi calculation successful
[2025-05-28 14:04:47] Building analysis prompt
[2025-05-28 14:04:47] Prompt built, length - Data: 542
[2025-05-28 14:04:47] Starting AI analysis with model: qwen
[2025-05-28 14:05:31] AI analysis completed - Data: {"has_error":false,"content_length":4815}
[2025-05-28 14:05:31] Preparing final response
[2025-05-28 14:05:31] Saving to database
[2025-05-28 14:05:31] Database save successful, record ID: 16
[2025-05-28 14:05:31] Preparing success response
[2025-05-28 14:05:31] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 14:05:31] === result.php end ===
[2025-05-28 14:11:32] === result.php start ===
[2025-05-28 14:11:32] Request method - Data: "POST"
[2025-05-28 14:11:32] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"145","Host":"test.linn.pro"}
[2025-05-28 14:11:32] Starting main processing...
[2025-05-28 14:11:32] Raw input length - Data: 145
[2025-05-28 14:11:32] Raw input preview - Data: "{\"name\":\"沈万三\",\"gender\":\"男\",\"year\":1986,\"month\":1,\"day\":1,\"hour\":0,\"minute\":0,\"question\":\"什么时候适合结婚？\",\"model\":\"deepseek\"}"
[2025-05-28 14:11:32] Decoded input - Data: {"name":"沈万三","gender":"男","year":1986,"month":1,"day":1,"hour":0,"minute":0,"question":"什么时候适合结婚？","model":"deepseek"}
[2025-05-28 14:11:32] Field validation passed
[2025-05-28 14:11:32] Extracted parameters - Data: {"name":"沈万三","gender":"男","birth":"1986-1-1 0:0","question_length":27,"model":"deepseek"}
[2025-05-28 14:11:32] Parameter validation passed
[2025-05-28 14:11:32] Files exist, loading...
[2025-05-28 14:11:32] Files loaded successfully
[2025-05-28 14:11:32] Starting bazi calculation
[2025-05-28 14:11:32] Bazi calculation successful
[2025-05-28 14:11:32] Building analysis prompt
[2025-05-28 14:11:32] Prompt built, length - Data: 542
[2025-05-28 14:11:32] Starting AI analysis with model: deepseek
[2025-05-28 14:12:32] AI analysis completed - Data: {"has_error":true,"content_length":726}
[2025-05-28 14:12:32] AI analysis has error - Data: "网络请求失败: Operation timed out after 60001 milliseconds with 0 bytes received"
[2025-05-28 14:12:32] Preparing final response
[2025-05-28 14:12:32] Saving to database
[2025-05-28 14:12:32] Database save successful, record ID: 17
[2025-05-28 14:12:32] Preparing success response
[2025-05-28 14:12:32] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 14:12:32] === result.php end ===
[2025-05-28 14:13:23] === result.php start ===
[2025-05-28 14:13:23] Request method - Data: "POST"
[2025-05-28 14:13:23] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"145","Host":"test.linn.pro"}
[2025-05-28 14:13:23] Starting main processing...
[2025-05-28 14:13:23] Raw input length - Data: 145
[2025-05-28 14:13:23] Raw input preview - Data: "{\"name\":\"沈万三\",\"gender\":\"男\",\"year\":1986,\"month\":1,\"day\":1,\"hour\":0,\"minute\":0,\"question\":\"什么时候适合结婚？\",\"model\":\"deepseek\"}"
[2025-05-28 14:13:23] Decoded input - Data: {"name":"沈万三","gender":"男","year":1986,"month":1,"day":1,"hour":0,"minute":0,"question":"什么时候适合结婚？","model":"deepseek"}
[2025-05-28 14:13:23] Field validation passed
[2025-05-28 14:13:23] Extracted parameters - Data: {"name":"沈万三","gender":"男","birth":"1986-1-1 0:0","question_length":27,"model":"deepseek"}
[2025-05-28 14:13:23] Parameter validation passed
[2025-05-28 14:13:23] Files exist, loading...
[2025-05-28 14:13:23] Files loaded successfully
[2025-05-28 14:13:23] Starting bazi calculation
[2025-05-28 14:13:23] Bazi calculation successful
[2025-05-28 14:13:23] Building analysis prompt
[2025-05-28 14:13:23] Prompt built, length - Data: 542
[2025-05-28 14:13:23] Starting AI analysis with model: deepseek
[2025-05-28 14:13:58] AI analysis completed - Data: {"has_error":false,"content_length":8629}
[2025-05-28 14:13:58] Preparing final response
[2025-05-28 14:13:58] Saving to database
[2025-05-28 14:13:58] Database save successful, record ID: 18
[2025-05-28 14:13:58] Preparing success response
[2025-05-28 14:13:58] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 14:13:58] === result.php end ===
[2025-05-28 14:25:38] === result.php start ===
[2025-05-28 14:25:38] Request method - Data: "POST"
[2025-05-28 14:25:38] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"145","Host":"test.linn.pro"}
[2025-05-28 14:25:38] Starting main processing...
[2025-05-28 14:25:38] Raw input length - Data: 145
[2025-05-28 14:25:38] Raw input preview - Data: "{\"name\":\"沈万三\",\"gender\":\"男\",\"year\":1986,\"month\":1,\"day\":1,\"hour\":0,\"minute\":0,\"question\":\"什么时候适合结婚？\",\"model\":\"deepseek\"}"
[2025-05-28 14:25:38] Decoded input - Data: {"name":"沈万三","gender":"男","year":1986,"month":1,"day":1,"hour":0,"minute":0,"question":"什么时候适合结婚？","model":"deepseek"}
[2025-05-28 14:25:38] Field validation passed
[2025-05-28 14:25:38] Extracted parameters - Data: {"name":"沈万三","gender":"男","birth":"1986-1-1 0:0","question_length":27,"model":"deepseek"}
[2025-05-28 14:25:38] Parameter validation passed
[2025-05-28 14:25:38] Files exist, loading...
[2025-05-28 14:25:38] Files loaded successfully
[2025-05-28 14:25:38] Starting bazi calculation
[2025-05-28 14:25:38] Bazi calculation successful
[2025-05-28 14:25:38] Building analysis prompt
[2025-05-28 14:25:38] Prompt built, length - Data: 542
[2025-05-28 14:25:38] Starting AI analysis with model: deepseek
[2025-05-28 14:26:27] AI analysis completed - Data: {"has_error":false,"content_length":7669}
[2025-05-28 14:26:27] Preparing final response
[2025-05-28 14:26:27] Saving to database
[2025-05-28 14:26:27] Database save successful, record ID: 22
[2025-05-28 14:26:27] Preparing success response
[2025-05-28 14:26:27] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 14:26:27] === result.php end ===
[2025-05-28 14:29:33] === result.php start ===
[2025-05-28 14:29:33] Request method - Data: "POST"
[2025-05-28 14:29:33] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"167","Host":"test.linn.pro"}
[2025-05-28 14:29:33] Starting main processing...
[2025-05-28 14:29:33] Raw input length - Data: 167
[2025-05-28 14:29:33] Raw input preview - Data: "{\"name\":\"林元洪\",\"gender\":\"男\",\"year\":1986,\"month\":1,\"day\":16,\"hour\":3,\"minute\":0,\"question\":\"我的事业发展前景如何与财运怎样？\",\"model\":\"deepseek\"}"
[2025-05-28 14:29:33] Decoded input - Data: {"name":"林元洪","gender":"男","year":1986,"month":1,"day":16,"hour":3,"minute":0,"question":"我的事业发展前景如何与财运怎样？","model":"deepseek"}
[2025-05-28 14:29:33] Field validation passed
[2025-05-28 14:29:33] Extracted parameters - Data: {"name":"林元洪","gender":"男","birth":"1986-1-16 3:0","question_length":48,"model":"deepseek"}
[2025-05-28 14:29:33] Parameter validation passed
[2025-05-28 14:29:33] Files exist, loading...
[2025-05-28 14:29:33] Files loaded successfully
[2025-05-28 14:29:33] Starting bazi calculation
[2025-05-28 14:29:33] Bazi calculation successful
[2025-05-28 14:29:33] Building analysis prompt
[2025-05-28 14:29:33] Prompt built, length - Data: 565
[2025-05-28 14:29:33] Starting AI analysis with model: deepseek
[2025-05-28 14:30:11] AI analysis completed - Data: {"has_error":false,"content_length":7047}
[2025-05-28 14:30:11] Preparing final response
[2025-05-28 14:30:11] Saving to database
[2025-05-28 14:30:11] Database save successful, record ID: 24
[2025-05-28 14:30:11] Preparing success response
[2025-05-28 14:30:11] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 14:30:11] === result.php end ===
[2025-05-28 14:34:48] === result.php start ===
[2025-05-28 14:34:48] Request method - Data: "POST"
[2025-05-28 14:34:48] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"163","Host":"test.linn.pro"}
[2025-05-28 14:34:48] Starting main processing...
[2025-05-28 14:34:48] Raw input length - Data: 163
[2025-05-28 14:34:48] Raw input preview - Data: "{\"name\":\"林元洪\",\"gender\":\"男\",\"year\":1986,\"month\":1,\"day\":16,\"hour\":3,\"minute\":0,\"question\":\"我的事业发展前景如何与财运怎样？\",\"model\":\"qwen\"}"
[2025-05-28 14:34:48] Decoded input - Data: {"name":"林元洪","gender":"男","year":1986,"month":1,"day":16,"hour":3,"minute":0,"question":"我的事业发展前景如何与财运怎样？","model":"qwen"}
[2025-05-28 14:34:48] Field validation passed
[2025-05-28 14:34:48] Extracted parameters - Data: {"name":"林元洪","gender":"男","birth":"1986-1-16 3:0","question_length":48,"model":"qwen"}
[2025-05-28 14:34:48] Parameter validation passed
[2025-05-28 14:34:48] Files exist, loading...
[2025-05-28 14:34:48] Files loaded successfully
[2025-05-28 14:34:48] Starting bazi calculation
[2025-05-28 14:34:48] Bazi calculation successful
[2025-05-28 14:34:48] Building analysis prompt
[2025-05-28 14:34:48] Prompt built, length - Data: 565
[2025-05-28 14:34:48] Starting AI analysis with model: qwen
[2025-05-28 14:35:28] AI analysis completed - Data: {"has_error":false,"content_length":5034}
[2025-05-28 14:35:28] Preparing final response
[2025-05-28 14:35:28] Saving to database
[2025-05-28 14:35:28] Database save successful, record ID: 26
[2025-05-28 14:35:28] Preparing success response
[2025-05-28 14:35:28] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 14:35:28] === result.php end ===
[2025-05-28 14:37:36] === result.php start ===
[2025-05-28 14:37:36] Request method - Data: "POST"
[2025-05-28 14:37:36] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"167","Host":"test.linn.pro"}
[2025-05-28 14:37:36] Starting main processing...
[2025-05-28 14:37:36] Raw input length - Data: 167
[2025-05-28 14:37:36] Raw input preview - Data: "{\"name\":\"林元洪\",\"gender\":\"男\",\"year\":1986,\"month\":1,\"day\":16,\"hour\":3,\"minute\":0,\"question\":\"我的事业发展前景如何与财运怎样？\",\"model\":\"deepseek\"}"
[2025-05-28 14:37:36] Decoded input - Data: {"name":"林元洪","gender":"男","year":1986,"month":1,"day":16,"hour":3,"minute":0,"question":"我的事业发展前景如何与财运怎样？","model":"deepseek"}
[2025-05-28 14:37:36] Field validation passed
[2025-05-28 14:37:36] Extracted parameters - Data: {"name":"林元洪","gender":"男","birth":"1986-1-16 3:0","question_length":48,"model":"deepseek"}
[2025-05-28 14:37:36] Parameter validation passed
[2025-05-28 14:37:36] Files exist, loading...
[2025-05-28 14:37:36] Files loaded successfully
[2025-05-28 14:37:36] Starting bazi calculation
[2025-05-28 14:37:36] Bazi calculation successful
[2025-05-28 14:37:36] Building analysis prompt
[2025-05-28 14:37:36] Prompt built, length - Data: 565
[2025-05-28 14:37:36] Starting AI analysis with model: deepseek
[2025-05-28 14:38:36] AI analysis completed - Data: {"has_error":true,"content_length":726}
[2025-05-28 14:38:36] AI analysis has error - Data: "网络请求失败: Operation timed out after 60001 milliseconds with 0 bytes received"
[2025-05-28 14:38:36] Preparing final response
[2025-05-28 14:38:36] Saving to database
[2025-05-28 14:38:36] Database save successful, record ID: 28
[2025-05-28 14:38:36] Preparing success response
[2025-05-28 14:38:36] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 14:38:36] === result.php end ===
[2025-05-28 14:42:51] === result.php start ===
[2025-05-28 14:42:51] Request method - Data: "POST"
[2025-05-28 14:42:51] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"167","Host":"test.linn.pro"}
[2025-05-28 14:42:51] Starting main processing...
[2025-05-28 14:42:51] Raw input length - Data: 167
[2025-05-28 14:42:51] Raw input preview - Data: "{\"name\":\"林元洪\",\"gender\":\"男\",\"year\":1986,\"month\":1,\"day\":16,\"hour\":3,\"minute\":0,\"question\":\"我的事业发展前景如何与财运怎样？\",\"model\":\"deepseek\"}"
[2025-05-28 14:42:51] Decoded input - Data: {"name":"林元洪","gender":"男","year":1986,"month":1,"day":16,"hour":3,"minute":0,"question":"我的事业发展前景如何与财运怎样？","model":"deepseek"}
[2025-05-28 14:42:51] Field validation passed
[2025-05-28 14:42:51] Extracted parameters - Data: {"name":"林元洪","gender":"男","birth":"1986-1-16 3:0","question_length":48,"model":"deepseek"}
[2025-05-28 14:42:51] Parameter validation passed
[2025-05-28 14:42:51] Files exist, loading...
[2025-05-28 14:42:51] Files loaded successfully
[2025-05-28 14:42:51] Starting bazi calculation
[2025-05-28 14:42:51] Bazi calculation successful
[2025-05-28 14:42:51] Building analysis prompt
[2025-05-28 14:42:51] Prompt built, length - Data: 565
[2025-05-28 14:42:51] Starting AI analysis with model: deepseek
[2025-05-28 14:43:13] AI analysis completed - Data: {"has_error":false,"content_length":4293}
[2025-05-28 14:43:13] Preparing final response
[2025-05-28 14:43:13] Saving to database
[2025-05-28 14:43:13] Database save successful, record ID: 30
[2025-05-28 14:43:13] Preparing success response
[2025-05-28 14:43:13] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 14:43:13] === result.php end ===
[2025-05-28 14:47:57] === result.php start ===
[2025-05-28 14:47:57] Request method - Data: "POST"
[2025-05-28 14:47:57] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"164","Host":"test.linn.pro"}
[2025-05-28 14:47:57] Starting main processing...
[2025-05-28 14:47:57] Raw input length - Data: 164
[2025-05-28 14:47:57] Raw input preview - Data: "{\"name\":\"林洪\",\"gender\":\"男\",\"year\":1986,\"month\":1,\"day\":16,\"hour\":3,\"minute\":0,\"question\":\"我的事业发展前景如何与财运怎样？\",\"model\":\"deepseek\"}"
[2025-05-28 14:47:57] Decoded input - Data: {"name":"林洪","gender":"男","year":1986,"month":1,"day":16,"hour":3,"minute":0,"question":"我的事业发展前景如何与财运怎样？","model":"deepseek"}
[2025-05-28 14:47:57] Field validation passed
[2025-05-28 14:47:57] Extracted parameters - Data: {"name":"林洪","gender":"男","birth":"1986-1-16 3:0","question_length":48,"model":"deepseek"}
[2025-05-28 14:47:57] Parameter validation passed
[2025-05-28 14:47:57] Files exist, loading...
[2025-05-28 14:47:57] Files loaded successfully
[2025-05-28 14:47:57] Starting bazi calculation
[2025-05-28 14:47:57] Bazi calculation successful
[2025-05-28 14:47:57] Building analysis prompt
[2025-05-28 14:47:57] Prompt built, length - Data: 562
[2025-05-28 14:47:57] Starting AI analysis with model: deepseek
[2025-05-28 14:48:25] AI analysis completed - Data: {"has_error":false,"content_length":3445}
[2025-05-28 14:48:25] Preparing final response
[2025-05-28 14:48:25] Saving to database
[2025-05-28 14:48:25] Database save successful, record ID: 32
[2025-05-28 14:48:25] Preparing success response
[2025-05-28 14:48:25] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 14:48:25] === result.php end ===
[2025-05-28 14:50:09] === result.php start ===
[2025-05-28 14:50:09] Request method - Data: "POST"
[2025-05-28 14:50:09] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"164","Host":"test.linn.pro"}
[2025-05-28 14:50:09] Starting main processing...
[2025-05-28 14:50:09] Raw input length - Data: 164
[2025-05-28 14:50:09] Raw input preview - Data: "{\"name\":\"林洪\",\"gender\":\"男\",\"year\":1986,\"month\":1,\"day\":16,\"hour\":3,\"minute\":0,\"question\":\"我的事业发展前景如何与财运怎样？\",\"model\":\"deepseek\"}"
[2025-05-28 14:50:09] Decoded input - Data: {"name":"林洪","gender":"男","year":1986,"month":1,"day":16,"hour":3,"minute":0,"question":"我的事业发展前景如何与财运怎样？","model":"deepseek"}
[2025-05-28 14:50:09] Field validation passed
[2025-05-28 14:50:09] Extracted parameters - Data: {"name":"林洪","gender":"男","birth":"1986-1-16 3:0","question_length":48,"model":"deepseek"}
[2025-05-28 14:50:09] Parameter validation passed
[2025-05-28 14:50:09] Files exist, loading...
[2025-05-28 14:50:09] Files loaded successfully
[2025-05-28 14:50:09] Starting bazi calculation
[2025-05-28 14:50:09] Bazi calculation successful
[2025-05-28 14:50:09] Building analysis prompt
[2025-05-28 14:50:09] Prompt built, length - Data: 562
[2025-05-28 14:50:09] Starting AI analysis with model: deepseek
[2025-05-28 14:51:09] AI analysis completed - Data: {"has_error":true,"content_length":726}
[2025-05-28 14:51:09] AI analysis has error - Data: "网络请求失败: Operation timed out after 60000 milliseconds with 0 bytes received"
[2025-05-28 14:51:09] Preparing final response
[2025-05-28 14:51:09] Saving to database
[2025-05-28 14:51:09] Database save successful, record ID: 34
[2025-05-28 14:51:09] Preparing success response
[2025-05-28 14:51:09] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 14:51:14] === result.php start ===
[2025-05-28 14:51:14] Request method - Data: "POST"
[2025-05-28 14:51:14] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"148","Host":"test.linn.pro"}
[2025-05-28 14:51:14] Starting main processing...
[2025-05-28 14:51:14] Raw input length - Data: 148
[2025-05-28 14:51:14] Raw input preview - Data: "{\"name\":\"洪新\",\"gender\":\"男\",\"year\":1986,\"month\":2,\"day\":1,\"hour\":0,\"minute\":5,\"question\":\"我的事业发展前景如何？\",\"model\":\"deepseek\"}"
[2025-05-28 14:51:14] Decoded input - Data: {"name":"洪新","gender":"男","year":1986,"month":2,"day":1,"hour":0,"minute":5,"question":"我的事业发展前景如何？","model":"deepseek"}
[2025-05-28 14:51:14] Field validation passed
[2025-05-28 14:51:14] Extracted parameters - Data: {"name":"洪新","gender":"男","birth":"1986-2-1 0:5","question_length":33,"model":"deepseek"}
[2025-05-28 14:51:14] Parameter validation passed
[2025-05-28 14:51:14] Files exist, loading...
[2025-05-28 14:51:14] Files loaded successfully
[2025-05-28 14:51:14] Starting bazi calculation
[2025-05-28 14:51:14] Bazi calculation successful
[2025-05-28 14:51:14] Building analysis prompt
[2025-05-28 14:51:14] Prompt built, length - Data: 545
[2025-05-28 14:51:14] Starting AI analysis with model: deepseek
[2025-05-28 14:51:14] Fatal error caught - Data: {"message":"Call to undefined method AIAnalyzer::loadConfig()","file":"\/www\/wwwroot\/test.linn.pro\/includes\/AIAnalyzer.php","line":9}
[2025-05-28 14:51:14] Sending response - Data: {"success":false,"error":"系统发生严重错误","statusCode":500,"has_data":false}
[2025-05-28 14:51:14] === result.php end ===
[2025-05-28 14:52:29] === result.php start ===
[2025-05-28 14:52:29] Request method - Data: "POST"
[2025-05-28 14:52:29] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"148","Host":"test.linn.pro"}
[2025-05-28 14:52:29] Starting main processing...
[2025-05-28 14:52:29] Raw input length - Data: 148
[2025-05-28 14:52:29] Raw input preview - Data: "{\"name\":\"洪新\",\"gender\":\"男\",\"year\":1986,\"month\":2,\"day\":1,\"hour\":0,\"minute\":5,\"question\":\"我的事业发展前景如何？\",\"model\":\"deepseek\"}"
[2025-05-28 14:52:29] Decoded input - Data: {"name":"洪新","gender":"男","year":1986,"month":2,"day":1,"hour":0,"minute":5,"question":"我的事业发展前景如何？","model":"deepseek"}
[2025-05-28 14:52:29] Field validation passed
[2025-05-28 14:52:29] Extracted parameters - Data: {"name":"洪新","gender":"男","birth":"1986-2-1 0:5","question_length":33,"model":"deepseek"}
[2025-05-28 14:52:29] Parameter validation passed
[2025-05-28 14:52:29] Files exist, loading...
[2025-05-28 14:52:29] Files loaded successfully
[2025-05-28 14:52:29] Starting bazi calculation
[2025-05-28 14:52:29] Bazi calculation successful
[2025-05-28 14:52:29] Building analysis prompt
[2025-05-28 14:52:29] Prompt built, length - Data: 545
[2025-05-28 14:52:29] Starting AI analysis with model: deepseek
[2025-05-28 14:53:29] AI analysis completed - Data: {"has_error":true,"content_length":726}
[2025-05-28 14:53:29] AI analysis has error - Data: "网络请求失败: Operation timed out after 60002 milliseconds with 0 bytes received"
[2025-05-28 14:53:29] Preparing final response
[2025-05-28 14:53:29] Saving to database
[2025-05-28 14:53:29] Database save successful, record ID: 36
[2025-05-28 14:53:29] Preparing success response
[2025-05-28 14:53:29] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 14:53:29] === result.php end ===
[2025-05-28 14:55:10] === result.php start ===
[2025-05-28 14:55:10] Request method - Data: "POST"
[2025-05-28 14:55:10] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"148","Host":"test.linn.pro"}
[2025-05-28 14:55:10] Starting main processing...
[2025-05-28 14:55:10] Raw input length - Data: 148
[2025-05-28 14:55:10] Raw input preview - Data: "{\"name\":\"洪新\",\"gender\":\"男\",\"year\":1986,\"month\":2,\"day\":1,\"hour\":0,\"minute\":5,\"question\":\"我的事业发展前景如何？\",\"model\":\"deepseek\"}"
[2025-05-28 14:55:10] Decoded input - Data: {"name":"洪新","gender":"男","year":1986,"month":2,"day":1,"hour":0,"minute":5,"question":"我的事业发展前景如何？","model":"deepseek"}
[2025-05-28 14:55:10] Field validation passed
[2025-05-28 14:55:10] Extracted parameters - Data: {"name":"洪新","gender":"男","birth":"1986-2-1 0:5","question_length":33,"model":"deepseek"}
[2025-05-28 14:55:10] Parameter validation passed
[2025-05-28 14:55:10] Files exist, loading...
[2025-05-28 14:55:10] Files loaded successfully
[2025-05-28 14:55:10] Starting bazi calculation
[2025-05-28 14:55:10] Bazi calculation successful
[2025-05-28 14:55:10] Building analysis prompt
[2025-05-28 14:55:10] Prompt built, length - Data: 545
[2025-05-28 14:55:10] Starting AI analysis with model: deepseek
[2025-05-28 14:55:33] AI analysis completed - Data: {"has_error":false,"content_length":3901}
[2025-05-28 14:55:33] Preparing final response
[2025-05-28 14:55:33] Saving to database
[2025-05-28 14:55:33] Database save successful, record ID: 38
[2025-05-28 14:55:33] Preparing success response
[2025-05-28 14:55:33] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 14:55:33] === result.php end ===
[2025-05-28 15:00:42] === result.php start ===
[2025-05-28 15:00:42] Request method - Data: "POST"
[2025-05-28 15:00:42] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"151","Host":"test.linn.pro"}
[2025-05-28 15:00:42] Starting main processing...
[2025-05-28 15:00:42] Raw input length - Data: 151
[2025-05-28 15:00:42] Raw input preview - Data: "{\"name\":\"洪新新\",\"gender\":\"男\",\"year\":1986,\"month\":2,\"day\":1,\"hour\":0,\"minute\":5,\"question\":\"我的事业发展前景如何？\",\"model\":\"deepseek\"}"
[2025-05-28 15:00:42] Decoded input - Data: {"name":"洪新新","gender":"男","year":1986,"month":2,"day":1,"hour":0,"minute":5,"question":"我的事业发展前景如何？","model":"deepseek"}
[2025-05-28 15:00:42] Field validation passed
[2025-05-28 15:00:42] Extracted parameters - Data: {"name":"洪新新","gender":"男","birth":"1986-2-1 0:5","question_length":33,"model":"deepseek"}
[2025-05-28 15:00:42] Parameter validation passed
[2025-05-28 15:00:42] Files exist, loading...
[2025-05-28 15:00:43] Files loaded successfully
[2025-05-28 15:00:43] Starting bazi calculation
[2025-05-28 15:00:43] Bazi calculation successful
[2025-05-28 15:00:43] Building analysis prompt
[2025-05-28 15:00:43] Prompt built, length - Data: 548
[2025-05-28 15:00:43] Starting AI analysis with model: deepseek
[2025-05-28 15:01:43] AI analysis completed - Data: {"has_error":true,"content_length":726}
[2025-05-28 15:01:43] AI analysis has error - Data: "网络请求失败: Operation timed out after 60001 milliseconds with 0 bytes received"
[2025-05-28 15:01:43] Preparing final response
[2025-05-28 15:01:43] Saving to database
[2025-05-28 15:01:43] Database save successful, record ID: 40
[2025-05-28 15:01:43] Preparing success response
[2025-05-28 15:01:43] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 15:01:43] === result.php end ===
[2025-05-28 15:06:01] === result.php start ===
[2025-05-28 15:06:01] Request method - Data: "POST"
[2025-05-28 15:06:01] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"147","Host":"test.linn.pro"}
[2025-05-28 15:06:01] Starting main processing...
[2025-05-28 15:06:01] Raw input length - Data: 147
[2025-05-28 15:06:01] Raw input preview - Data: "{\"name\":\"洪新新\",\"gender\":\"男\",\"year\":1986,\"month\":2,\"day\":1,\"hour\":0,\"minute\":5,\"question\":\"我的事业发展前景如何？\",\"model\":\"qwen\"}"
[2025-05-28 15:06:01] Decoded input - Data: {"name":"洪新新","gender":"男","year":1986,"month":2,"day":1,"hour":0,"minute":5,"question":"我的事业发展前景如何？","model":"qwen"}
[2025-05-28 15:06:01] Field validation passed
[2025-05-28 15:06:01] Extracted parameters - Data: {"name":"洪新新","gender":"男","birth":"1986-2-1 0:5","question_length":33,"model":"qwen"}
[2025-05-28 15:06:01] Parameter validation passed
[2025-05-28 15:06:01] Files exist, loading...
[2025-05-28 15:06:01] Files loaded successfully
[2025-05-28 15:06:01] Starting bazi calculation
[2025-05-28 15:06:01] Bazi calculation successful
[2025-05-28 15:06:01] Building analysis prompt
[2025-05-28 15:06:01] Prompt built, length - Data: 548
[2025-05-28 15:06:01] Starting AI analysis with model: qwen
[2025-05-28 15:06:49] AI analysis completed - Data: {"has_error":false,"content_length":3511}
[2025-05-28 15:06:49] Preparing final response
[2025-05-28 15:06:49] Saving to database
[2025-05-28 15:06:49] Database save successful, record ID: 42
[2025-05-28 15:06:49] Preparing success response
[2025-05-28 15:06:49] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 15:06:49] === result.php end ===
[2025-05-28 15:07:09] === result.php start ===
[2025-05-28 15:07:09] Request method - Data: "POST"
[2025-05-28 15:07:09] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"151","Host":"test.linn.pro"}
[2025-05-28 15:07:09] Starting main processing...
[2025-05-28 15:07:09] Raw input length - Data: 151
[2025-05-28 15:07:09] Raw input preview - Data: "{\"name\":\"洪新新\",\"gender\":\"男\",\"year\":1986,\"month\":2,\"day\":1,\"hour\":0,\"minute\":5,\"question\":\"我的事业发展前景如何？\",\"model\":\"deepseek\"}"
[2025-05-28 15:07:09] Decoded input - Data: {"name":"洪新新","gender":"男","year":1986,"month":2,"day":1,"hour":0,"minute":5,"question":"我的事业发展前景如何？","model":"deepseek"}
[2025-05-28 15:07:09] Field validation passed
[2025-05-28 15:07:09] Extracted parameters - Data: {"name":"洪新新","gender":"男","birth":"1986-2-1 0:5","question_length":33,"model":"deepseek"}
[2025-05-28 15:07:09] Parameter validation passed
[2025-05-28 15:07:09] Files exist, loading...
[2025-05-28 15:07:09] Files loaded successfully
[2025-05-28 15:07:09] Starting bazi calculation
[2025-05-28 15:07:09] Bazi calculation successful
[2025-05-28 15:07:09] Building analysis prompt
[2025-05-28 15:07:09] Prompt built, length - Data: 548
[2025-05-28 15:07:09] Starting AI analysis with model: deepseek
[2025-05-28 15:08:09] AI analysis completed - Data: {"has_error":true,"content_length":726}
[2025-05-28 15:08:09] AI analysis has error - Data: "网络请求失败: Operation timed out after 60001 milliseconds with 0 bytes received"
[2025-05-28 15:08:09] Preparing final response
[2025-05-28 15:08:09] Saving to database
[2025-05-28 15:08:09] Database save successful, record ID: 44
[2025-05-28 15:08:09] Preparing success response
[2025-05-28 15:08:09] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 15:08:09] === result.php end ===
[2025-05-28 15:08:24] === result.php start ===
[2025-05-28 15:08:24] Request method - Data: "POST"
[2025-05-28 15:08:24] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"151","Host":"test.linn.pro"}
[2025-05-28 15:08:24] Starting main processing...
[2025-05-28 15:08:24] Raw input length - Data: 151
[2025-05-28 15:08:24] Raw input preview - Data: "{\"name\":\"洪新新\",\"gender\":\"男\",\"year\":1986,\"month\":2,\"day\":1,\"hour\":0,\"minute\":5,\"question\":\"我的事业发展前景如何？\",\"model\":\"deepseek\"}"
[2025-05-28 15:08:24] Decoded input - Data: {"name":"洪新新","gender":"男","year":1986,"month":2,"day":1,"hour":0,"minute":5,"question":"我的事业发展前景如何？","model":"deepseek"}
[2025-05-28 15:08:24] Field validation passed
[2025-05-28 15:08:24] Extracted parameters - Data: {"name":"洪新新","gender":"男","birth":"1986-2-1 0:5","question_length":33,"model":"deepseek"}
[2025-05-28 15:08:24] Parameter validation passed
[2025-05-28 15:08:24] Files exist, loading...
[2025-05-28 15:08:24] Files loaded successfully
[2025-05-28 15:08:24] Starting bazi calculation
[2025-05-28 15:08:24] Bazi calculation successful
[2025-05-28 15:08:24] Building analysis prompt
[2025-05-28 15:08:24] Prompt built, length - Data: 548
[2025-05-28 15:08:24] Starting AI analysis with model: deepseek
[2025-05-28 15:08:57] AI analysis completed - Data: {"has_error":false,"content_length":7933}
[2025-05-28 15:08:57] Preparing final response
[2025-05-28 15:08:57] Saving to database
[2025-05-28 15:08:57] Database save successful, record ID: 46
[2025-05-28 15:08:57] Preparing success response
[2025-05-28 15:08:57] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 15:08:57] === result.php end ===
[2025-05-28 15:18:03] === result.php start ===
[2025-05-28 15:18:03] Request method - Data: "POST"
[2025-05-28 15:18:03] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"173","Host":"test.linn.pro"}
[2025-05-28 15:18:03] Starting main processing...
[2025-05-28 15:18:03] Raw input length - Data: 173
[2025-05-28 15:18:03] Raw input preview - Data: "{\"name\":\"林是\",\"gender\":\"男\",\"year\":1987,\"month\":2,\"day\":3,\"hour\":1,\"minute\":0,\"question\":\"我的事业发展前景如何？\",\"model\":\"deepseek\",\"calendar_type\":\"农历\"}"
[2025-05-28 15:18:03] Decoded input - Data: {"name":"林是","gender":"男","year":1987,"month":2,"day":3,"hour":1,"minute":0,"question":"我的事业发展前景如何？","model":"deepseek","calendar_type":"农历"}
[2025-05-28 15:18:03] Field validation passed
[2025-05-28 15:18:03] Extracted parameters - Data: {"name":"林是","gender":"男","birth":"1987-2-3 1:0","question_length":33,"model":"deepseek"}
[2025-05-28 15:18:03] Parameter validation passed
[2025-05-28 15:18:03] Files exist, loading...
[2025-05-28 15:18:03] Files loaded successfully
[2025-05-28 15:18:03] Starting bazi calculation
[2025-05-28 15:18:03] Bazi calculation successful
[2025-05-28 15:18:03] Building analysis prompt
[2025-05-28 15:18:03] Prompt built, length - Data: 545
[2025-05-28 15:18:03] Starting AI analysis with model: deepseek
[2025-05-28 15:19:04] AI analysis completed - Data: {"has_error":true,"content_length":726}
[2025-05-28 15:19:04] AI analysis has error - Data: "网络请求失败: Operation timed out after 60001 milliseconds with 0 bytes received"
[2025-05-28 15:19:04] Preparing final response
[2025-05-28 15:19:04] Saving to database
[2025-05-28 15:19:04] Database save successful, record ID: 48
[2025-05-28 15:19:04] Preparing success response
[2025-05-28 15:19:04] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 15:19:04] === result.php end ===
[2025-05-28 15:19:40] === result.php start ===
[2025-05-28 15:19:40] Request method - Data: "POST"
[2025-05-28 15:19:40] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"173","Host":"test.linn.pro"}
[2025-05-28 15:19:40] Starting main processing...
[2025-05-28 15:19:40] Raw input length - Data: 173
[2025-05-28 15:19:40] Raw input preview - Data: "{\"name\":\"林是\",\"gender\":\"男\",\"year\":1987,\"month\":2,\"day\":3,\"hour\":1,\"minute\":0,\"question\":\"我的事业发展前景如何？\",\"model\":\"deepseek\",\"calendar_type\":\"农历\"}"
[2025-05-28 15:19:40] Decoded input - Data: {"name":"林是","gender":"男","year":1987,"month":2,"day":3,"hour":1,"minute":0,"question":"我的事业发展前景如何？","model":"deepseek","calendar_type":"农历"}
[2025-05-28 15:19:40] Field validation passed
[2025-05-28 15:19:40] Extracted parameters - Data: {"name":"林是","gender":"男","birth":"1987-2-3 1:0","question_length":33,"model":"deepseek"}
[2025-05-28 15:19:40] Parameter validation passed
[2025-05-28 15:19:40] Files exist, loading...
[2025-05-28 15:19:40] Files loaded successfully
[2025-05-28 15:19:40] Starting bazi calculation
[2025-05-28 15:19:40] Bazi calculation successful
[2025-05-28 15:19:40] Building analysis prompt
[2025-05-28 15:19:40] Prompt built, length - Data: 545
[2025-05-28 15:19:40] Starting AI analysis with model: deepseek
[2025-05-28 15:20:07] AI analysis completed - Data: {"has_error":false,"content_length":6781}
[2025-05-28 15:20:07] Preparing final response
[2025-05-28 15:20:07] Saving to database
[2025-05-28 15:20:07] Database save successful, record ID: 50
[2025-05-28 15:20:07] Preparing success response
[2025-05-28 15:20:07] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 15:20:07] === result.php end ===
[2025-05-28 15:59:58] === result.php start ===
[2025-05-28 15:59:58] Request method - Data: "POST"
[2025-05-28 15:59:58] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/index.php","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"129","Host":"test.linn.pro"}
[2025-05-28 15:59:58] Starting main processing...
[2025-05-28 15:59:58] Raw input length - Data: 129
[2025-05-28 15:59:58] Raw input preview - Data: "{\"name\":\"阿里\",\"gender\":\"男\",\"year\":1986,\"month\":2,\"day\":2,\"hour\":11,\"minute\":50,\"question\":\"财运分析\",\"model\":\"deepseek\"}"
[2025-05-28 15:59:58] Decoded input - Data: {"name":"阿里","gender":"男","year":1986,"month":2,"day":2,"hour":11,"minute":50,"question":"财运分析","model":"deepseek"}
[2025-05-28 15:59:58] Field validation passed
[2025-05-28 15:59:58] Extracted parameters - Data: {"name":"阿里","gender":"男","birth":"1986-2-2 11:50","question_length":12,"model":"deepseek"}
[2025-05-28 15:59:58] Parameter validation passed
[2025-05-28 15:59:58] Files exist, loading...
[2025-05-28 15:59:58] Files loaded successfully
[2025-05-28 15:59:58] Starting bazi calculation
[2025-05-28 15:59:58] Bazi calculation successful
[2025-05-28 15:59:58] Building analysis prompt
[2025-05-28 15:59:58] Prompt built, length - Data: 526
[2025-05-28 15:59:58] Starting AI analysis with model: deepseek
[2025-05-28 16:00:49] AI analysis completed - Data: {"has_error":false,"content_length":2475}
[2025-05-28 16:00:49] Preparing final response
[2025-05-28 16:00:49] Saving to database
[2025-05-28 16:00:49] Database save successful, record ID: 52
[2025-05-28 16:00:49] Preparing success response
[2025-05-28 16:00:49] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 16:00:49] === result.php end ===
[2025-05-28 16:04:19] === result.php start ===
[2025-05-28 16:04:19] Request method - Data: "POST"
[2025-05-28 16:04:19] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/index.php","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"125","Host":"test.linn.pro"}
[2025-05-28 16:04:19] Starting main processing...
[2025-05-28 16:04:19] Raw input length - Data: 125
[2025-05-28 16:04:19] Raw input preview - Data: "{\"name\":\"阿里\",\"gender\":\"男\",\"year\":1986,\"month\":2,\"day\":2,\"hour\":11,\"minute\":50,\"question\":\"财运分析\",\"model\":\"qwen\"}"
[2025-05-28 16:04:19] Decoded input - Data: {"name":"阿里","gender":"男","year":1986,"month":2,"day":2,"hour":11,"minute":50,"question":"财运分析","model":"qwen"}
[2025-05-28 16:04:19] Field validation passed
[2025-05-28 16:04:19] Extracted parameters - Data: {"name":"阿里","gender":"男","birth":"1986-2-2 11:50","question_length":12,"model":"qwen"}
[2025-05-28 16:04:19] Parameter validation passed
[2025-05-28 16:04:19] Files exist, loading...
[2025-05-28 16:04:19] Files loaded successfully
[2025-05-28 16:04:19] Starting bazi calculation
[2025-05-28 16:04:19] Bazi calculation successful
[2025-05-28 16:04:19] Building analysis prompt
[2025-05-28 16:04:19] Prompt built, length - Data: 526
[2025-05-28 16:04:19] Starting AI analysis with model: qwen
[2025-05-28 16:04:48] AI analysis completed - Data: {"has_error":false,"content_length":4635}
[2025-05-28 16:04:48] Preparing final response
[2025-05-28 16:04:48] Saving to database
[2025-05-28 16:04:48] Database save successful, record ID: 54
[2025-05-28 16:04:48] Preparing success response
[2025-05-28 16:04:48] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 16:04:48] === result.php end ===
[2025-05-28 16:08:12] === result.php start ===
[2025-05-28 16:08:12] Request method - Data: "POST"
[2025-05-28 16:08:12] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/index.php","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"129","Host":"test.linn.pro"}
[2025-05-28 16:08:12] Starting main processing...
[2025-05-28 16:08:12] Raw input length - Data: 129
[2025-05-28 16:08:12] Raw input preview - Data: "{\"name\":\"阿里\",\"gender\":\"男\",\"year\":1986,\"month\":2,\"day\":2,\"hour\":11,\"minute\":50,\"question\":\"财运分析\",\"model\":\"deepseek\"}"
[2025-05-28 16:08:12] Decoded input - Data: {"name":"阿里","gender":"男","year":1986,"month":2,"day":2,"hour":11,"minute":50,"question":"财运分析","model":"deepseek"}
[2025-05-28 16:08:12] Field validation passed
[2025-05-28 16:08:12] Extracted parameters - Data: {"name":"阿里","gender":"男","birth":"1986-2-2 11:50","question_length":12,"model":"deepseek"}
[2025-05-28 16:08:12] Parameter validation passed
[2025-05-28 16:08:12] Files exist, loading...
[2025-05-28 16:08:12] Files loaded successfully
[2025-05-28 16:08:12] Starting bazi calculation
[2025-05-28 16:08:12] Bazi calculation successful
[2025-05-28 16:08:12] Building analysis prompt
[2025-05-28 16:08:12] Prompt built, length - Data: 526
[2025-05-28 16:08:12] Starting AI analysis with model: deepseek
[2025-05-28 16:08:51] AI analysis completed - Data: {"has_error":false,"content_length":8966}
[2025-05-28 16:08:51] Preparing final response
[2025-05-28 16:08:51] Saving to database
[2025-05-28 16:08:51] Database save successful, record ID: 56
[2025-05-28 16:08:51] Preparing success response
[2025-05-28 16:08:51] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 16:08:51] === result.php end ===
[2025-05-28 16:11:07] === result.php start ===
[2025-05-28 16:11:07] Request method - Data: "POST"
[2025-05-28 16:11:07] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/index.php","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"125","Host":"test.linn.pro"}
[2025-05-28 16:11:07] Starting main processing...
[2025-05-28 16:11:07] Raw input length - Data: 125
[2025-05-28 16:11:07] Raw input preview - Data: "{\"name\":\"阿里\",\"gender\":\"男\",\"year\":1986,\"month\":2,\"day\":2,\"hour\":11,\"minute\":50,\"question\":\"财运分析\",\"model\":\"qwen\"}"
[2025-05-28 16:11:07] Decoded input - Data: {"name":"阿里","gender":"男","year":1986,"month":2,"day":2,"hour":11,"minute":50,"question":"财运分析","model":"qwen"}
[2025-05-28 16:11:07] Field validation passed
[2025-05-28 16:11:07] Extracted parameters - Data: {"name":"阿里","gender":"男","birth":"1986-2-2 11:50","question_length":12,"model":"qwen"}
[2025-05-28 16:11:07] Parameter validation passed
[2025-05-28 16:11:07] Files exist, loading...
[2025-05-28 16:11:07] Files loaded successfully
[2025-05-28 16:11:07] Starting bazi calculation
[2025-05-28 16:11:07] Bazi calculation successful
[2025-05-28 16:11:07] Building analysis prompt
[2025-05-28 16:11:07] Prompt built, length - Data: 526
[2025-05-28 16:11:07] Starting AI analysis with model: qwen
[2025-05-28 16:11:49] AI analysis completed - Data: {"has_error":false,"content_length":5633}
[2025-05-28 16:11:49] Preparing final response
[2025-05-28 16:11:49] Saving to database
[2025-05-28 16:11:49] Database save successful, record ID: 58
[2025-05-28 16:11:49] Preparing success response
[2025-05-28 16:11:49] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 16:11:49] === result.php end ===
[2025-05-28 17:01:20] === result.php start ===
[2025-05-28 17:01:20] Request method - Data: "POST"
[2025-05-28 17:01:20] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"164","Host":"test.linn.pro"}
[2025-05-28 17:01:20] Starting main processing...
[2025-05-28 17:01:20] Raw input length - Data: 164
[2025-05-28 17:01:20] Raw input preview - Data: "{\"name\":\"林红\",\"gender\":\"女\",\"year\":1986,\"month\":10,\"day\":2,\"hour\":16,\"minute\":45,\"question\":\"今年的财运怎样\",\"model\":\"deepseek\",\"calendar_type\":\"农历\"}"
[2025-05-28 17:01:20] Decoded input - Data: {"name":"林红","gender":"女","year":1986,"month":10,"day":2,"hour":16,"minute":45,"question":"今年的财运怎样","model":"deepseek","calendar_type":"农历"}
[2025-05-28 17:01:20] Field validation passed
[2025-05-28 17:01:20] Extracted parameters - Data: {"name":"林红","gender":"女","birth":"1986-10-2 16:45","question_length":21,"model":"deepseek"}
[2025-05-28 17:01:20] Parameter validation passed
[2025-05-28 17:01:20] Files exist, loading...
[2025-05-28 17:01:20] Files loaded successfully
[2025-05-28 17:01:20] Starting bazi calculation
[2025-05-28 17:01:20] Bazi calculation successful
[2025-05-28 17:01:20] Building analysis prompt
[2025-05-28 17:01:20] Prompt built, length - Data: 535
[2025-05-28 17:01:20] Starting AI analysis with model: deepseek
[2025-05-28 17:01:42] AI analysis completed - Data: {"has_error":false,"content_length":4840}
[2025-05-28 17:01:42] Preparing final response
[2025-05-28 17:01:42] Saving to database
[2025-05-28 17:01:42] Database save successful, record ID: 60
[2025-05-28 17:01:42] Preparing success response
[2025-05-28 17:01:42] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 17:01:42] === result.php end ===
[2025-05-28 17:02:04] === result.php start ===
[2025-05-28 17:02:04] Request method - Data: "POST"
[2025-05-28 17:02:04] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"160","Host":"test.linn.pro"}
[2025-05-28 17:02:04] Starting main processing...
[2025-05-28 17:02:04] Raw input length - Data: 160
[2025-05-28 17:02:04] Raw input preview - Data: "{\"name\":\"林红\",\"gender\":\"女\",\"year\":1986,\"month\":10,\"day\":2,\"hour\":16,\"minute\":45,\"question\":\"今年的财运怎样\",\"model\":\"qwen\",\"calendar_type\":\"农历\"}"
[2025-05-28 17:02:04] Decoded input - Data: {"name":"林红","gender":"女","year":1986,"month":10,"day":2,"hour":16,"minute":45,"question":"今年的财运怎样","model":"qwen","calendar_type":"农历"}
[2025-05-28 17:02:04] Field validation passed
[2025-05-28 17:02:04] Extracted parameters - Data: {"name":"林红","gender":"女","birth":"1986-10-2 16:45","question_length":21,"model":"qwen"}
[2025-05-28 17:02:04] Parameter validation passed
[2025-05-28 17:02:04] Files exist, loading...
[2025-05-28 17:02:04] Files loaded successfully
[2025-05-28 17:02:04] Starting bazi calculation
[2025-05-28 17:02:04] Bazi calculation successful
[2025-05-28 17:02:04] Building analysis prompt
[2025-05-28 17:02:04] Prompt built, length - Data: 535
[2025-05-28 17:02:04] Starting AI analysis with model: qwen
[2025-05-28 17:02:46] AI analysis completed - Data: {"has_error":false,"content_length":5047}
[2025-05-28 17:02:46] Preparing final response
[2025-05-28 17:02:46] Saving to database
[2025-05-28 17:02:46] Database save successful, record ID: 62
[2025-05-28 17:02:46] Preparing success response
[2025-05-28 17:02:46] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-28 17:02:46] === result.php end ===
[2025-05-29 10:50:43] === result.php start ===
[2025-05-29 10:50:43] Request method - Data: "POST"
[2025-05-29 10:50:43] Request headers - Data: {"Cookie":"Hm_lvt_19f03e48c5501dc55033d85ebc385ecd=**********; HMACCOUNT=318C312FCE0BB4CB; Hm_lpvt_19f03e48c5501dc55033d85ebc385ecd=**********; PHPSESSID=e8n7vil4j8r3n4uo7mc6ve42c9","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/test.linn.pro\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/test.linn.pro","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"151","Host":"test.linn.pro"}
[2025-05-29 10:50:43] Starting main processing...
[2025-05-29 10:50:43] Raw input length - Data: 151
[2025-05-29 10:50:43] Raw input preview - Data: "{\"name\":\"林小\",\"gender\":\"男\",\"year\":1986,\"month\":12,\"day\":1,\"hour\":15,\"minute\":40,\"question\":\"我的事业发展前景如何？\",\"model\":\"deepseek\"}"
[2025-05-29 10:50:43] Decoded input - Data: {"name":"林小","gender":"男","year":1986,"month":12,"day":1,"hour":15,"minute":40,"question":"我的事业发展前景如何？","model":"deepseek"}
[2025-05-29 10:50:43] Field validation passed
[2025-05-29 10:50:43] Extracted parameters - Data: {"name":"林小","gender":"男","birth":"1986-12-1 15:40","question_length":33,"model":"deepseek"}
[2025-05-29 10:50:43] Parameter validation passed
[2025-05-29 10:50:43] Files exist, loading...
[2025-05-29 10:50:43] Files loaded successfully
[2025-05-29 10:50:43] Starting bazi calculation
[2025-05-29 10:50:43] Bazi calculation successful
[2025-05-29 10:50:43] Building analysis prompt
[2025-05-29 10:50:43] Prompt built, length - Data: 547
[2025-05-29 10:50:43] Starting AI analysis with model: deepseek
[2025-05-29 10:51:08] AI analysis completed - Data: {"has_error":false,"content_length":4131}
[2025-05-29 10:51:08] Preparing final response
[2025-05-29 10:51:08] Saving to database
[2025-05-29 10:51:08] Database save successful, record ID: 64
[2025-05-29 10:51:08] Preparing success response
[2025-05-29 10:51:08] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 10:51:08] === result.php end ===
[2025-05-29 10:54:10] === result.php start ===
[2025-05-29 10:54:10] Request method - Data: "POST"
[2025-05-29 10:54:10] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"154","Host":"www.6ird.com"}
[2025-05-29 10:54:10] Starting main processing...
[2025-05-29 10:54:10] Raw input length - Data: 154
[2025-05-29 10:54:10] Raw input preview - Data: "{\"name\":\"林小小\",\"gender\":\"男\",\"year\":1987,\"month\":11,\"day\":1,\"hour\":15,\"minute\":20,\"question\":\"我的事业发展前景如何？\",\"model\":\"deepseek\"}"
[2025-05-29 10:54:10] Decoded input - Data: {"name":"林小小","gender":"男","year":1987,"month":11,"day":1,"hour":15,"minute":20,"question":"我的事业发展前景如何？","model":"deepseek"}
[2025-05-29 10:54:10] Field validation passed
[2025-05-29 10:54:10] Extracted parameters - Data: {"name":"林小小","gender":"男","birth":"1987-11-1 15:20","question_length":33,"model":"deepseek"}
[2025-05-29 10:54:10] Parameter validation passed
[2025-05-29 10:54:10] Files exist, loading...
[2025-05-29 10:54:10] Files loaded successfully
[2025-05-29 10:54:10] Starting bazi calculation
[2025-05-29 10:54:10] Bazi calculation successful
[2025-05-29 10:54:10] Building analysis prompt
[2025-05-29 10:54:10] Prompt built, length - Data: 548
[2025-05-29 10:54:10] Starting AI analysis with model: deepseek
[2025-05-29 10:54:33] AI analysis completed - Data: {"has_error":false,"content_length":5335}
[2025-05-29 10:54:33] Preparing final response
[2025-05-29 10:54:33] Saving to database
[2025-05-29 10:54:33] Database save successful, record ID: 72
[2025-05-29 10:54:33] Preparing success response
[2025-05-29 10:54:33] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 10:54:33] === result.php end ===
[2025-05-29 11:04:23] === result.php start ===
[2025-05-29 11:04:23] Request method - Data: "POST"
[2025-05-29 11:04:23] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"154","Host":"www.6ird.com"}
[2025-05-29 11:04:23] Starting main processing...
[2025-05-29 11:04:23] Raw input length - Data: 154
[2025-05-29 11:04:23] Raw input preview - Data: "{\"name\":\"林小小\",\"gender\":\"男\",\"year\":1987,\"month\":11,\"day\":1,\"hour\":15,\"minute\":20,\"question\":\"我的事业发展前景如何？\",\"model\":\"deepseek\"}"
[2025-05-29 11:04:23] Decoded input - Data: {"name":"林小小","gender":"男","year":1987,"month":11,"day":1,"hour":15,"minute":20,"question":"我的事业发展前景如何？","model":"deepseek"}
[2025-05-29 11:04:23] Field validation passed
[2025-05-29 11:04:23] Extracted parameters - Data: {"name":"林小小","gender":"男","birth":"1987-11-1 15:20","question_length":33,"model":"deepseek"}
[2025-05-29 11:04:23] Parameter validation passed
[2025-05-29 11:04:23] Files exist, loading...
[2025-05-29 11:04:23] Files loaded successfully
[2025-05-29 11:04:23] Starting bazi calculation
[2025-05-29 11:04:23] Bazi calculation successful
[2025-05-29 11:04:23] Building analysis prompt
[2025-05-29 11:04:23] Prompt built, length - Data: 548
[2025-05-29 11:04:23] Starting AI analysis with model: deepseek
[2025-05-29 11:04:48] AI analysis completed - Data: {"has_error":false,"content_length":6640}
[2025-05-29 11:04:48] Preparing final response
[2025-05-29 11:04:48] Saving to database
[2025-05-29 11:04:48] Database save successful, record ID: 73
[2025-05-29 11:04:48] Preparing success response
[2025-05-29 11:04:48] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 11:04:48] === result.php end ===
[2025-05-29 11:15:40] === result.php start ===
[2025-05-29 11:15:40] Request method - Data: "POST"
[2025-05-29 11:15:40] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"142","Host":"www.6ird.com"}
[2025-05-29 11:15:40] Starting main processing...
[2025-05-29 11:15:40] Raw input length - Data: 142
[2025-05-29 11:15:40] Raw input preview - Data: "{\"name\":\"林一\",\"gender\":\"男\",\"year\":1987,\"month\":10,\"day\":1,\"hour\":14,\"minute\":40,\"question\":\"今年的财运怎样？\",\"model\":\"deepseek\"}"
[2025-05-29 11:15:40] Decoded input - Data: {"name":"林一","gender":"男","year":1987,"month":10,"day":1,"hour":14,"minute":40,"question":"今年的财运怎样？","model":"deepseek"}
[2025-05-29 11:15:40] Field validation passed
[2025-05-29 11:15:40] Extracted parameters - Data: {"name":"林一","gender":"男","birth":"1987-10-1 14:40","question_length":24,"model":"deepseek"}
[2025-05-29 11:15:40] Parameter validation passed
[2025-05-29 11:15:40] Files exist, loading...
[2025-05-29 11:15:40] Files loaded successfully
[2025-05-29 11:15:40] Starting bazi calculation
[2025-05-29 11:15:40] Bazi calculation successful
[2025-05-29 11:15:40] Building analysis prompt
[2025-05-29 11:15:40] Prompt built, length - Data: 538
[2025-05-29 11:15:40] Starting AI analysis with model: deepseek
[2025-05-29 11:16:40] AI analysis completed - Data: {"has_error":true,"content_length":726}
[2025-05-29 11:16:40] AI analysis has error - Data: "网络请求失败: Operation timed out after 60002 milliseconds with 5 bytes received"
[2025-05-29 11:16:40] Preparing final response
[2025-05-29 11:16:40] Saving to database
[2025-05-29 11:16:40] Database save successful, record ID: 74
[2025-05-29 11:16:40] Preparing success response
[2025-05-29 11:16:40] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 11:16:40] === result.php end ===
[2025-05-29 11:16:58] === result.php start ===
[2025-05-29 11:16:58] Request method - Data: "POST"
[2025-05-29 11:16:58] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"142","Host":"www.6ird.com"}
[2025-05-29 11:16:58] Starting main processing...
[2025-05-29 11:16:58] Raw input length - Data: 142
[2025-05-29 11:16:58] Raw input preview - Data: "{\"name\":\"林一\",\"gender\":\"男\",\"year\":1987,\"month\":10,\"day\":1,\"hour\":14,\"minute\":40,\"question\":\"今年的财运怎样？\",\"model\":\"deepseek\"}"
[2025-05-29 11:16:58] Decoded input - Data: {"name":"林一","gender":"男","year":1987,"month":10,"day":1,"hour":14,"minute":40,"question":"今年的财运怎样？","model":"deepseek"}
[2025-05-29 11:16:58] Field validation passed
[2025-05-29 11:16:58] Extracted parameters - Data: {"name":"林一","gender":"男","birth":"1987-10-1 14:40","question_length":24,"model":"deepseek"}
[2025-05-29 11:16:58] Parameter validation passed
[2025-05-29 11:16:58] Files exist, loading...
[2025-05-29 11:16:58] Files loaded successfully
[2025-05-29 11:16:58] Starting bazi calculation
[2025-05-29 11:16:58] Bazi calculation successful
[2025-05-29 11:16:58] Building analysis prompt
[2025-05-29 11:16:58] Prompt built, length - Data: 538
[2025-05-29 11:16:58] Starting AI analysis with model: deepseek
[2025-05-29 11:17:58] AI analysis completed - Data: {"has_error":true,"content_length":726}
[2025-05-29 11:17:58] AI analysis has error - Data: "网络请求失败: Operation timed out after 60002 milliseconds with 5 bytes received"
[2025-05-29 11:17:58] Preparing final response
[2025-05-29 11:17:58] Saving to database
[2025-05-29 11:17:58] Database save successful, record ID: 75
[2025-05-29 11:17:58] Preparing success response
[2025-05-29 11:17:58] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 11:17:58] === result.php end ===
[2025-05-29 11:18:19] === result.php start ===
[2025-05-29 11:18:19] Request method - Data: "POST"
[2025-05-29 11:18:19] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"138","Host":"www.6ird.com"}
[2025-05-29 11:18:19] Starting main processing...
[2025-05-29 11:18:19] Raw input length - Data: 138
[2025-05-29 11:18:19] Raw input preview - Data: "{\"name\":\"林一\",\"gender\":\"男\",\"year\":1987,\"month\":10,\"day\":1,\"hour\":14,\"minute\":40,\"question\":\"今年的财运怎样？\",\"model\":\"qwen\"}"
[2025-05-29 11:18:19] Decoded input - Data: {"name":"林一","gender":"男","year":1987,"month":10,"day":1,"hour":14,"minute":40,"question":"今年的财运怎样？","model":"qwen"}
[2025-05-29 11:18:19] Field validation passed
[2025-05-29 11:18:19] Extracted parameters - Data: {"name":"林一","gender":"男","birth":"1987-10-1 14:40","question_length":24,"model":"qwen"}
[2025-05-29 11:18:19] Parameter validation passed
[2025-05-29 11:18:19] Files exist, loading...
[2025-05-29 11:18:19] Files loaded successfully
[2025-05-29 11:18:19] Starting bazi calculation
[2025-05-29 11:18:19] Bazi calculation successful
[2025-05-29 11:18:19] Building analysis prompt
[2025-05-29 11:18:19] Prompt built, length - Data: 538
[2025-05-29 11:18:19] Starting AI analysis with model: qwen
[2025-05-29 11:18:45] AI analysis completed - Data: {"has_error":false,"content_length":3064}
[2025-05-29 11:18:45] Preparing final response
[2025-05-29 11:18:45] Saving to database
[2025-05-29 11:18:45] Database save successful, record ID: 76
[2025-05-29 11:18:45] Preparing success response
[2025-05-29 11:18:45] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 11:18:45] === result.php end ===
[2025-05-29 11:19:50] === result.php start ===
[2025-05-29 11:19:50] Request method - Data: "POST"
[2025-05-29 11:19:50] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"142","Host":"www.6ird.com"}
[2025-05-29 11:19:50] Starting main processing...
[2025-05-29 11:19:50] Raw input length - Data: 142
[2025-05-29 11:19:50] Raw input preview - Data: "{\"name\":\"林一\",\"gender\":\"男\",\"year\":1987,\"month\":10,\"day\":1,\"hour\":14,\"minute\":40,\"question\":\"今年的财运怎样？\",\"model\":\"deepseek\"}"
[2025-05-29 11:19:50] Decoded input - Data: {"name":"林一","gender":"男","year":1987,"month":10,"day":1,"hour":14,"minute":40,"question":"今年的财运怎样？","model":"deepseek"}
[2025-05-29 11:19:50] Field validation passed
[2025-05-29 11:19:50] Extracted parameters - Data: {"name":"林一","gender":"男","birth":"1987-10-1 14:40","question_length":24,"model":"deepseek"}
[2025-05-29 11:19:50] Parameter validation passed
[2025-05-29 11:19:50] Files exist, loading...
[2025-05-29 11:19:50] Files loaded successfully
[2025-05-29 11:19:50] Starting bazi calculation
[2025-05-29 11:19:50] Bazi calculation successful
[2025-05-29 11:19:50] Building analysis prompt
[2025-05-29 11:19:50] Prompt built, length - Data: 538
[2025-05-29 11:19:50] Starting AI analysis with model: deepseek
[2025-05-29 11:20:40] AI analysis completed - Data: {"has_error":false,"content_length":2915}
[2025-05-29 11:20:40] Preparing final response
[2025-05-29 11:20:40] Saving to database
[2025-05-29 11:20:40] Database save successful, record ID: 77
[2025-05-29 11:20:40] Preparing success response
[2025-05-29 11:20:40] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 11:20:40] === result.php end ===
[2025-05-29 11:36:51] === result.php start ===
[2025-05-29 11:36:51] Request method - Data: "POST"
[2025-05-29 11:36:51] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"140","Host":"www.6ird.com"}
[2025-05-29 11:36:51] Starting main processing...
[2025-05-29 11:36:51] Raw input length - Data: 140
[2025-05-29 11:36:51] Raw input preview - Data: "{\"name\":\"林一\",\"gender\":\"男\",\"year\":1987,\"month\":10,\"day\":1,\"hour\":14,\"minute\":40,\"question\":\"2025的财运怎样？\",\"model\":\"deepseek\"}"
[2025-05-29 11:36:51] Decoded input - Data: {"name":"林一","gender":"男","year":1987,"month":10,"day":1,"hour":14,"minute":40,"question":"2025的财运怎样？","model":"deepseek"}
[2025-05-29 11:36:51] Field validation passed
[2025-05-29 11:36:51] Extracted parameters - Data: {"name":"林一","gender":"男","birth":"1987-10-1 14:40","question_length":22,"model":"deepseek"}
[2025-05-29 11:36:51] Parameter validation passed
[2025-05-29 11:36:51] Files exist, loading...
[2025-05-29 11:36:51] Files loaded successfully
[2025-05-29 11:36:51] Starting bazi calculation
[2025-05-29 11:36:51] Bazi calculation successful
[2025-05-29 11:36:51] Building analysis prompt
[2025-05-29 11:36:51] Prompt built, length - Data: 536
[2025-05-29 11:36:51] Starting AI analysis with model: deepseek
[2025-05-29 11:37:51] AI analysis completed - Data: {"has_error":true,"content_length":726}
[2025-05-29 11:37:51] AI analysis has error - Data: "网络请求失败: Operation timed out after 60002 milliseconds with 5 bytes received"
[2025-05-29 11:37:51] Preparing final response
[2025-05-29 11:37:51] Saving to database
[2025-05-29 11:37:51] Database save successful, record ID: 78
[2025-05-29 11:37:51] Preparing success response
[2025-05-29 11:37:51] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 11:37:51] === result.php end ===
[2025-05-29 11:59:17] === result.php start ===
[2025-05-29 11:59:17] Request method - Data: "POST"
[2025-05-29 11:59:17] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"140","Host":"www.6ird.com"}
[2025-05-29 11:59:17] Starting main processing...
[2025-05-29 11:59:17] Raw input length - Data: 140
[2025-05-29 11:59:17] Raw input preview - Data: "{\"name\":\"林一\",\"gender\":\"男\",\"year\":1987,\"month\":10,\"day\":1,\"hour\":14,\"minute\":40,\"question\":\"2025的财运怎样？\",\"model\":\"deepseek\"}"
[2025-05-29 11:59:17] Decoded input - Data: {"name":"林一","gender":"男","year":1987,"month":10,"day":1,"hour":14,"minute":40,"question":"2025的财运怎样？","model":"deepseek"}
[2025-05-29 11:59:17] Field validation passed
[2025-05-29 11:59:17] Extracted parameters - Data: {"name":"林一","gender":"男","birth":"1987-10-1 14:40","question_length":22,"model":"deepseek"}
[2025-05-29 11:59:17] Parameter validation passed
[2025-05-29 11:59:17] Files exist, loading...
[2025-05-29 11:59:17] Files loaded successfully
[2025-05-29 11:59:17] Starting bazi calculation
[2025-05-29 11:59:17] Bazi calculation successful
[2025-05-29 11:59:17] Building analysis prompt
[2025-05-29 11:59:17] Prompt built, length - Data: 536
[2025-05-29 11:59:17] Starting AI analysis with model: deepseek
[2025-05-29 12:00:17] AI analysis completed - Data: {"has_error":false,"content_length":6157}
[2025-05-29 12:00:17] Preparing final response
[2025-05-29 12:00:17] Saving to database
[2025-05-29 12:00:17] Database save successful, record ID: 79
[2025-05-29 12:00:17] Preparing success response
[2025-05-29 12:00:17] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 12:00:17] === result.php end ===
[2025-05-29 12:11:26] === result.php start ===
[2025-05-29 12:11:26] Request method - Data: "POST"
[2025-05-29 12:11:26] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"130","Host":"www.6ird.com"}
[2025-05-29 12:11:26] Starting main processing...
[2025-05-29 12:11:26] Raw input length - Data: 130
[2025-05-29 12:11:26] Raw input preview - Data: "{\"name\":\"林二\",\"gender\":\"男\",\"year\":1986,\"month\":1,\"day\":2,\"hour\":0,\"minute\":5,\"question\":\"财运怎样？\",\"model\":\"deepseek\"}"
[2025-05-29 12:11:26] Decoded input - Data: {"name":"林二","gender":"男","year":1986,"month":1,"day":2,"hour":0,"minute":5,"question":"财运怎样？","model":"deepseek"}
[2025-05-29 12:11:26] Field validation passed
[2025-05-29 12:11:26] Extracted parameters - Data: {"name":"林二","gender":"男","birth":"1986-1-2 0:5","question_length":15,"model":"deepseek"}
[2025-05-29 12:11:26] Parameter validation passed
[2025-05-29 12:11:26] Files exist, loading...
[2025-05-29 12:11:26] Files loaded successfully
[2025-05-29 12:11:26] Starting bazi calculation
[2025-05-29 12:11:26] Bazi calculation successful
[2025-05-29 12:11:26] Building analysis prompt
[2025-05-29 12:11:26] Prompt built, length - Data: 529
[2025-05-29 12:11:26] Starting AI analysis with model: deepseek
[2025-05-29 12:12:13] AI analysis completed - Data: {"has_error":false,"content_length":4830}
[2025-05-29 12:12:13] Preparing final response
[2025-05-29 12:12:13] Saving to database
[2025-05-29 12:12:13] Database save successful, record ID: 80
[2025-05-29 12:12:13] Preparing success response
[2025-05-29 12:12:13] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 12:12:13] === result.php end ===
[2025-05-29 12:13:04] === result.php start ===
[2025-05-29 12:13:04] Request method - Data: "POST"
[2025-05-29 12:13:04] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"126","Host":"www.6ird.com"}
[2025-05-29 12:13:04] Starting main processing...
[2025-05-29 12:13:04] Raw input length - Data: 126
[2025-05-29 12:13:04] Raw input preview - Data: "{\"name\":\"林二\",\"gender\":\"男\",\"year\":1986,\"month\":1,\"day\":2,\"hour\":0,\"minute\":5,\"question\":\"财运怎样？\",\"model\":\"qwen\"}"
[2025-05-29 12:13:04] Decoded input - Data: {"name":"林二","gender":"男","year":1986,"month":1,"day":2,"hour":0,"minute":5,"question":"财运怎样？","model":"qwen"}
[2025-05-29 12:13:04] Field validation passed
[2025-05-29 12:13:04] Extracted parameters - Data: {"name":"林二","gender":"男","birth":"1986-1-2 0:5","question_length":15,"model":"qwen"}
[2025-05-29 12:13:04] Parameter validation passed
[2025-05-29 12:13:04] Files exist, loading...
[2025-05-29 12:13:04] Files loaded successfully
[2025-05-29 12:13:04] Starting bazi calculation
[2025-05-29 12:13:04] Bazi calculation successful
[2025-05-29 12:13:04] Building analysis prompt
[2025-05-29 12:13:04] Prompt built, length - Data: 529
[2025-05-29 12:13:04] Starting AI analysis with model: qwen
[2025-05-29 12:13:51] AI analysis completed - Data: {"has_error":false,"content_length":6706}
[2025-05-29 12:13:51] Preparing final response
[2025-05-29 12:13:51] Saving to database
[2025-05-29 12:13:51] Database save successful, record ID: 81
[2025-05-29 12:13:51] Preparing success response
[2025-05-29 12:13:51] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 12:13:51] === result.php end ===
[2025-05-29 12:40:45] === result.php start ===
[2025-05-29 12:40:45] Request method - Data: "POST"
[2025-05-29 12:40:45] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"126","Host":"www.6ird.com"}
[2025-05-29 12:40:45] Starting main processing...
[2025-05-29 12:40:45] Raw input length - Data: 126
[2025-05-29 12:40:45] Raw input preview - Data: "{\"name\":\"林二\",\"gender\":\"男\",\"year\":1986,\"month\":1,\"day\":2,\"hour\":0,\"minute\":5,\"question\":\"财运怎样？\",\"model\":\"qwen\"}"
[2025-05-29 12:40:45] Decoded input - Data: {"name":"林二","gender":"男","year":1986,"month":1,"day":2,"hour":0,"minute":5,"question":"财运怎样？","model":"qwen"}
[2025-05-29 12:40:45] Field validation passed
[2025-05-29 12:40:45] Extracted parameters - Data: {"name":"林二","gender":"男","birth":"1986-1-2 0:5","question_length":15,"model":"qwen"}
[2025-05-29 12:40:45] Parameter validation passed
[2025-05-29 12:40:45] Files exist, loading...
[2025-05-29 12:40:45] Files loaded successfully
[2025-05-29 12:40:45] Starting bazi calculation
[2025-05-29 12:40:45] Bazi calculation successful
[2025-05-29 12:40:45] Building analysis prompt
[2025-05-29 12:40:45] Prompt built, length - Data: 529
[2025-05-29 12:40:45] Starting AI analysis with model: qwen
[2025-05-29 12:41:31] AI analysis completed - Data: {"has_error":false,"content_length":5465}
[2025-05-29 12:41:31] Preparing final response
[2025-05-29 12:41:31] Saving to database
[2025-05-29 12:41:31] Database save successful, record ID: 82
[2025-05-29 12:41:31] Preparing success response
[2025-05-29 12:41:31] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 12:41:31] === result.php end ===
[2025-05-29 12:44:07] === result.php start ===
[2025-05-29 12:44:07] Request method - Data: "POST"
[2025-05-29 12:44:07] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v; wordpress_logged_in_2321612242773d5a13a4c1457aee841c=6ird%7C1749703278%7C6Bvzxxnqa3ROuZrnMzZ3QW15zs1DlJgLQOKqMZJgOjO%7C8c93b0fb09b6f524d0c30cd82c82f8c1d4058d0b30ccf52d83c9f1b0d18a76ed; __eoi=ID=0003676fa60e4351:T=**********:RT=**********:S=AA-AfjYweomYFv2bRYjuDYo4DPTN","Priority":"u=1, i","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"133","Host":"www.6ird.com"}
[2025-05-29 12:44:07] Starting main processing...
[2025-05-29 12:44:07] Raw input length - Data: 133
[2025-05-29 12:44:07] Raw input preview - Data: "{\"name\":\"林三\",\"gender\":\"男\",\"year\":1986,\"month\":10,\"day\":2,\"hour\":13,\"minute\":45,\"question\":\"财运怎样？\",\"model\":\"deepseek\"}"
[2025-05-29 12:44:07] Decoded input - Data: {"name":"林三","gender":"男","year":1986,"month":10,"day":2,"hour":13,"minute":45,"question":"财运怎样？","model":"deepseek"}
[2025-05-29 12:44:07] Field validation passed
[2025-05-29 12:44:07] Extracted parameters - Data: {"name":"林三","gender":"男","birth":"1986-10-2 13:45","question_length":15,"model":"deepseek"}
[2025-05-29 12:44:07] Parameter validation passed
[2025-05-29 12:44:07] Files exist, loading...
[2025-05-29 12:44:07] Files loaded successfully
[2025-05-29 12:44:07] Starting bazi calculation
[2025-05-29 12:44:07] Bazi calculation successful
[2025-05-29 12:44:07] Building analysis prompt
[2025-05-29 12:44:07] Prompt built, length - Data: 527
[2025-05-29 12:44:07] Starting AI analysis with model: deepseek
[2025-05-29 12:45:34] AI analysis completed - Data: {"has_error":true,"content_length":726}
[2025-05-29 12:45:34] AI analysis has error - Data: "网络请求失败: Operation timed out after 60002 milliseconds with 5 bytes received"
[2025-05-29 12:45:34] Preparing final response
[2025-05-29 12:45:34] Saving to database
[2025-05-29 12:45:34] Database save successful, record ID: 83
[2025-05-29 12:45:34] Preparing success response
[2025-05-29 12:45:34] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 12:45:34] === result.php end ===
[2025-05-29 12:49:19] === result.php start ===
[2025-05-29 12:49:19] Request method - Data: "POST"
[2025-05-29 12:49:19] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v; wordpress_logged_in_2321612242773d5a13a4c1457aee841c=6ird%7C1749703278%7C6Bvzxxnqa3ROuZrnMzZ3QW15zs1DlJgLQOKqMZJgOjO%7C8c93b0fb09b6f524d0c30cd82c82f8c1d4058d0b30ccf52d83c9f1b0d18a76ed; __eoi=ID=0003676fa60e4351:T=**********:RT=**********:S=AA-AfjYweomYFv2bRYjuDYo4DPTN","Priority":"u=1, i","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"133","Host":"www.6ird.com"}
[2025-05-29 12:49:19] Starting main processing...
[2025-05-29 12:49:19] Raw input length - Data: 133
[2025-05-29 12:49:19] Raw input preview - Data: "{\"name\":\"林三\",\"gender\":\"男\",\"year\":1986,\"month\":10,\"day\":2,\"hour\":13,\"minute\":45,\"question\":\"财运怎样？\",\"model\":\"deepseek\"}"
[2025-05-29 12:49:19] Decoded input - Data: {"name":"林三","gender":"男","year":1986,"month":10,"day":2,"hour":13,"minute":45,"question":"财运怎样？","model":"deepseek"}
[2025-05-29 12:49:19] Field validation passed
[2025-05-29 12:49:19] Extracted parameters - Data: {"name":"林三","gender":"男","birth":"1986-10-2 13:45","question_length":15,"model":"deepseek"}
[2025-05-29 12:49:19] Parameter validation passed
[2025-05-29 12:49:19] Files exist, loading...
[2025-05-29 12:49:19] Files loaded successfully
[2025-05-29 12:49:19] Starting bazi calculation
[2025-05-29 12:49:19] Bazi calculation successful
[2025-05-29 12:49:19] Building analysis prompt
[2025-05-29 12:49:19] Prompt built, length - Data: 527
[2025-05-29 12:49:19] Starting AI analysis with model: deepseek
[2025-05-29 12:49:57] AI analysis completed - Data: {"has_error":false,"content_length":5117}
[2025-05-29 12:49:57] Preparing final response
[2025-05-29 12:49:57] Saving to database
[2025-05-29 12:49:57] Database save successful, record ID: 84
[2025-05-29 12:49:57] Preparing success response
[2025-05-29 12:49:57] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 12:49:57] === result.php end ===
[2025-05-29 12:49:59] === result.php start ===
[2025-05-29 12:49:59] Request method - Data: "POST"
[2025-05-29 12:49:59] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v; wordpress_logged_in_2321612242773d5a13a4c1457aee841c=6ird%7C1749703278%7C6Bvzxxnqa3ROuZrnMzZ3QW15zs1DlJgLQOKqMZJgOjO%7C8c93b0fb09b6f524d0c30cd82c82f8c1d4058d0b30ccf52d83c9f1b0d18a76ed; __eoi=ID=0003676fa60e4351:T=**********:RT=**********:S=AA-AfjYweomYFv2bRYjuDYo4DPTN","Priority":"u=1, i","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"130","Host":"www.6ird.com"}
[2025-05-29 12:49:59] Starting main processing...
[2025-05-29 12:49:59] Raw input length - Data: 130
[2025-05-29 12:49:59] Raw input preview - Data: "{\"name\":\"林珊\",\"gender\":\"女\",\"year\":1985,\"month\":2,\"day\":2,\"hour\":1,\"minute\":5,\"question\":\"财运怎样？\",\"model\":\"deepseek\"}"
[2025-05-29 12:49:59] Decoded input - Data: {"name":"林珊","gender":"女","year":1985,"month":2,"day":2,"hour":1,"minute":5,"question":"财运怎样？","model":"deepseek"}
[2025-05-29 12:49:59] Field validation passed
[2025-05-29 12:49:59] Extracted parameters - Data: {"name":"林珊","gender":"女","birth":"1985-2-2 1:5","question_length":15,"model":"deepseek"}
[2025-05-29 12:49:59] Parameter validation passed
[2025-05-29 12:49:59] Files exist, loading...
[2025-05-29 12:49:59] Files loaded successfully
[2025-05-29 12:49:59] Starting bazi calculation
[2025-05-29 12:49:59] Bazi calculation successful
[2025-05-29 12:49:59] Building analysis prompt
[2025-05-29 12:49:59] Prompt built, length - Data: 529
[2025-05-29 12:49:59] Starting AI analysis with model: deepseek
[2025-05-29 12:50:26] AI analysis completed - Data: {"has_error":false,"content_length":6698}
[2025-05-29 12:50:26] Preparing final response
[2025-05-29 12:50:26] Saving to database
[2025-05-29 12:50:26] Database save successful, record ID: 85
[2025-05-29 12:50:26] Preparing success response
[2025-05-29 12:50:26] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 12:50:26] === result.php end ===
[2025-05-29 12:50:34] === result.php start ===
[2025-05-29 12:50:34] Request method - Data: "POST"
[2025-05-29 12:50:34] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v; wordpress_logged_in_2321612242773d5a13a4c1457aee841c=6ird%7C1749703278%7C6Bvzxxnqa3ROuZrnMzZ3QW15zs1DlJgLQOKqMZJgOjO%7C8c93b0fb09b6f524d0c30cd82c82f8c1d4058d0b30ccf52d83c9f1b0d18a76ed; __eoi=ID=0003676fa60e4351:T=**********:RT=**********:S=AA-AfjYweomYFv2bRYjuDYo4DPTN","Priority":"u=1, i","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"130","Host":"www.6ird.com"}
[2025-05-29 12:50:34] Starting main processing...
[2025-05-29 12:50:34] Raw input length - Data: 130
[2025-05-29 12:50:34] Raw input preview - Data: "{\"name\":\"林珊\",\"gender\":\"女\",\"year\":1985,\"month\":2,\"day\":2,\"hour\":1,\"minute\":5,\"question\":\"财运怎样？\",\"model\":\"deepseek\"}"
[2025-05-29 12:50:34] Decoded input - Data: {"name":"林珊","gender":"女","year":1985,"month":2,"day":2,"hour":1,"minute":5,"question":"财运怎样？","model":"deepseek"}
[2025-05-29 12:50:34] Field validation passed
[2025-05-29 12:50:34] Extracted parameters - Data: {"name":"林珊","gender":"女","birth":"1985-2-2 1:5","question_length":15,"model":"deepseek"}
[2025-05-29 12:50:34] Parameter validation passed
[2025-05-29 12:50:34] Files exist, loading...
[2025-05-29 12:50:34] Files loaded successfully
[2025-05-29 12:50:34] Starting bazi calculation
[2025-05-29 12:50:34] Bazi calculation successful
[2025-05-29 12:50:34] Building analysis prompt
[2025-05-29 12:50:34] Prompt built, length - Data: 529
[2025-05-29 12:50:34] Starting AI analysis with model: deepseek
[2025-05-29 12:51:26] AI analysis completed - Data: {"has_error":false,"content_length":3277}
[2025-05-29 12:51:26] Preparing final response
[2025-05-29 12:51:26] Saving to database
[2025-05-29 12:51:26] Database save successful, record ID: 86
[2025-05-29 12:51:26] Preparing success response
[2025-05-29 12:51:26] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 12:51:26] === result.php end ===
[2025-05-29 13:01:08] === result.php start ===
[2025-05-29 13:01:08] Request method - Data: "POST"
[2025-05-29 13:01:08] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v; wordpress_logged_in_2321612242773d5a13a4c1457aee841c=6ird%7C1749703278%7C6Bvzxxnqa3ROuZrnMzZ3QW15zs1DlJgLQOKqMZJgOjO%7C8c93b0fb09b6f524d0c30cd82c82f8c1d4058d0b30ccf52d83c9f1b0d18a76ed; __eoi=ID=0003676fa60e4351:T=**********:RT=**********:S=AA-AfjYweomYFv2bRYjuDYo4DPTN","Priority":"u=1, i","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"130","Host":"www.6ird.com"}
[2025-05-29 13:01:08] Starting main processing...
[2025-05-29 13:01:08] Raw input length - Data: 130
[2025-05-29 13:01:08] Raw input preview - Data: "{\"name\":\"林珊\",\"gender\":\"女\",\"year\":1985,\"month\":2,\"day\":2,\"hour\":1,\"minute\":5,\"question\":\"财运怎样？\",\"model\":\"deepseek\"}"
[2025-05-29 13:01:08] Decoded input - Data: {"name":"林珊","gender":"女","year":1985,"month":2,"day":2,"hour":1,"minute":5,"question":"财运怎样？","model":"deepseek"}
[2025-05-29 13:01:08] Field validation passed
[2025-05-29 13:01:08] Extracted parameters - Data: {"name":"林珊","gender":"女","birth":"1985-2-2 1:5","question_length":15,"model":"deepseek"}
[2025-05-29 13:01:08] Parameter validation passed
[2025-05-29 13:01:08] Files exist, loading...
[2025-05-29 13:01:08] Files loaded successfully
[2025-05-29 13:01:08] Starting bazi calculation
[2025-05-29 13:01:08] Bazi calculation successful
[2025-05-29 13:01:08] Building analysis prompt
[2025-05-29 13:01:08] Prompt built, length - Data: 529
[2025-05-29 13:01:08] Starting AI analysis with model: deepseek
[2025-05-29 13:01:31] === result.php start ===
[2025-05-29 13:01:31] Request method - Data: "POST"
[2025-05-29 13:01:31] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v; wordpress_logged_in_2321612242773d5a13a4c1457aee841c=6ird%7C1749703278%7C6Bvzxxnqa3ROuZrnMzZ3QW15zs1DlJgLQOKqMZJgOjO%7C8c93b0fb09b6f524d0c30cd82c82f8c1d4058d0b30ccf52d83c9f1b0d18a76ed; __eoi=ID=0003676fa60e4351:T=**********:RT=**********:S=AA-AfjYweomYFv2bRYjuDYo4DPTN","Priority":"u=1, i","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"128","Host":"www.6ird.com"}
[2025-05-29 13:01:31] Starting main processing...
[2025-05-29 13:01:31] Raw input length - Data: 128
[2025-05-29 13:01:31] Raw input preview - Data: "{\"name\":\"林大\",\"gender\":\"男\",\"year\":1987,\"month\":1,\"day\":2,\"hour\":1,\"minute\":10,\"question\":\"财运怎样\",\"model\":\"deepseek\"}"
[2025-05-29 13:01:31] Decoded input - Data: {"name":"林大","gender":"男","year":1987,"month":1,"day":2,"hour":1,"minute":10,"question":"财运怎样","model":"deepseek"}
[2025-05-29 13:01:31] Field validation passed
[2025-05-29 13:01:31] Extracted parameters - Data: {"name":"林大","gender":"男","birth":"1987-1-2 1:10","question_length":12,"model":"deepseek"}
[2025-05-29 13:01:31] Parameter validation passed
[2025-05-29 13:01:31] Files exist, loading...
[2025-05-29 13:01:31] Files loaded successfully
[2025-05-29 13:01:31] Starting bazi calculation
[2025-05-29 13:01:31] Bazi calculation successful
[2025-05-29 13:01:31] Building analysis prompt
[2025-05-29 13:01:31] Prompt built, length - Data: 524
[2025-05-29 13:01:31] Starting AI analysis with model: deepseek
[2025-05-29 13:01:45] AI analysis completed - Data: {"has_error":false,"content_length":5518}
[2025-05-29 13:01:45] Preparing final response
[2025-05-29 13:01:45] Saving to database
[2025-05-29 13:01:45] Database save successful, record ID: 87
[2025-05-29 13:01:45] Preparing success response
[2025-05-29 13:01:45] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 13:01:45] === result.php end ===
[2025-05-29 13:02:31] AI analysis completed - Data: {"has_error":true,"content_length":726}
[2025-05-29 13:02:31] AI analysis has error - Data: "网络请求失败: Operation timed out after 60002 milliseconds with 5 bytes received"
[2025-05-29 13:02:31] Preparing final response
[2025-05-29 13:02:31] Saving to database
[2025-05-29 13:02:31] Database save successful, record ID: 88
[2025-05-29 13:02:31] Preparing success response
[2025-05-29 13:02:31] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 13:02:31] === result.php end ===
[2025-05-29 13:02:53] === result.php start ===
[2025-05-29 13:02:53] Request method - Data: "POST"
[2025-05-29 13:02:53] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v; wordpress_logged_in_2321612242773d5a13a4c1457aee841c=6ird%7C1749703278%7C6Bvzxxnqa3ROuZrnMzZ3QW15zs1DlJgLQOKqMZJgOjO%7C8c93b0fb09b6f524d0c30cd82c82f8c1d4058d0b30ccf52d83c9f1b0d18a76ed; __eoi=ID=0003676fa60e4351:T=**********:RT=1748494950:S=AA-AfjYweomYFv2bRYjuDYo4DPTN","Priority":"u=1, i","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"133","Host":"www.6ird.com"}
[2025-05-29 13:02:53] Starting main processing...
[2025-05-29 13:02:53] Raw input length - Data: 133
[2025-05-29 13:02:53] Raw input preview - Data: "{\"name\":\"李大\",\"gender\":\"男\",\"year\":1986,\"month\":2,\"day\":3,\"hour\":0,\"minute\":5,\"question\":\"发展前景如何\",\"model\":\"deepseek\"}"
[2025-05-29 13:02:53] Decoded input - Data: {"name":"李大","gender":"男","year":1986,"month":2,"day":3,"hour":0,"minute":5,"question":"发展前景如何","model":"deepseek"}
[2025-05-29 13:02:53] Field validation passed
[2025-05-29 13:02:53] Extracted parameters - Data: {"name":"李大","gender":"男","birth":"1986-2-3 0:5","question_length":18,"model":"deepseek"}
[2025-05-29 13:02:53] Parameter validation passed
[2025-05-29 13:02:53] Files exist, loading...
[2025-05-29 13:02:53] Files loaded successfully
[2025-05-29 13:02:53] Starting bazi calculation
[2025-05-29 13:02:53] Bazi calculation successful
[2025-05-29 13:02:53] Building analysis prompt
[2025-05-29 13:02:53] Prompt built, length - Data: 532
[2025-05-29 13:02:53] Starting AI analysis with model: deepseek
[2025-05-29 13:03:39] AI analysis completed - Data: {"has_error":false,"content_length":7604}
[2025-05-29 13:03:39] Preparing final response
[2025-05-29 13:03:39] Saving to database
[2025-05-29 13:03:39] Database save successful, record ID: 89
[2025-05-29 13:03:39] Preparing success response
[2025-05-29 13:03:39] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 13:03:39] === result.php end ===
[2025-05-29 13:04:12] === result.php start ===
[2025-05-29 13:04:12] Request method - Data: "POST"
[2025-05-29 13:04:12] Request headers - Data: {"Cookie":"__eoi=ID=1135192837c73241:T=1748495010:RT=1748495010:S=AA-AfjZyEp1sXviRYszjuM7OUk7u; __gads=ID=a72160311048aa1a:T=1748495010:RT=1748495010:S=ALNI_MY0TPE96YTdDiJWGPbcIrDsUxk2qg; __gpi=UID=0000110514e636b5:T=1748495010:RT=1748495010:S=ALNI_MZe9dKyfoeH8k00oTXFxoZthYqZug; PHPSESSID=ipc7lrrerus4v899164c944mal; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dbrowse%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=1731853704","Sec-Fetch-Dest":"empty","Content-Length":"139","Referer":"https:\/\/www.6ird.com\/suan\/","User-Agent":"Mozilla\/5.0 (iPhone; CPU iPhone OS 18_5_0 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) CriOS\/137.0.7151.51 Mobile\/15E148 Safari\/604.1","Origin":"https:\/\/www.6ird.com","Sec-Fetch-Mode":"cors","Accept-Encoding":"gzip, deflate, br","Accept-Language":"zh-CN,zh-Hans;q=0.9","Sec-Fetch-Site":"same-origin","Accept":"*\/*","Content-Type":"application\/json","Host":"www.6ird.com"}
[2025-05-29 13:04:12] Starting main processing...
[2025-05-29 13:04:12] Raw input length - Data: 139
[2025-05-29 13:04:12] Raw input preview - Data: "{\"name\":\"林珊\",\"gender\":\"女\",\"year\":1985,\"month\":1,\"day\":1,\"hour\":3,\"minute\":5,\"question\":\"事业发展前景如何\",\"model\":\"deepseek\"}"
[2025-05-29 13:04:12] Decoded input - Data: {"name":"林珊","gender":"女","year":1985,"month":1,"day":1,"hour":3,"minute":5,"question":"事业发展前景如何","model":"deepseek"}
[2025-05-29 13:04:12] Field validation passed
[2025-05-29 13:04:12] Extracted parameters - Data: {"name":"林珊","gender":"女","birth":"1985-1-1 3:5","question_length":24,"model":"deepseek"}
[2025-05-29 13:04:12] Parameter validation passed
[2025-05-29 13:04:12] Files exist, loading...
[2025-05-29 13:04:12] Files loaded successfully
[2025-05-29 13:04:12] Starting bazi calculation
[2025-05-29 13:04:12] Bazi calculation successful
[2025-05-29 13:04:12] Building analysis prompt
[2025-05-29 13:04:12] Prompt built, length - Data: 538
[2025-05-29 13:04:12] Starting AI analysis with model: deepseek
[2025-05-29 13:04:32] AI analysis completed - Data: {"has_error":false,"content_length":3043}
[2025-05-29 13:04:32] Preparing final response
[2025-05-29 13:04:32] Saving to database
[2025-05-29 13:04:32] Database save successful, record ID: 90
[2025-05-29 13:04:32] Preparing success response
[2025-05-29 13:04:32] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 13:04:32] === result.php end ===
[2025-05-29 13:08:36] === result.php start ===
[2025-05-29 13:08:36] Request method - Data: "POST"
[2025-05-29 13:08:36] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v; wordpress_logged_in_2321612242773d5a13a4c1457aee841c=6ird%7C1749703278%7C6Bvzxxnqa3ROuZrnMzZ3QW15zs1DlJgLQOKqMZJgOjO%7C8c93b0fb09b6f524d0c30cd82c82f8c1d4058d0b30ccf52d83c9f1b0d18a76ed; __eoi=ID=0003676fa60e4351:T=**********:RT=1748494950:S=AA-AfjYweomYFv2bRYjuDYo4DPTN","Priority":"u=1, i","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"133","Host":"www.6ird.com"}
[2025-05-29 13:08:36] Starting main processing...
[2025-05-29 13:08:36] Raw input length - Data: 133
[2025-05-29 13:08:36] Raw input preview - Data: "{\"name\":\"李大\",\"gender\":\"男\",\"year\":1986,\"month\":2,\"day\":3,\"hour\":0,\"minute\":5,\"question\":\"发展前景如何\",\"model\":\"deepseek\"}"
[2025-05-29 13:08:36] Decoded input - Data: {"name":"李大","gender":"男","year":1986,"month":2,"day":3,"hour":0,"minute":5,"question":"发展前景如何","model":"deepseek"}
[2025-05-29 13:08:36] Field validation passed
[2025-05-29 13:08:36] Extracted parameters - Data: {"name":"李大","gender":"男","birth":"1986-2-3 0:5","question_length":18,"model":"deepseek"}
[2025-05-29 13:08:36] Parameter validation passed
[2025-05-29 13:08:36] Files exist, loading...
[2025-05-29 13:08:36] Files loaded successfully
[2025-05-29 13:08:36] Starting bazi calculation
[2025-05-29 13:08:36] Bazi calculation successful
[2025-05-29 13:08:36] Building analysis prompt
[2025-05-29 13:08:36] Prompt built, length - Data: 532
[2025-05-29 13:08:36] Starting AI analysis with model: deepseek
[2025-05-29 13:09:19] AI analysis completed - Data: {"has_error":false,"content_length":3122}
[2025-05-29 13:09:19] Preparing final response
[2025-05-29 13:09:19] Saving to database
[2025-05-29 13:09:19] Database save successful, record ID: 91
[2025-05-29 13:09:19] Preparing success response
[2025-05-29 13:09:19] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 13:09:19] === result.php end ===
[2025-05-29 13:10:14] === result.php start ===
[2025-05-29 13:10:14] Request method - Data: "POST"
[2025-05-29 13:10:14] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v; wordpress_logged_in_2321612242773d5a13a4c1457aee841c=6ird%7C1749703278%7C6Bvzxxnqa3ROuZrnMzZ3QW15zs1DlJgLQOKqMZJgOjO%7C8c93b0fb09b6f524d0c30cd82c82f8c1d4058d0b30ccf52d83c9f1b0d18a76ed; __eoi=ID=0003676fa60e4351:T=**********:RT=1748494950:S=AA-AfjYweomYFv2bRYjuDYo4DPTN","Priority":"u=1, i","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"148","Host":"www.6ird.com"}
[2025-05-29 13:10:14] Starting main processing...
[2025-05-29 13:10:14] Raw input length - Data: 148
[2025-05-29 13:10:14] Raw input preview - Data: "{\"name\":\"林小\",\"gender\":\"女\",\"year\":1987,\"month\":10,\"day\":1,\"hour\":15,\"minute\":40,\"question\":\"我的事业发展前景如何\",\"model\":\"deepseek\"}"
[2025-05-29 13:10:14] Decoded input - Data: {"name":"林小","gender":"女","year":1987,"month":10,"day":1,"hour":15,"minute":40,"question":"我的事业发展前景如何","model":"deepseek"}
[2025-05-29 13:10:14] Field validation passed
[2025-05-29 13:10:14] Extracted parameters - Data: {"name":"林小","gender":"女","birth":"1987-10-1 15:40","question_length":30,"model":"deepseek"}
[2025-05-29 13:10:14] Parameter validation passed
[2025-05-29 13:10:14] Files exist, loading...
[2025-05-29 13:10:14] Files loaded successfully
[2025-05-29 13:10:14] Starting bazi calculation
[2025-05-29 13:10:14] Bazi calculation successful
[2025-05-29 13:10:14] Building analysis prompt
[2025-05-29 13:10:14] Prompt built, length - Data: 544
[2025-05-29 13:10:14] Starting AI analysis with model: deepseek
[2025-05-29 13:11:06] AI analysis completed - Data: {"has_error":false,"content_length":3775}
[2025-05-29 13:11:06] Preparing final response
[2025-05-29 13:11:06] Saving to database
[2025-05-29 13:11:06] Database save successful, record ID: 92
[2025-05-29 13:11:06] Preparing success response
[2025-05-29 13:11:06] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 13:11:06] === result.php end ===
[2025-05-29 13:11:35] === result.php start ===
[2025-05-29 13:11:35] Request method - Data: "POST"
[2025-05-29 13:11:35] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v; wordpress_logged_in_2321612242773d5a13a4c1457aee841c=6ird%7C1749703278%7C6Bvzxxnqa3ROuZrnMzZ3QW15zs1DlJgLQOKqMZJgOjO%7C8c93b0fb09b6f524d0c30cd82c82f8c1d4058d0b30ccf52d83c9f1b0d18a76ed; __eoi=ID=0003676fa60e4351:T=**********:RT=1748494950:S=AA-AfjYweomYFv2bRYjuDYo4DPTN","Priority":"u=1, i","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"144","Host":"www.6ird.com"}
[2025-05-29 13:11:35] Starting main processing...
[2025-05-29 13:11:35] Raw input length - Data: 144
[2025-05-29 13:11:35] Raw input preview - Data: "{\"name\":\"林小\",\"gender\":\"女\",\"year\":1987,\"month\":10,\"day\":1,\"hour\":15,\"minute\":40,\"question\":\"我的事业发展前景如何\",\"model\":\"qwen\"}"
[2025-05-29 13:11:35] Decoded input - Data: {"name":"林小","gender":"女","year":1987,"month":10,"day":1,"hour":15,"minute":40,"question":"我的事业发展前景如何","model":"qwen"}
[2025-05-29 13:11:35] Field validation passed
[2025-05-29 13:11:35] Extracted parameters - Data: {"name":"林小","gender":"女","birth":"1987-10-1 15:40","question_length":30,"model":"qwen"}
[2025-05-29 13:11:35] Parameter validation passed
[2025-05-29 13:11:35] Files exist, loading...
[2025-05-29 13:11:35] Files loaded successfully
[2025-05-29 13:11:35] Starting bazi calculation
[2025-05-29 13:11:35] Bazi calculation successful
[2025-05-29 13:11:35] Building analysis prompt
[2025-05-29 13:11:35] Prompt built, length - Data: 544
[2025-05-29 13:11:35] Starting AI analysis with model: qwen
[2025-05-29 13:12:02] AI analysis completed - Data: {"has_error":false,"content_length":3287}
[2025-05-29 13:12:02] Preparing final response
[2025-05-29 13:12:02] Saving to database
[2025-05-29 13:12:02] Database save successful, record ID: 93
[2025-05-29 13:12:02] Preparing success response
[2025-05-29 13:12:02] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 13:12:02] === result.php end ===
[2025-05-29 13:17:07] === result.php start ===
[2025-05-29 13:17:07] Request method - Data: "POST"
[2025-05-29 13:17:07] Request headers - Data: {"Cookie":"__eoi=ID=1135192837c73241:T=1748495010:RT=1748495697:S=AA-AfjZyEp1sXviRYszjuM7OUk7u; __gads=ID=a72160311048aa1a:T=1748495010:RT=1748495697:S=ALNI_MY0TPE96YTdDiJWGPbcIrDsUxk2qg; __gpi=UID=0000110514e636b5:T=1748495010:RT=1748495697:S=ALNI_MZe9dKyfoeH8k00oTXFxoZthYqZug; PHPSESSID=ipc7lrrerus4v899164c944mal; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dbrowse%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=1731853704","Sec-Fetch-Dest":"empty","Content-Length":"149","Referer":"https:\/\/www.6ird.com\/suan\/","User-Agent":"Mozilla\/5.0 (iPhone; CPU iPhone OS 18_5_0 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) CriOS\/137.0.7151.51 Mobile\/15E148 Safari\/604.1","Origin":"https:\/\/www.6ird.com","Sec-Fetch-Mode":"cors","Accept-Encoding":"gzip, deflate, br","Accept-Language":"zh-CN,zh-Hans;q=0.9","Sec-Fetch-Site":"same-origin","Accept":"*\/*","Content-Type":"application\/json","Host":"www.6ird.com"}
[2025-05-29 13:17:07] Starting main processing...
[2025-05-29 13:17:07] Raw input length - Data: 149
[2025-05-29 13:17:07] Raw input preview - Data: "{\"name\":\"林总\",\"gender\":\"男\",\"year\":1986,\"month\":5,\"day\":4,\"hour\":4,\"minute\":10,\"question\":\"我的事业发展前景如何？\",\"model\":\"deepseek\"}"
[2025-05-29 13:17:07] Decoded input - Data: {"name":"林总","gender":"男","year":1986,"month":5,"day":4,"hour":4,"minute":10,"question":"我的事业发展前景如何？","model":"deepseek"}
[2025-05-29 13:17:07] Field validation passed
[2025-05-29 13:17:07] Extracted parameters - Data: {"name":"林总","gender":"男","birth":"1986-5-4 4:10","question_length":33,"model":"deepseek"}
[2025-05-29 13:17:07] Parameter validation passed
[2025-05-29 13:17:07] Files exist, loading...
[2025-05-29 13:17:07] Files loaded successfully
[2025-05-29 13:17:07] Starting bazi calculation
[2025-05-29 13:17:07] Bazi calculation successful
[2025-05-29 13:17:07] Building analysis prompt
[2025-05-29 13:17:07] Prompt built, length - Data: 547
[2025-05-29 13:17:07] Starting AI analysis with model: deepseek
[2025-05-29 13:17:30] AI analysis completed - Data: {"has_error":false,"content_length":5494}
[2025-05-29 13:17:30] Preparing final response
[2025-05-29 13:17:30] Saving to database
[2025-05-29 13:17:30] Database save successful, record ID: 94
[2025-05-29 13:17:30] Preparing success response
[2025-05-29 13:17:30] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 13:17:30] === result.php end ===
[2025-05-29 13:22:46] === result.php start ===
[2025-05-29 13:22:46] Request method - Data: "POST"
[2025-05-29 13:22:46] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v; wordpress_logged_in_2321612242773d5a13a4c1457aee841c=6ird%7C1749703278%7C6Bvzxxnqa3ROuZrnMzZ3QW15zs1DlJgLQOKqMZJgOjO%7C8c93b0fb09b6f524d0c30cd82c82f8c1d4058d0b30ccf52d83c9f1b0d18a76ed; __eoi=ID=0003676fa60e4351:T=**********:RT=1748494950:S=AA-AfjYweomYFv2bRYjuDYo4DPTN","Priority":"u=1, i","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"148","Host":"www.6ird.com"}
[2025-05-29 13:22:46] Starting main processing...
[2025-05-29 13:22:46] Raw input length - Data: 148
[2025-05-29 13:22:46] Raw input preview - Data: "{\"name\":\"林阿\",\"gender\":\"男\",\"year\":1986,\"month\":11,\"day\":4,\"hour\":14,\"minute\":50,\"question\":\"我的事业发展前景如何\",\"model\":\"deepseek\"}"
[2025-05-29 13:22:46] Decoded input - Data: {"name":"林阿","gender":"男","year":1986,"month":11,"day":4,"hour":14,"minute":50,"question":"我的事业发展前景如何","model":"deepseek"}
[2025-05-29 13:22:46] Field validation passed
[2025-05-29 13:22:46] Extracted parameters - Data: {"name":"林阿","gender":"男","birth":"1986-11-4 14:50","question_length":30,"model":"deepseek"}
[2025-05-29 13:22:46] Parameter validation passed
[2025-05-29 13:22:46] Files exist, loading...
[2025-05-29 13:22:46] Files loaded successfully
[2025-05-29 13:22:46] Starting bazi calculation
[2025-05-29 13:22:46] Bazi calculation successful
[2025-05-29 13:22:46] Building analysis prompt
[2025-05-29 13:22:46] Prompt built, length - Data: 544
[2025-05-29 13:22:46] Starting AI analysis with model: deepseek
[2025-05-29 13:23:46] AI analysis completed - Data: {"has_error":true,"content_length":726}
[2025-05-29 13:23:46] AI analysis has error - Data: "网络请求失败: Operation timed out after 60000 milliseconds with 5 bytes received"
[2025-05-29 13:23:46] Preparing final response
[2025-05-29 13:23:46] Saving to database
[2025-05-29 13:23:46] Database save successful, record ID: 95
[2025-05-29 13:23:46] Preparing success response
[2025-05-29 13:23:46] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 13:23:46] === result.php end ===
[2025-05-29 13:24:09] === result.php start ===
[2025-05-29 13:24:09] Request method - Data: "POST"
[2025-05-29 13:24:09] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v; wordpress_logged_in_2321612242773d5a13a4c1457aee841c=6ird%7C1749703278%7C6Bvzxxnqa3ROuZrnMzZ3QW15zs1DlJgLQOKqMZJgOjO%7C8c93b0fb09b6f524d0c30cd82c82f8c1d4058d0b30ccf52d83c9f1b0d18a76ed; __eoi=ID=0003676fa60e4351:T=**********:RT=1748494950:S=AA-AfjYweomYFv2bRYjuDYo4DPTN","Priority":"u=1, i","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"148","Host":"www.6ird.com"}
[2025-05-29 13:24:09] Starting main processing...
[2025-05-29 13:24:09] Raw input length - Data: 148
[2025-05-29 13:24:09] Raw input preview - Data: "{\"name\":\"林阿\",\"gender\":\"男\",\"year\":1986,\"month\":11,\"day\":4,\"hour\":14,\"minute\":50,\"question\":\"我的事业发展前景如何\",\"model\":\"deepseek\"}"
[2025-05-29 13:24:09] Decoded input - Data: {"name":"林阿","gender":"男","year":1986,"month":11,"day":4,"hour":14,"minute":50,"question":"我的事业发展前景如何","model":"deepseek"}
[2025-05-29 13:24:09] Field validation passed
[2025-05-29 13:24:09] Extracted parameters - Data: {"name":"林阿","gender":"男","birth":"1986-11-4 14:50","question_length":30,"model":"deepseek"}
[2025-05-29 13:24:09] Parameter validation passed
[2025-05-29 13:24:09] Files exist, loading...
[2025-05-29 13:24:09] Files loaded successfully
[2025-05-29 13:24:09] Starting bazi calculation
[2025-05-29 13:24:09] Bazi calculation successful
[2025-05-29 13:24:09] Building analysis prompt
[2025-05-29 13:24:09] Prompt built, length - Data: 544
[2025-05-29 13:24:09] Starting AI analysis with model: deepseek
[2025-05-29 13:24:42] AI analysis completed - Data: {"has_error":false,"content_length":7868}
[2025-05-29 13:24:42] Preparing final response
[2025-05-29 13:24:42] Saving to database
[2025-05-29 13:24:42] Database save successful, record ID: 96
[2025-05-29 13:24:42] Preparing success response
[2025-05-29 13:24:42] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 13:24:42] === result.php end ===
[2025-05-29 13:35:13] === result.php start ===
[2025-05-29 13:35:13] Request method - Data: "POST"
[2025-05-29 13:35:13] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v; wordpress_logged_in_2321612242773d5a13a4c1457aee841c=6ird%7C1749703278%7C6Bvzxxnqa3ROuZrnMzZ3QW15zs1DlJgLQOKqMZJgOjO%7C8c93b0fb09b6f524d0c30cd82c82f8c1d4058d0b30ccf52d83c9f1b0d18a76ed; __eoi=ID=0003676fa60e4351:T=**********:RT=1748494950:S=AA-AfjYweomYFv2bRYjuDYo4DPTN","Priority":"u=1, i","Sec-Gpc":"1","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"144","Host":"www.6ird.com"}
[2025-05-29 13:35:13] Starting main processing...
[2025-05-29 13:35:13] Raw input length - Data: 144
[2025-05-29 13:35:13] Raw input preview - Data: "{\"name\":\"林阿\",\"gender\":\"男\",\"year\":1986,\"month\":11,\"day\":4,\"hour\":14,\"minute\":50,\"question\":\"我的事业发展前景如何\",\"model\":\"qwen\"}"
[2025-05-29 13:35:13] Decoded input - Data: {"name":"林阿","gender":"男","year":1986,"month":11,"day":4,"hour":14,"minute":50,"question":"我的事业发展前景如何","model":"qwen"}
[2025-05-29 13:35:13] Field validation passed
[2025-05-29 13:35:13] Extracted parameters - Data: {"name":"林阿","gender":"男","birth":"1986-11-4 14:50","question_length":30,"model":"qwen"}
[2025-05-29 13:35:13] Parameter validation passed
[2025-05-29 13:35:13] Files exist, loading...
[2025-05-29 13:35:13] Files loaded successfully
[2025-05-29 13:35:13] Starting bazi calculation
[2025-05-29 13:35:13] Bazi calculation successful
[2025-05-29 13:35:13] Building analysis prompt
[2025-05-29 13:35:13] Prompt built, length - Data: 544
[2025-05-29 13:35:13] Starting AI analysis with model: qwen
[2025-05-29 13:35:44] === result.php start ===
[2025-05-29 13:35:44] Request method - Data: "POST"
[2025-05-29 13:35:44] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v; wordpress_logged_in_2321612242773d5a13a4c1457aee841c=6ird%7C1749703278%7C6Bvzxxnqa3ROuZrnMzZ3QW15zs1DlJgLQOKqMZJgOjO%7C8c93b0fb09b6f524d0c30cd82c82f8c1d4058d0b30ccf52d83c9f1b0d18a76ed; __eoi=ID=0003676fa60e4351:T=**********:RT=1748494950:S=AA-AfjYweomYFv2bRYjuDYo4DPTN","Priority":"u=1, i","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"146","Host":"www.6ird.com"}
[2025-05-29 13:35:44] Starting main processing...
[2025-05-29 13:35:44] Raw input length - Data: 146
[2025-05-29 13:35:44] Raw input preview - Data: "{\"name\":\"林是\",\"gender\":\"男\",\"year\":1987,\"month\":5,\"day\":2,\"hour\":14,\"minute\":40,\"question\":\"我的事业发展前景如何？\",\"model\":\"qwen\"}"
[2025-05-29 13:35:44] Decoded input - Data: {"name":"林是","gender":"男","year":1987,"month":5,"day":2,"hour":14,"minute":40,"question":"我的事业发展前景如何？","model":"qwen"}
[2025-05-29 13:35:44] Field validation passed
[2025-05-29 13:35:44] Extracted parameters - Data: {"name":"林是","gender":"男","birth":"1987-5-2 14:40","question_length":33,"model":"qwen"}
[2025-05-29 13:35:44] Parameter validation passed
[2025-05-29 13:35:44] Files exist, loading...
[2025-05-29 13:35:44] Files loaded successfully
[2025-05-29 13:35:44] Starting bazi calculation
[2025-05-29 13:35:44] Bazi calculation successful
[2025-05-29 13:35:44] Building analysis prompt
[2025-05-29 13:35:44] Prompt built, length - Data: 545
[2025-05-29 13:35:44] Starting AI analysis with model: qwen
[2025-05-29 13:36:10] AI analysis completed - Data: {"has_error":false,"content_length":5111}
[2025-05-29 13:36:10] Preparing final response
[2025-05-29 13:36:10] Saving to database
[2025-05-29 13:36:10] Database save successful, record ID: 97
[2025-05-29 13:36:10] Preparing success response
[2025-05-29 13:36:10] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 13:36:10] === result.php end ===
[2025-05-29 13:36:44] AI analysis completed - Data: {"has_error":true,"content_length":726}
[2025-05-29 13:36:44] AI analysis has error - Data: "网络请求失败: Operation timed out after 60002 milliseconds with 5 bytes received"
[2025-05-29 13:36:44] Preparing final response
[2025-05-29 13:36:44] Saving to database
[2025-05-29 13:36:44] Database save successful, record ID: 98
[2025-05-29 13:36:44] Preparing success response
[2025-05-29 13:36:44] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 13:36:44] === result.php end ===
[2025-05-29 13:37:00] === result.php start ===
[2025-05-29 13:37:00] Request method - Data: "POST"
[2025-05-29 13:37:00] Request headers - Data: {"Cookie":"__eoi=ID=1135192837c73241:T=1748495010:RT=1748496019:S=AA-AfjZyEp1sXviRYszjuM7OUk7u; __gads=ID=a72160311048aa1a:T=1748495010:RT=1748496019:S=ALNI_MY0TPE96YTdDiJWGPbcIrDsUxk2qg; __gpi=UID=0000110514e636b5:T=1748495010:RT=1748496019:S=ALNI_MZe9dKyfoeH8k00oTXFxoZthYqZug; PHPSESSID=ipc7lrrerus4v899164c944mal; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dbrowse%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=1731853704","Sec-Fetch-Dest":"empty","Content-Length":"149","Referer":"https:\/\/www.6ird.com\/suan\/","User-Agent":"Mozilla\/5.0 (iPhone; CPU iPhone OS 18_5_0 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) CriOS\/137.0.7151.51 Mobile\/15E148 Safari\/604.1","Origin":"https:\/\/www.6ird.com","Sec-Fetch-Mode":"cors","Accept-Encoding":"gzip, deflate, br","Accept-Language":"zh-CN,zh-Hans;q=0.9","Sec-Fetch-Site":"same-origin","Accept":"*\/*","Content-Type":"application\/json","Host":"www.6ird.com"}
[2025-05-29 13:37:00] Starting main processing...
[2025-05-29 13:37:00] Raw input length - Data: 149
[2025-05-29 13:37:00] Raw input preview - Data: "{\"name\":\"李大\",\"gender\":\"男\",\"year\":1983,\"month\":4,\"day\":3,\"hour\":4,\"minute\":15,\"question\":\"我的事业发展前景如何？\",\"model\":\"deepseek\"}"
[2025-05-29 13:37:00] Decoded input - Data: {"name":"李大","gender":"男","year":1983,"month":4,"day":3,"hour":4,"minute":15,"question":"我的事业发展前景如何？","model":"deepseek"}
[2025-05-29 13:37:00] Field validation passed
[2025-05-29 13:37:00] Extracted parameters - Data: {"name":"李大","gender":"男","birth":"1983-4-3 4:15","question_length":33,"model":"deepseek"}
[2025-05-29 13:37:00] Parameter validation passed
[2025-05-29 13:37:00] Files exist, loading...
[2025-05-29 13:37:00] Files loaded successfully
[2025-05-29 13:37:00] Starting bazi calculation
[2025-05-29 13:37:00] Bazi calculation successful
[2025-05-29 13:37:00] Building analysis prompt
[2025-05-29 13:37:00] Prompt built, length - Data: 547
[2025-05-29 13:37:00] Starting AI analysis with model: deepseek
[2025-05-29 13:38:00] AI analysis completed - Data: {"has_error":true,"content_length":726}
[2025-05-29 13:38:00] AI analysis has error - Data: "网络请求失败: Operation timed out after 60002 milliseconds with 5 bytes received"
[2025-05-29 13:38:00] Preparing final response
[2025-05-29 13:38:00] Saving to database
[2025-05-29 13:38:00] Database save successful, record ID: 99
[2025-05-29 13:38:00] Preparing success response
[2025-05-29 13:38:00] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 13:38:00] === result.php end ===
[2025-05-29 13:40:14] === result.php start ===
[2025-05-29 13:40:14] Request method - Data: "POST"
[2025-05-29 13:40:14] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v; wordpress_logged_in_2321612242773d5a13a4c1457aee841c=6ird%7C1749703278%7C6Bvzxxnqa3ROuZrnMzZ3QW15zs1DlJgLQOKqMZJgOjO%7C8c93b0fb09b6f524d0c30cd82c82f8c1d4058d0b30ccf52d83c9f1b0d18a76ed; __eoi=ID=0003676fa60e4351:T=**********:RT=1748494950:S=AA-AfjYweomYFv2bRYjuDYo4DPTN","Priority":"u=1, i","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"144","Host":"www.6ird.com"}
[2025-05-29 13:40:14] Starting main processing...
[2025-05-29 13:40:14] Raw input length - Data: 144
[2025-05-29 13:40:14] Raw input preview - Data: "{\"name\":\"林是\",\"gender\":\"女\",\"year\":1987,\"month\":1,\"day\":2,\"hour\":0,\"minute\":0,\"question\":\"我的事业发展前景如何？\",\"model\":\"qwen\"}"
[2025-05-29 13:40:14] Decoded input - Data: {"name":"林是","gender":"女","year":1987,"month":1,"day":2,"hour":0,"minute":0,"question":"我的事业发展前景如何？","model":"qwen"}
[2025-05-29 13:40:14] Field validation passed
[2025-05-29 13:40:14] Extracted parameters - Data: {"name":"林是","gender":"女","birth":"1987-1-2 0:0","question_length":33,"model":"qwen"}
[2025-05-29 13:40:14] Parameter validation passed
[2025-05-29 13:40:14] Files exist, loading...
[2025-05-29 13:40:14] Files loaded successfully
[2025-05-29 13:40:14] Starting bazi calculation
[2025-05-29 13:40:14] Bazi calculation successful
[2025-05-29 13:40:14] Building analysis prompt
[2025-05-29 13:40:14] Prompt built, length - Data: 547
[2025-05-29 13:40:14] Starting AI analysis with model: qwen
[2025-05-29 13:40:46] AI analysis completed - Data: {"has_error":false,"content_length":4208}
[2025-05-29 13:40:46] Preparing final response
[2025-05-29 13:40:46] Saving to database
[2025-05-29 13:40:46] Database save successful, record ID: 100
[2025-05-29 13:40:46] Preparing success response
[2025-05-29 13:40:46] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 13:40:46] === result.php end ===
[2025-05-29 13:45:28] === result.php start ===
[2025-05-29 13:45:28] Request method - Data: "POST"
[2025-05-29 13:45:28] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v; wordpress_logged_in_2321612242773d5a13a4c1457aee841c=6ird%7C1749703278%7C6Bvzxxnqa3ROuZrnMzZ3QW15zs1DlJgLQOKqMZJgOjO%7C8c93b0fb09b6f524d0c30cd82c82f8c1d4058d0b30ccf52d83c9f1b0d18a76ed; __gads=ID=1cdd10df5c2bbac5:T=**********:RT=**********:S=ALNI_MbtJh--KQJ5K2lEfdaOOxcDiA31Sg; __gpi=UID=000011051c6fa4f3:T=**********:RT=**********:S=ALNI_Mau4qk6PfTZXfyXfi9F87yfydEQsw; __eoi=ID=0003676fa60e4351:T=**********:RT=**********:S=AA-AfjYweomYFv2bRYjuDYo4DPTN","Priority":"u=1, i","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"151","Host":"www.6ird.com"}
[2025-05-29 13:45:28] Starting main processing...
[2025-05-29 13:45:28] Raw input length - Data: 151
[2025-05-29 13:45:28] Raw input preview - Data: "{\"name\":\"林北\",\"gender\":\"男\",\"year\":1986,\"month\":11,\"day\":7,\"hour\":14,\"minute\":45,\"question\":\"我的事业发展前景如何？\",\"model\":\"deepseek\"}"
[2025-05-29 13:45:28] Decoded input - Data: {"name":"林北","gender":"男","year":1986,"month":11,"day":7,"hour":14,"minute":45,"question":"我的事业发展前景如何？","model":"deepseek"}
[2025-05-29 13:45:28] Field validation passed
[2025-05-29 13:45:28] Extracted parameters - Data: {"name":"林北","gender":"男","birth":"1986-11-7 14:45","question_length":33,"model":"deepseek"}
[2025-05-29 13:45:28] Parameter validation passed
[2025-05-29 13:45:28] Files exist, loading...
[2025-05-29 13:45:28] Files loaded successfully
[2025-05-29 13:45:28] Starting bazi calculation
[2025-05-29 13:45:28] Bazi calculation successful
[2025-05-29 13:45:28] Building analysis prompt
[2025-05-29 13:45:28] Prompt built, length - Data: 547
[2025-05-29 13:45:28] Starting AI analysis with model: deepseek
[2025-05-29 13:45:59] AI analysis completed - Data: {"has_error":false,"content_length":7618}
[2025-05-29 13:45:59] Preparing final response
[2025-05-29 13:45:59] Saving to database
[2025-05-29 13:45:59] Database save successful, record ID: 101
[2025-05-29 13:45:59] Preparing success response
[2025-05-29 13:45:59] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 13:45:59] === result.php end ===
[2025-05-29 13:47:00] === result.php start ===
[2025-05-29 13:47:00] Request method - Data: "POST"
[2025-05-29 13:47:00] Request headers - Data: {"Cookie":"__eoi=ID=1135192837c73241:T=1748495010:RT=1748497587:S=AA-AfjZyEp1sXviRYszjuM7OUk7u; __gads=ID=a72160311048aa1a:T=1748495010:RT=1748497587:S=ALNI_MY0TPE96YTdDiJWGPbcIrDsUxk2qg; __gpi=UID=0000110514e636b5:T=1748495010:RT=1748497587:S=ALNI_MZe9dKyfoeH8k00oTXFxoZthYqZug; PHPSESSID=ipc7lrrerus4v899164c944mal; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dbrowse%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=1731853704","Sec-Fetch-Dest":"empty","Content-Length":"149","Referer":"https:\/\/www.6ird.com\/suan\/","User-Agent":"Mozilla\/5.0 (iPhone; CPU iPhone OS 18_5_0 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) CriOS\/137.0.7151.51 Mobile\/15E148 Safari\/604.1","Origin":"https:\/\/www.6ird.com","Sec-Fetch-Mode":"cors","Accept-Encoding":"gzip, deflate, br","Accept-Language":"zh-CN,zh-Hans;q=0.9","Sec-Fetch-Site":"same-origin","Accept":"*\/*","Content-Type":"application\/json","Host":"www.6ird.com"}
[2025-05-29 13:47:00] Starting main processing...
[2025-05-29 13:47:00] Raw input length - Data: 149
[2025-05-29 13:47:00] Raw input preview - Data: "{\"name\":\"李大\",\"gender\":\"男\",\"year\":1982,\"month\":4,\"day\":4,\"hour\":3,\"minute\":10,\"question\":\"我的事业发展前景如何？\",\"model\":\"deepseek\"}"
[2025-05-29 13:47:00] Decoded input - Data: {"name":"李大","gender":"男","year":1982,"month":4,"day":4,"hour":3,"minute":10,"question":"我的事业发展前景如何？","model":"deepseek"}
[2025-05-29 13:47:00] Field validation passed
[2025-05-29 13:47:00] Extracted parameters - Data: {"name":"李大","gender":"男","birth":"1982-4-4 3:10","question_length":33,"model":"deepseek"}
[2025-05-29 13:47:00] Parameter validation passed
[2025-05-29 13:47:00] Files exist, loading...
[2025-05-29 13:47:00] Files loaded successfully
[2025-05-29 13:47:00] Starting bazi calculation
[2025-05-29 13:47:00] Bazi calculation successful
[2025-05-29 13:47:00] Building analysis prompt
[2025-05-29 13:47:00] Prompt built, length - Data: 545
[2025-05-29 13:47:00] Starting AI analysis with model: deepseek
[2025-05-29 13:48:00] AI analysis completed - Data: {"has_error":true,"content_length":726}
[2025-05-29 13:48:00] AI analysis has error - Data: "网络请求失败: Operation timed out after 60002 milliseconds with 5 bytes received"
[2025-05-29 13:48:00] Preparing final response
[2025-05-29 13:48:00] Saving to database
[2025-05-29 13:48:00] Database save successful, record ID: 102
[2025-05-29 13:48:00] Preparing success response
[2025-05-29 13:48:00] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 13:48:00] === result.php end ===
[2025-05-29 13:55:01] === result.php start ===
[2025-05-29 13:55:01] Request method - Data: "POST"
[2025-05-29 13:55:01] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v; wordpress_logged_in_2321612242773d5a13a4c1457aee841c=6ird%7C1749703278%7C6Bvzxxnqa3ROuZrnMzZ3QW15zs1DlJgLQOKqMZJgOjO%7C8c93b0fb09b6f524d0c30cd82c82f8c1d4058d0b30ccf52d83c9f1b0d18a76ed; __gads=ID=1cdd10df5c2bbac5:T=**********:RT=**********:S=ALNI_MbtJh--KQJ5K2lEfdaOOxcDiA31Sg; __gpi=UID=000011051c6fa4f3:T=**********:RT=**********:S=ALNI_Mau4qk6PfTZXfyXfi9F87yfydEQsw; __eoi=ID=0003676fa60e4351:T=**********:RT=**********:S=AA-AfjYweomYFv2bRYjuDYo4DPTN","Priority":"u=1, i","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"146","Host":"www.6ird.com"}
[2025-05-29 13:55:01] Starting main processing...
[2025-05-29 13:55:01] Raw input length - Data: 146
[2025-05-29 13:55:01] Raw input preview - Data: "{\"name\":\"李北\",\"gender\":\"男\",\"year\":1986,\"month\":2,\"day\":14,\"hour\":1,\"minute\":10,\"question\":\"我的事业发展前景如何？\",\"model\":\"qwen\"}"
[2025-05-29 13:55:01] Decoded input - Data: {"name":"李北","gender":"男","year":1986,"month":2,"day":14,"hour":1,"minute":10,"question":"我的事业发展前景如何？","model":"qwen"}
[2025-05-29 13:55:01] Field validation passed
[2025-05-29 13:55:01] Extracted parameters - Data: {"name":"李北","gender":"男","birth":"1986-2-14 1:10","question_length":33,"model":"qwen"}
[2025-05-29 13:55:01] Parameter validation passed
[2025-05-29 13:55:01] Files exist, loading...
[2025-05-29 13:55:01] Files loaded successfully
[2025-05-29 13:55:01] Starting bazi calculation
[2025-05-29 13:55:01] Bazi calculation successful
[2025-05-29 13:55:01] Building analysis prompt
[2025-05-29 13:55:01] Prompt built, length - Data: 547
[2025-05-29 13:55:01] Starting AI analysis with model: qwen
[2025-05-29 13:55:31] AI analysis completed - Data: {"has_error":false,"content_length":5596}
[2025-05-29 13:55:31] Preparing final response
[2025-05-29 13:55:31] Saving to database
[2025-05-29 13:55:31] Database save successful, record ID: 103
[2025-05-29 13:55:31] Preparing success response
[2025-05-29 13:55:31] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 13:55:31] === result.php end ===
[2025-05-29 15:21:11] === result.php start ===
[2025-05-29 15:21:11] Request method - Data: "POST"
[2025-05-29 15:21:11] Request headers - Data: {"Cookie":"PHPSESSID=torkl9f5tu5asma3h6c3ar3rm5","Priority":"u=1, i","Accept-Language":"zh,zh-CN;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"139","Host":"www.6ird.com"}
[2025-05-29 15:21:11] Starting main processing...
[2025-05-29 15:21:11] Raw input length - Data: 139
[2025-05-29 15:21:11] Raw input preview - Data: "{\"name\":\"负二代\",\"gender\":\"男\",\"year\":1999,\"month\":8,\"day\":8,\"hour\":7,\"minute\":25,\"question\":\"事业,婚姻,财富\",\"model\":\"deepseek\"}"
[2025-05-29 15:21:11] Decoded input - Data: {"name":"负二代","gender":"男","year":1999,"month":8,"day":8,"hour":7,"minute":25,"question":"事业,婚姻,财富","model":"deepseek"}
[2025-05-29 15:21:11] Field validation passed
[2025-05-29 15:21:11] Extracted parameters - Data: {"name":"负二代","gender":"男","birth":"1999-8-8 7:25","question_length":20,"model":"deepseek"}
[2025-05-29 15:21:11] Parameter validation passed
[2025-05-29 15:21:11] Files exist, loading...
[2025-05-29 15:21:11] Files loaded successfully
[2025-05-29 15:21:11] Starting bazi calculation
[2025-05-29 15:21:11] Bazi calculation successful
[2025-05-29 15:21:11] Building analysis prompt
[2025-05-29 15:21:11] Prompt built, length - Data: 537
[2025-05-29 15:21:11] Starting AI analysis with model: deepseek
[2025-05-29 15:22:09] AI analysis completed - Data: {"has_error":false,"content_length":3209}
[2025-05-29 15:22:09] Preparing final response
[2025-05-29 15:22:09] Saving to database
[2025-05-29 15:22:09] Database save successful, record ID: 104
[2025-05-29 15:22:09] Preparing success response
[2025-05-29 15:22:09] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 15:22:09] === result.php end ===
[2025-05-29 15:22:44] === result.php start ===
[2025-05-29 15:22:44] Request method - Data: "POST"
[2025-05-29 15:22:44] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v; wordpress_logged_in_2321612242773d5a13a4c1457aee841c=6ird%7C1749703278%7C6Bvzxxnqa3ROuZrnMzZ3QW15zs1DlJgLQOKqMZJgOjO%7C8c93b0fb09b6f524d0c30cd82c82f8c1d4058d0b30ccf52d83c9f1b0d18a76ed; __gads=ID=1cdd10df5c2bbac5:T=**********:RT=1748501255:S=ALNI_MbtJh--KQJ5K2lEfdaOOxcDiA31Sg; __gpi=UID=000011051c6fa4f3:T=**********:RT=1748501255:S=ALNI_Mau4qk6PfTZXfyXfi9F87yfydEQsw; __eoi=ID=0003676fa60e4351:T=**********:RT=1748501255:S=AA-AfjYweomYFv2bRYjuDYo4DPTN","Priority":"u=1, i","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"148","Host":"www.6ird.com"}
[2025-05-29 15:22:44] Starting main processing...
[2025-05-29 15:22:44] Raw input length - Data: 148
[2025-05-29 15:22:44] Raw input preview - Data: "{\"name\":\"李申\",\"gender\":\"男\",\"year\":1985,\"month\":2,\"day\":3,\"hour\":1,\"minute\":5,\"question\":\"我的事业发展前景如何？\",\"model\":\"deepseek\"}"
[2025-05-29 15:22:44] Decoded input - Data: {"name":"李申","gender":"男","year":1985,"month":2,"day":3,"hour":1,"minute":5,"question":"我的事业发展前景如何？","model":"deepseek"}
[2025-05-29 15:22:44] Field validation passed
[2025-05-29 15:22:44] Extracted parameters - Data: {"name":"李申","gender":"男","birth":"1985-2-3 1:5","question_length":33,"model":"deepseek"}
[2025-05-29 15:22:44] Parameter validation passed
[2025-05-29 15:22:44] Files exist, loading...
[2025-05-29 15:22:44] Files loaded successfully
[2025-05-29 15:22:44] Starting bazi calculation
[2025-05-29 15:22:44] Bazi calculation successful
[2025-05-29 15:22:44] Building analysis prompt
[2025-05-29 15:22:44] Prompt built, length - Data: 547
[2025-05-29 15:22:44] Starting AI analysis with model: deepseek
[2025-05-29 15:22:59] AI analysis completed - Data: {"has_error":false,"content_length":3896}
[2025-05-29 15:22:59] Preparing final response
[2025-05-29 15:22:59] Saving to database
[2025-05-29 15:22:59] Database save successful, record ID: 105
[2025-05-29 15:22:59] Preparing success response
[2025-05-29 15:22:59] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 15:22:59] === result.php end ===
[2025-05-29 15:24:30] === result.php start ===
[2025-05-29 15:24:30] Request method - Data: "POST"
[2025-05-29 15:24:30] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v; wordpress_logged_in_2321612242773d5a13a4c1457aee841c=6ird%7C1749703278%7C6Bvzxxnqa3ROuZrnMzZ3QW15zs1DlJgLQOKqMZJgOjO%7C8c93b0fb09b6f524d0c30cd82c82f8c1d4058d0b30ccf52d83c9f1b0d18a76ed; __gads=ID=1cdd10df5c2bbac5:T=**********:RT=1748501255:S=ALNI_MbtJh--KQJ5K2lEfdaOOxcDiA31Sg; __gpi=UID=000011051c6fa4f3:T=**********:RT=1748501255:S=ALNI_Mau4qk6PfTZXfyXfi9F87yfydEQsw; __eoi=ID=0003676fa60e4351:T=**********:RT=1748501255:S=AA-AfjYweomYFv2bRYjuDYo4DPTN","Priority":"u=1, i","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"144","Host":"www.6ird.com"}
[2025-05-29 15:24:30] Starting main processing...
[2025-05-29 15:24:30] Raw input length - Data: 144
[2025-05-29 15:24:30] Raw input preview - Data: "{\"name\":\"李申\",\"gender\":\"男\",\"year\":1985,\"month\":2,\"day\":3,\"hour\":1,\"minute\":5,\"question\":\"我的事业发展前景如何？\",\"model\":\"qwen\"}"
[2025-05-29 15:24:30] Decoded input - Data: {"name":"李申","gender":"男","year":1985,"month":2,"day":3,"hour":1,"minute":5,"question":"我的事业发展前景如何？","model":"qwen"}
[2025-05-29 15:24:30] Field validation passed
[2025-05-29 15:24:30] Extracted parameters - Data: {"name":"李申","gender":"男","birth":"1985-2-3 1:5","question_length":33,"model":"qwen"}
[2025-05-29 15:24:30] Parameter validation passed
[2025-05-29 15:24:30] Files exist, loading...
[2025-05-29 15:24:30] Files loaded successfully
[2025-05-29 15:24:30] Starting bazi calculation
[2025-05-29 15:24:30] Bazi calculation successful
[2025-05-29 15:24:30] Building analysis prompt
[2025-05-29 15:24:30] Prompt built, length - Data: 547
[2025-05-29 15:24:30] Starting AI analysis with model: qwen
[2025-05-29 15:25:30] AI analysis completed - Data: {"has_error":true,"content_length":726}
[2025-05-29 15:25:30] AI analysis has error - Data: "网络请求失败: Operation timed out after 60002 milliseconds with 5 bytes received"
[2025-05-29 15:25:30] Preparing final response
[2025-05-29 15:25:30] Saving to database
[2025-05-29 15:25:30] Database save successful, record ID: 106
[2025-05-29 15:25:30] Preparing success response
[2025-05-29 15:25:30] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 15:25:30] === result.php end ===
[2025-05-29 15:27:54] === result.php start ===
[2025-05-29 15:27:54] Request method - Data: "POST"
[2025-05-29 15:27:54] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v; wordpress_logged_in_2321612242773d5a13a4c1457aee841c=6ird%7C1749703278%7C6Bvzxxnqa3ROuZrnMzZ3QW15zs1DlJgLQOKqMZJgOjO%7C8c93b0fb09b6f524d0c30cd82c82f8c1d4058d0b30ccf52d83c9f1b0d18a76ed; __gads=ID=1cdd10df5c2bbac5:T=**********:RT=1748503638:S=ALNI_MbtJh--KQJ5K2lEfdaOOxcDiA31Sg; __gpi=UID=000011051c6fa4f3:T=**********:RT=1748503638:S=ALNI_Mau4qk6PfTZXfyXfi9F87yfydEQsw; __eoi=ID=0003676fa60e4351:T=**********:RT=1748503638:S=AA-AfjYweomYFv2bRYjuDYo4DPTN","Priority":"u=1, i","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"147","Host":"www.6ird.com"}
[2025-05-29 15:27:54] Starting main processing...
[2025-05-29 15:27:54] Raw input length - Data: 147
[2025-05-29 15:27:54] Raw input preview - Data: "{\"name\":\"李白\",\"gender\":\"男\",\"year\":1987,\"month\":11,\"day\":1,\"hour\":14,\"minute\":50,\"question\":\"我的事业发展前景如何？\",\"model\":\"qwen\"}"
[2025-05-29 15:27:54] Decoded input - Data: {"name":"李白","gender":"男","year":1987,"month":11,"day":1,"hour":14,"minute":50,"question":"我的事业发展前景如何？","model":"qwen"}
[2025-05-29 15:27:54] Field validation passed
[2025-05-29 15:27:54] Extracted parameters - Data: {"name":"李白","gender":"男","birth":"1987-11-1 14:50","question_length":33,"model":"qwen"}
[2025-05-29 15:27:54] Parameter validation passed
[2025-05-29 15:27:54] Files exist, loading...
[2025-05-29 15:27:54] Files loaded successfully
[2025-05-29 15:27:54] Starting bazi calculation
[2025-05-29 15:27:54] Bazi calculation successful
[2025-05-29 15:27:54] Building analysis prompt
[2025-05-29 15:27:54] Prompt built, length - Data: 547
[2025-05-29 15:27:54] Starting AI analysis with model: qwen
[2025-05-29 15:28:35] AI analysis completed - Data: {"has_error":false,"content_length":5652}
[2025-05-29 15:28:35] Preparing final response
[2025-05-29 15:28:36] Saving to database
[2025-05-29 15:28:36] Database save successful, record ID: 107
[2025-05-29 15:28:36] Preparing success response
[2025-05-29 15:28:36] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 15:28:36] === result.php end ===
[2025-05-29 15:31:10] === result.php start ===
[2025-05-29 15:31:10] Request method - Data: "POST"
[2025-05-29 15:31:10] Request headers - Data: {"Cookie":"Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=1729500488; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v; wordpress_logged_in_2321612242773d5a13a4c1457aee841c=6ird%7C1749703278%7C6Bvzxxnqa3ROuZrnMzZ3QW15zs1DlJgLQOKqMZJgOjO%7C8c93b0fb09b6f524d0c30cd82c82f8c1d4058d0b30ccf52d83c9f1b0d18a76ed; __gads=ID=1cdd10df5c2bbac5:T=**********:RT=1748503638:S=ALNI_MbtJh--KQJ5K2lEfdaOOxcDiA31Sg; __gpi=UID=000011051c6fa4f3:T=**********:RT=1748503638:S=ALNI_Mau4qk6PfTZXfyXfi9F87yfydEQsw; __eoi=ID=0003676fa60e4351:T=**********:RT=1748503638:S=AA-AfjYweomYFv2bRYjuDYo4DPTN","Priority":"u=1, i","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"151","Host":"www.6ird.com"}
[2025-05-29 15:31:10] Starting main processing...
[2025-05-29 15:31:10] Raw input length - Data: 151
[2025-05-29 15:31:10] Raw input preview - Data: "{\"name\":\"李白\",\"gender\":\"男\",\"year\":1987,\"month\":11,\"day\":1,\"hour\":14,\"minute\":50,\"question\":\"我的事业发展前景如何？\",\"model\":\"deepseek\"}"
[2025-05-29 15:31:10] Decoded input - Data: {"name":"李白","gender":"男","year":1987,"month":11,"day":1,"hour":14,"minute":50,"question":"我的事业发展前景如何？","model":"deepseek"}
[2025-05-29 15:31:10] Field validation passed
[2025-05-29 15:31:10] Extracted parameters - Data: {"name":"李白","gender":"男","birth":"1987-11-1 14:50","question_length":33,"model":"deepseek"}
[2025-05-29 15:31:10] Parameter validation passed
[2025-05-29 15:31:10] Files exist, loading...
[2025-05-29 15:31:10] Files loaded successfully
[2025-05-29 15:31:10] Starting bazi calculation
[2025-05-29 15:31:10] Bazi calculation successful
[2025-05-29 15:31:10] Building analysis prompt
[2025-05-29 15:31:10] Prompt built, length - Data: 547
[2025-05-29 15:31:10] Starting AI analysis with model: deepseek
[2025-05-29 15:32:10] AI analysis completed - Data: {"has_error":true,"content_length":726}
[2025-05-29 15:32:10] AI analysis has error - Data: "网络请求失败: Operation timed out after 60002 milliseconds with 5 bytes received"
[2025-05-29 15:32:10] Preparing final response
[2025-05-29 15:32:10] Saving to database
[2025-05-29 15:32:10] Database save successful, record ID: 108
[2025-05-29 15:32:10] Preparing success response
[2025-05-29 15:32:10] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-05-29 15:32:10] === result.php end ===
[2025-06-12 15:24:20] === result.php start ===
[2025-06-12 15:24:20] Request method - Data: "POST"
[2025-06-12 15:24:20] Request headers - Data: {"Cookie":"wp-settings-1=editor%3Dtinymce%26libraryContent%3Dupload%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=**********; PHPSESSID=vhf2p4lpalfc866u2at2ivfn2v; wordpress_logged_in_2321612242773d5a13a4c1457aee841c=6ird%7C1749703278%7C6Bvzxxnqa3ROuZrnMzZ3QW15zs1DlJgLQOKqMZJgOjO%7C8c93b0fb09b6f524d0c30cd82c82f8c1d4058d0b30ccf52d83c9f1b0d18a76ed; Hm_lvt_7fdcabbd898d3abe2690b023345adc9a=**********; HMACCOUNT=93BB7429D35549CC; _clck=8z9z3s%7C2%7Cfwo%7C0%7C1988; _clsk=ej7ael%7C1749636737180%7C1%7C1%7Ce.clarity.ms%2Fcollect; Hm_lpvt_7fdcabbd898d3abe2690b023345adc9a=**********; __gads=ID=1cdd10df5c2bbac5:T=**********:RT=**********:S=ALNI_MbtJh--KQJ5K2lEfdaOOxcDiA31Sg; __gpi=UID=000011051c6fa4f3:T=**********:RT=**********:S=ALNI_Mau4qk6PfTZXfyXfi9F87yfydEQsw; __eoi=ID=0003676fa60e4351:T=**********:RT=**********:S=AA-AfjYweomYFv2bRYjuDYo4DPTN","Priority":"u=1, i","Accept-Language":"zh-CN,zh;q=0.9,en;q=0.8","Accept-Encoding":"gzip, deflate, br, zstd","Referer":"https:\/\/www.6ird.com\/suan\/","Sec-Fetch-Dest":"empty","Sec-Fetch-Mode":"cors","Sec-Fetch-Site":"same-origin","Origin":"https:\/\/www.6ird.com","Accept":"*\/*","Sec-Ch-Ua-Mobile":"?0","Content-Type":"application\/json","Dnt":"1","Sec-Ch-Ua":"\"Chromium\";v=\"136\", \"Google Chrome\";v=\"136\", \"Not.A\/Brand\";v=\"99\"","User-Agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36","Sec-Ch-Ua-Platform":"\"Windows\"","Content-Length":"152","Host":"www.6ird.com"}
[2025-06-12 15:24:20] Starting main processing...
[2025-06-12 15:24:20] Raw input length - Data: 152
[2025-06-12 15:24:20] Raw input preview - Data: "{\"name\":\"林小\",\"gender\":\"男\",\"year\":1985,\"month\":10,\"day\":12,\"hour\":13,\"minute\":45,\"question\":\"我的事业发展前景如何？\",\"model\":\"deepseek\"}"
[2025-06-12 15:24:20] Decoded input - Data: {"name":"林小","gender":"男","year":1985,"month":10,"day":12,"hour":13,"minute":45,"question":"我的事业发展前景如何？","model":"deepseek"}
[2025-06-12 15:24:20] Field validation passed
[2025-06-12 15:24:20] Extracted parameters - Data: {"name":"林小","gender":"男","birth":"1985-10-12 13:45","question_length":33,"model":"deepseek"}
[2025-06-12 15:24:20] Parameter validation passed
[2025-06-12 15:24:20] Files exist, loading...
[2025-06-12 15:24:20] Files loaded successfully
[2025-06-12 15:24:20] Starting bazi calculation
[2025-06-12 15:24:20] Bazi calculation successful
[2025-06-12 15:24:20] Building analysis prompt
[2025-06-12 15:24:20] Prompt built, length - Data: 545
[2025-06-12 15:24:20] Starting AI analysis with model: deepseek
[2025-06-12 15:24:50] AI analysis completed - Data: {"has_error":false,"content_length":4025}
[2025-06-12 15:24:50] Preparing final response
[2025-06-12 15:24:50] Saving to database
[2025-06-12 15:24:50] Database save successful, record ID: 109
[2025-06-12 15:24:50] Preparing success response
[2025-06-12 15:24:50] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-06-12 15:24:50] === result.php end ===
[2025-06-12 15:37:52] === result.php start ===
[2025-06-12 15:37:52] Request method - Data: "POST"
[2025-06-12 15:37:52] Request headers - Data: {"Cookie":"__eoi=ID=1135192837c73241:T=1748495010:RT=1749713825:S=AA-AfjZyEp1sXviRYszjuM7OUk7u; __gads=ID=a72160311048aa1a:T=1748495010:RT=1749713825:S=ALNI_MY0TPE96YTdDiJWGPbcIrDsUxk2qg; __gpi=UID=0000110514e636b5:T=1748495010:RT=1749713825:S=ALNI_MZe9dKyfoeH8k00oTXFxoZthYqZug; PHPSESSID=ronra0mnj1dj1brsk1g7cja380; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dbrowse%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=1731853704","Sec-Fetch-Dest":"empty","Content-Length":"149","Referer":"https:\/\/www.6ird.com\/suan\/","User-Agent":"Mozilla\/5.0 (iPhone; CPU iPhone OS 18_5_0 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) CriOS\/137.0.7151.107 Mobile\/15E148 Safari\/604.1","Origin":"https:\/\/www.6ird.com","Sec-Fetch-Mode":"cors","Accept-Encoding":"gzip, deflate, br","Accept-Language":"zh-CN,zh-Hans;q=0.9","Sec-Fetch-Site":"same-origin","Accept":"*\/*","Content-Type":"application\/json","Host":"www.6ird.com"}
[2025-06-12 15:37:52] Starting main processing...
[2025-06-12 15:37:52] Raw input length - Data: 149
[2025-06-12 15:37:52] Raw input preview - Data: "{\"name\":\"林总\",\"gender\":\"男\",\"year\":1987,\"month\":4,\"day\":12,\"hour\":3,\"minute\":5,\"question\":\"我的事业发展前景如何？\",\"model\":\"deepseek\"}"
[2025-06-12 15:37:52] Decoded input - Data: {"name":"林总","gender":"男","year":1987,"month":4,"day":12,"hour":3,"minute":5,"question":"我的事业发展前景如何？","model":"deepseek"}
[2025-06-12 15:37:52] Field validation passed
[2025-06-12 15:37:52] Extracted parameters - Data: {"name":"林总","gender":"男","birth":"1987-4-12 3:5","question_length":33,"model":"deepseek"}
[2025-06-12 15:37:52] Parameter validation passed
[2025-06-12 15:37:52] Files exist, loading...
[2025-06-12 15:37:52] Files loaded successfully
[2025-06-12 15:37:52] Starting bazi calculation
[2025-06-12 15:37:52] Bazi calculation successful
[2025-06-12 15:37:52] Building analysis prompt
[2025-06-12 15:37:52] Prompt built, length - Data: 547
[2025-06-12 15:37:52] Starting AI analysis with model: deepseek
[2025-06-12 15:38:50] AI analysis completed - Data: {"has_error":false,"content_length":3023}
[2025-06-12 15:38:50] Preparing final response
[2025-06-12 15:38:50] Saving to database
[2025-06-12 15:38:50] Database save successful, record ID: 110
[2025-06-12 15:38:50] Preparing success response
[2025-06-12 15:38:50] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-06-12 15:38:50] === result.php end ===
[2025-06-12 15:56:35] === result.php start ===
[2025-06-12 15:56:35] Request method - Data: "POST"
[2025-06-12 15:56:35] Request headers - Data: {"Cookie":"__eoi=ID=1135192837c73241:T=1748495010:RT=1749714950:S=AA-AfjZyEp1sXviRYszjuM7OUk7u; __gads=ID=a72160311048aa1a:T=1748495010:RT=1749714950:S=ALNI_MY0TPE96YTdDiJWGPbcIrDsUxk2qg; __gpi=UID=0000110514e636b5:T=1748495010:RT=1749714950:S=ALNI_MZe9dKyfoeH8k00oTXFxoZthYqZug; PHPSESSID=ronra0mnj1dj1brsk1g7cja380; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dbrowse%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=1731853704","Sec-Fetch-Dest":"empty","Content-Length":"152","Referer":"https:\/\/www.6ird.com\/suan\/","User-Agent":"Mozilla\/5.0 (iPhone; CPU iPhone OS 18_5_0 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) CriOS\/137.0.7151.107 Mobile\/15E148 Safari\/604.1","Origin":"https:\/\/www.6ird.com","Sec-Fetch-Mode":"cors","Accept-Encoding":"gzip, deflate, br","Accept-Language":"zh-CN,zh-Hans;q=0.9","Sec-Fetch-Site":"same-origin","Accept":"*\/*","Content-Type":"application\/json","Host":"www.6ird.com"}
[2025-06-12 15:56:35] Starting main processing...
[2025-06-12 15:56:35] Raw input length - Data: 152
[2025-06-12 15:56:35] Raw input preview - Data: "{\"name\":\"林俊杰\",\"gender\":\"男\",\"year\":1985,\"month\":4,\"day\":12,\"hour\":3,\"minute\":5,\"question\":\"我的事业发展前景如何？\",\"model\":\"deepseek\"}"
[2025-06-12 15:56:35] Decoded input - Data: {"name":"林俊杰","gender":"男","year":1985,"month":4,"day":12,"hour":3,"minute":5,"question":"我的事业发展前景如何？","model":"deepseek"}
[2025-06-12 15:56:35] Field validation passed
[2025-06-12 15:56:35] Extracted parameters - Data: {"name":"林俊杰","gender":"男","birth":"1985-4-12 3:5","question_length":33,"model":"deepseek"}
[2025-06-12 15:56:35] Parameter validation passed
[2025-06-12 15:56:35] Files exist, loading...
[2025-06-12 15:56:35] Files loaded successfully
[2025-06-12 15:56:35] Starting bazi calculation
[2025-06-12 15:56:35] Bazi calculation successful
[2025-06-12 15:56:35] Building analysis prompt
[2025-06-12 15:56:35] Prompt built, length - Data: 550
[2025-06-12 15:56:35] Starting AI analysis with model: deepseek
[2025-06-12 15:56:52] AI analysis completed - Data: {"has_error":false,"content_length":4953}
[2025-06-12 15:56:52] Preparing final response
[2025-06-12 15:56:52] Saving to database
[2025-06-12 15:56:52] Database save successful, record ID: 111
[2025-06-12 15:56:52] Preparing success response
[2025-06-12 15:56:52] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-06-12 15:56:52] === result.php end ===
[2025-06-12 18:55:29] === result.php start ===
[2025-06-12 18:55:29] Request method - Data: "POST"
[2025-06-12 18:55:29] Request headers - Data: {"Cookie":"__eoi=ID=1135192837c73241:T=1748495010:RT=1749725678:S=AA-AfjZyEp1sXviRYszjuM7OUk7u; __gads=ID=a72160311048aa1a:T=1748495010:RT=1749725678:S=ALNI_MY0TPE96YTdDiJWGPbcIrDsUxk2qg; __gpi=UID=0000110514e636b5:T=1748495010:RT=1749725678:S=ALNI_MZe9dKyfoeH8k00oTXFxoZthYqZug; PHPSESSID=rof7thde7crbmpgrbjjo6uhu4l; wp-settings-1=editor%3Dtinymce%26libraryContent%3Dbrowse%26hidetb%3D1%26advImgDetails%3Dshow%26post_dfw%3Doff%26imgsize%3Dlarge%26posts_list_mode%3Dlist%26widgets_access%3Doff; wp-settings-time-1=1731853704","Sec-Fetch-Dest":"empty","Content-Length":"129","Referer":"https:\/\/www.6ird.com\/suan\/","User-Agent":"Mozilla\/5.0 (iPhone; CPU iPhone OS 18_5_0 like Mac OS X) AppleWebKit\/605.1.15 (KHTML, like Gecko) CriOS\/137.0.7151.107 Mobile\/15E148 Safari\/604.1","Origin":"https:\/\/www.6ird.com","Sec-Fetch-Mode":"cors","Accept-Encoding":"gzip, deflate, br","Accept-Language":"zh-CN,zh-Hans;q=0.9","Sec-Fetch-Site":"same-origin","Accept":"*\/*","Content-Type":"application\/json","Host":"www.6ird.com"}
[2025-06-12 18:55:29] Starting main processing...
[2025-06-12 18:55:29] Raw input length - Data: 129
[2025-06-12 18:55:29] Raw input preview - Data: "{\"name\":\"林小\",\"gender\":\"男\",\"year\":1987,\"month\":4,\"day\":12,\"hour\":4,\"minute\":25,\"question\":\"健康状态\",\"model\":\"deepseek\"}"
[2025-06-12 18:55:29] Decoded input - Data: {"name":"林小","gender":"男","year":1987,"month":4,"day":12,"hour":4,"minute":25,"question":"健康状态","model":"deepseek"}
[2025-06-12 18:55:29] Field validation passed
[2025-06-12 18:55:29] Extracted parameters - Data: {"name":"林小","gender":"男","birth":"1987-4-12 4:25","question_length":12,"model":"deepseek"}
[2025-06-12 18:55:29] Parameter validation passed
[2025-06-12 18:55:29] Files exist, loading...
[2025-06-12 18:55:29] Files loaded successfully
[2025-06-12 18:55:29] Starting bazi calculation
[2025-06-12 18:55:29] Bazi calculation successful
[2025-06-12 18:55:29] Building analysis prompt
[2025-06-12 18:55:29] Prompt built, length - Data: 526
[2025-06-12 18:55:29] Starting AI analysis with model: deepseek
[2025-06-12 18:55:57] AI analysis completed - Data: {"has_error":false,"content_length":4443}
[2025-06-12 18:55:57] Preparing final response
[2025-06-12 18:55:57] Saving to database
[2025-06-12 18:55:57] Database save successful, record ID: 112
[2025-06-12 18:55:57] Preparing success response
[2025-06-12 18:55:57] Sending response - Data: {"success":true,"error":null,"statusCode":200,"has_data":true}
[2025-06-12 18:55:57] === result.php end ===
