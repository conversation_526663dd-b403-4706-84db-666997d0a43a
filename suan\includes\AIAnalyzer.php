<?php
class AIAnalyzer {
    private $config;
    private $pdo;
    private static $saveCount = 0; // 静态计数器
    
    public function __construct() {
        global $pdo;
        $this->pdo = $pdo;
        $this->loadConfig();
    }
    
    private function str_ends_with($haystack, $needle) {
        return $needle === '' || substr($haystack, -strlen($needle)) === $needle;
    }
    
    private function loadConfig() {
        try {
            if ($this->pdo) {
                $stmt = $this->pdo->prepare("SELECT config_key, config_value FROM system_config");
                $stmt->execute();
                $this->config = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            } else {
                throw new Exception("数据库连接不可用");
            }
        } catch (Exception $e) {
            $this->config = [
                'deepseek_api_url' => '',
                'deepseek_api_key' => '',
                'deepseek_model_name' => 'DeepSeek-R1',
                'qwen_api_url' => '',
                'qwen_api_key' => '',
                'qwen_model_name' => 'qwen-turbo',
                'default_model' => 'DeepSeek-R1',
                'api_timeout' => 60,
                'max_tokens' => 2000,
                'temperature' => 0.7
            ];
        }
    }
    
    public function analyze($prompt, $model = null, $userInfo = []) {
        $sessionId = session_id() ?: uniqid();
        $requestId = uniqid('req_');
        
        error_log("=== ANALYZE START [{$requestId}] ===");
        error_log("Session: {$sessionId}");
        error_log("Save count: " . self::$saveCount);
        
        try {
            if (!$model) {
                $model = $this->config['default_model'] ?? 'DeepSeek-R1';
            }
            
            $apiUrl = $this->config["{$model}_api_url"] ?? '';
            $apiKey = $this->config["{$model}_api_key"] ?? '';
            $modelName = $this->config["{$model}_model_name"] ?? $this->getDefaultModelName($model);
            
            if (empty($apiUrl) || empty($apiKey)) {
                throw new Exception("API配置未完成");
            }
            
            $result = $this->callOpenAICompatibleAPI($apiUrl, $apiKey, $prompt, $model, $modelName);
            $result['request_id'] = $requestId;
            
            // 只在这里保存一次
            $this->saveAnalysisRecord($prompt, $result, $model, $userInfo, $requestId);
            
            error_log("=== ANALYZE SUCCESS [{$requestId}] ===");
            return $result;
            
        } catch (Exception $e) {
            error_log("=== ANALYZE ERROR [{$requestId}]: " . $e->getMessage() . " ===");
            
            $result = [
                'error' => $e->getMessage(),
                'content' => $this->generateFallbackResponse($prompt, $e->getMessage()),
                'model' => $model,
                'request_id' => $requestId
            ];
            
            // 错误时也保存
            $this->saveAnalysisRecord($prompt, $result, $model, $userInfo, $requestId);
            
            return $result;
        }
    }
    
    private function callOpenAICompatibleAPI($baseUrl, $apiKey, $prompt, $model, $modelName) {
        $baseUrl = rtrim($baseUrl, '/');
        if (!$this->str_ends_with($baseUrl, '/v1')) {
            $baseUrl .= '/v1';
        }
        $url = $baseUrl . '/chat/completions';
        
        $data = [
            'model' => $modelName,
            'messages' => [
                [
                    'role' => 'system',
                    'content' => '你是一位经验丰富的专业八字命理师，精通传统命理学，能够根据八字信息提供准确、详细、实用的分析和建议。请用专业而通俗易懂的语言回答用户的问题。'
                ],
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'temperature' => floatval($this->config['temperature'] ?? 0.7),
            'max_tokens' => intval($this->config['max_tokens'] ?? 2000),
            'stream' => false
        ];
        
        if ($model === 'qwen' || strpos($modelName, 'qwen') !== false) {
            $data['enable_thinking'] = false;
            $data['top_p'] = 0.8;
            $data['repetition_penalty'] = 1.1;
        } elseif ($model === 'deepseek' || strpos($modelName, 'deepseek') !== false) {
            $data['top_p'] = 0.95;
        }
        
        $ch = curl_init();
        $timeout = intval($this->config['api_timeout'] ?? 60);
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data, JSON_UNESCAPED_UNICODE),
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey,
                'User-Agent: BaziAI/1.0'
            ],
            CURLOPT_TIMEOUT => $timeout,
            CURLOPT_CONNECTTIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3
        ]);
        
        $start_time = microtime(true);
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        $end_time = microtime(true);
        $request_time = round(($end_time - $start_time), 2);
        
        curl_close($ch);
        
        if ($curlError) {
            throw new Exception("网络请求失败: " . $curlError);
        }
        
        if ($httpCode !== 200) {
            throw new Exception("API请求失败: HTTP {$httpCode}");
        }
        
        if (!$response) {
            throw new Exception("API响应为空");
        }
        
        $result = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("响应JSON解析失败: " . json_last_error_msg());
        }
        
        if (isset($result['error'])) {
            $errorMsg = is_array($result['error']) ? 
                       ($result['error']['message'] ?? json_encode($result['error'])) : 
                       $result['error'];
            throw new Exception("API返回错误: " . $errorMsg);
        }
        
        if (!isset($result['choices'][0]['message']['content'])) {
            throw new Exception("API响应格式错误");
        }
        
        $content = $result['choices'][0]['message']['content'];
        if (empty($content)) {
            throw new Exception("API返回空内容");
        }
        
        return [
            'content' => $content,
            'model' => $modelName,
            'usage' => $result['usage'] ?? [],
            'request_time' => $request_time
        ];
    }
    
    private function saveAnalysisRecord($prompt, $result, $model, $userInfo, $requestId) {
        try {
            // 增加保存计数
            self::$saveCount++;
            
            error_log("SAVE ATTEMPT #{" . self::$saveCount . "} for request {$requestId}");
            
            // 检查表是否存在
            $stmt = $this->pdo->query("SHOW TABLES LIKE 'analysis_records'");
            if ($stmt->rowCount() == 0) {
                error_log("Analysis records table does not exist");
                return;
            }
            
            // 检查是否已经保存过相同的请求
            if (isset($result['request_id'])) {
                $checkStmt = $this->pdo->prepare("SELECT id FROM analysis_records WHERE usage_info LIKE ? LIMIT 1");
                $checkStmt->execute(['%"request_id":"' . $result['request_id'] . '"%']);
                if ($checkStmt->rowCount() > 0) {
                    error_log("Request {$requestId} already saved, skipping");
                    return;
                }
            }
            
            $ipAddress = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            if (strpos($ipAddress, ',') !== false) {
                $ipAddress = trim(explode(',', $ipAddress)[0]);
            }
            
            $userName = trim($userInfo['name'] ?? $userInfo['user_name'] ?? '');
            if (empty($userName)) {
                $userName = $this->extractNameFromPrompt($prompt);
            }
            
            $birthInfo = $this->formatBirthInfo($userInfo);
            $analysisType = $userInfo['analysis_type'] ?? '八字分析';
            $aiResponse = $result['content'] ?? '';
            $aiModel = $result['model'] ?? $model;
            $requestTime = $result['request_time'] ?? null;
            
            // 在usage中添加request_id用于去重
            $usage = $result['usage'] ?? [];
            $usage['request_id'] = $requestId;
            $usage['save_count'] = self::$saveCount;
            
            error_log("Saving: user={$userName}, model={$aiModel}, prompt_len=" . strlen($prompt) . ", response_len=" . strlen($aiResponse));
            
            $stmt = $this->pdo->prepare("
                INSERT INTO analysis_records 
                (user_name, birth_info, analysis_type, ai_model, prompt_content, ai_response, 
                 request_time, usage_info, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $userName ?: null,
                $birthInfo ?: null,
                $analysisType,
                $aiModel,
                $prompt,
                $aiResponse,
                $requestTime,
                json_encode($usage, JSON_UNESCAPED_UNICODE),
                substr($ipAddress, 0, 45),
                substr($_SERVER['HTTP_USER_AGENT'] ?? '', 0, 500)
            ]);
            
            $recordId = $this->pdo->lastInsertId();
            error_log("Record saved successfully with ID: {$recordId} for request {$requestId}");
            
        } catch (Exception $e) {
            error_log("Failed to save record for request {$requestId}: " . $e->getMessage());
        }
    }
    
    private function getDefaultModelName($model) {
        $defaultModels = [
            'deepseek' => 'deepseek-chat',
            'qwen' => 'qwen-turbo',
            'gpt' => 'gpt-3.5-turbo'
        ];
        return $defaultModels[strtolower($model)] ?? 'deepseek-chat';
    }
    
    private function generateFallbackResponse($prompt, $error = null) {
        $analysis = "【系统提示】\n抱歉，AI分析服务暂时不可用";
        if ($error) {
            $analysis .= "（错误：{$error}）";
        }
        $analysis .= "。\n\n以下为基础分析结果：\n\n";
        $analysis .= "根据您提供的八字信息，我为您进行基础分析：\n\n";
        $analysis .= "1. **命理总论**\n您的八字显示出独特的命理格局，整体运势平稳向上，具有不错的发展潜力。\n\n";
        $analysis .= "2. **性格特征**\n您性格较为稳重，做事有条理，具有责任感，在人际交往中表现良好。\n\n";
        $analysis .= "3. **运势概况**\n各方面运势总体平衡，通过努力能够获得相应的回报。建议保持积极心态，把握机遇。\n\n";
        $analysis .= "**说明：** 请联系管理员检查AI服务配置，以获得更详细专业的分析。";
        return $analysis;
    }
    
    private function formatBirthInfo($userInfo) {
        $birthInfo = [];
        if (!empty($userInfo['year'])) $birthInfo[] = "出生年份：{$userInfo['year']}年";
        if (!empty($userInfo['month'])) $birthInfo[] = "月份：{$userInfo['month']}月";
        if (!empty($userInfo['day'])) $birthInfo[] = "日期：{$userInfo['day']}日";
        if (!empty($userInfo['hour'])) $birthInfo[] = "时辰：{$userInfo['hour']}时";
        if (!empty($userInfo['gender'])) $birthInfo[] = "性别：{$userInfo['gender']}";
        if (!empty($userInfo['calendar_type'])) $birthInfo[] = "历法：{$userInfo['calendar_type']}";
        return implode('，', $birthInfo);
    }
    
    private function extractNameFromPrompt($prompt) {
        $patterns = [
            '/姓名[：:]\s*([^\s\n,，。.]{2,4})/u',
            '/我叫([^\s\n,，。.]{2,4})/u',
            '/为([^\s\n,，。.]{2,4})分析/u',
            '/([^\s\n,，。.]{2,4})的八字/u'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $prompt, $matches)) {
                $name = trim($matches[1]);
                if (strlen($name) >= 4 && strlen($name) <= 12) {
                    return $name;
                }
            }
        }
        return '';
    }
}
?>
