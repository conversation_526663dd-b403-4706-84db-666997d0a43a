<?php
class AIAnalyzer {
    private $config;
    private $pdo;
    
    public function __construct() {
        global $pdo;
        $this->pdo = $pdo;
        $this->loadConfig();
    }
   
    
    // 添加PHP兼容性函数
    private function str_ends_with($haystack, $needle) {
        return $needle === '' || substr($haystack, -strlen($needle)) === $needle;
    }
    
    private function loadConfig() {
        try {
            if ($this->pdo) {
                $stmt = $this->pdo->prepare("SELECT config_key, config_value FROM system_config");
                $stmt->execute();
                $this->config = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            } else {
                throw new Exception("数据库连接不可用");
            }
        } catch (Exception $e) {
            // 使用默认配置
            $this->config = [
                'deepseek_api_url' => '',
                'deepseek_api_key' => '',
                'deepseek_model_name' => 'deepseek-chat',
                'qwen_api_url' => '',
                'qwen_api_key' => '',
                'qwen_model_name' => 'qwen-turbo',
                'default_model' => 'deepseek',
                'api_timeout' => 60,
                'max_tokens' => 2000,
                'temperature' => 0.7
            ];
        }
    }
    
    public function analyze($prompt, $model = null) {
        try {
            // 如果没有指定模型，使用默认模型
            if (!$model) {
                $model = $this->config['default_model'] ?? 'deepseek';
            }
            
            error_log("Starting AI analysis with model: {$model}");
            
            // 检查API是否配置
            $apiUrl = isset($this->config["{$model}_api_url"]) ? $this->config["{$model}_api_url"] : '';
            $apiKey = isset($this->config["{$model}_api_key"]) ? $this->config["{$model}_api_key"] : '';
            $modelName = isset($this->config["{$model}_model_name"]) ? $this->config["{$model}_model_name"] : $this->getDefaultModelName($model);
            
            error_log("API URL: {$apiUrl}");
            error_log("Model name: {$modelName}");
            error_log("API Key: " . substr($apiKey, 0, 10) . "...");
            
            if (empty($apiUrl) || empty($apiKey)) {
                throw new Exception("API配置未完成，请在管理后台配置API地址和密钥");
            }
            
            return $this->callOpenAICompatibleAPI($apiUrl, $apiKey, $prompt, $model, $modelName);
            
        } catch (Exception $e) {
            error_log("AI Analysis Error: " . $e->getMessage());
            return [
                'error' => $e->getMessage(),
                'content' => $this->generateFallbackResponse($prompt, $e->getMessage())
            ];
        }
    }
    
    private function callOpenAICompatibleAPI($baseUrl, $apiKey, $prompt, $model, $modelName) {
        // 确保URL格式正确
        $baseUrl = rtrim($baseUrl, '/');
        if (!$this->str_ends_with($baseUrl, '/v1')) {
            $baseUrl .= '/v1';
        }
        $url = $baseUrl . '/chat/completions';
        
        error_log("=== AI API Call Start ===");
        error_log("URL: {$url}");
        error_log("Model: {$model} -> {$modelName}");
        error_log("Prompt length: " . strlen($prompt));
        
        $data = [
            'model' => $modelName,
            'messages' => [
                [
                    'role' => 'system',
                    'content' => '你是一位经验丰富的专业八字命理师，精通传统命理学，能够根据八字信息提供准确、详细、实用的分析和建议。请用专业而通俗易懂的语言回答用户的问题。'
                ],
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'temperature' => floatval($this->config['temperature'] ?? 0.7),
            'max_tokens' => intval($this->config['max_tokens'] ?? 2000),
            'stream' => false
        ];
        
        $jsonData = json_encode($data, JSON_UNESCAPED_UNICODE);
        error_log("Request data prepared, size: " . strlen($jsonData) . " bytes");
        
        // 使用 cURL 发送请求
        $ch = curl_init();
        
        $timeout = intval($this->config['api_timeout'] ?? 60);
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $jsonData,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey,
                'User-Agent: BaziAI/1.0'
            ],
            CURLOPT_TIMEOUT => $timeout,
            CURLOPT_CONNECTTIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3
        ]);
        
        error_log("cURL request initiating with timeout: {$timeout}s");
        $start_time = microtime(true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        
        $end_time = microtime(true);
        $request_time = round(($end_time - $start_time), 2);
        
        error_log("Request completed in {$request_time} seconds");
        error_log("HTTP Code: {$httpCode}");
        error_log("Response size: " . strlen($response) . " bytes");
        
        if ($curlError) {
            error_log("cURL Error: " . $curlError);
        }
        
        curl_close($ch);
        
        if ($curlError) {
            throw new Exception("网络请求失败: " . $curlError);
        }
        
        if ($httpCode !== 200) {
            $errorDetail = "HTTP {$httpCode}";
            if ($response) {
                $errorDetail .= " - " . substr($response, 0, 200);
            }
            error_log("API request failed: " . $errorDetail);
            throw new Exception("API请求失败: " . $errorDetail);
        }
        
        if (!$response) {
            error_log("Empty response received");
            throw new Exception("API响应为空");
        }
        
        // 解析响应
        $result = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("JSON decode error: " . json_last_error_msg());
            error_log("Response preview: " . substr($response, 0, 500));
            throw new Exception("响应JSON解析失败: " . json_last_error_msg());
        }
        
        // 检查API错误
        if (isset($result['error'])) {
            $errorMsg = is_array($result['error']) ? 
                       (isset($result['error']['message']) ? $result['error']['message'] : json_encode($result['error'])) : 
                       $result['error'];
            error_log("API returned error: " . $errorMsg);
            throw new Exception("API返回错误: " . $errorMsg);
        }
        
        // 检查响应格式
        if (!isset($result['choices']) || !is_array($result['choices']) || empty($result['choices'])) {
            error_log("Invalid response format - missing choices");
            error_log("Response structure: " . json_encode(array_keys($result)));
            throw new Exception("API响应格式错误: 缺少choices字段");
        }
        
        $choice = $result['choices'][0];
        if (!isset($choice['message']) || !isset($choice['message']['content'])) {
            error_log("Invalid choice format");
            error_log("Choice structure: " . json_encode(array_keys($choice)));
            throw new Exception("API响应格式错误: 缺少message.content字段");
        }
        
        $content = $choice['message']['content'];
        if (empty($content)) {
            error_log("Empty content received");
            throw new Exception("API返回空内容");
        }
        
        error_log("AI analysis successful, content length: " . strlen($content));
        error_log("=== AI API Call End ===");
        
        return [
            'content' => $content,
            'model' => $modelName,
            'usage' => isset($result['usage']) ? $result['usage'] : [],
            'request_time' => $request_time,
            'config_used' => [
                'temperature' => $data['temperature'],
                'max_tokens' => $data['max_tokens'],
                'timeout' => $timeout
            ]
        ];
    }
    
    private function getDefaultModelName($model) {
        // 默认模型名称映射
        $defaultModels = [
            'deepseek' => 'deepseek-chat',
            'qwen' => 'qwen-turbo',
            'gpt' => 'gpt-3.5-turbo'
        ];
        
        return $defaultModels[strtolower($model)] ?? 'deepseek-chat';
    }
    
    private function generateFallbackResponse($prompt, $error = null) {
        $analysis = "【系统提示】\n";
        $analysis .= "抱歉，AI分析服务暂时不可用";
        
        if ($error) {
            $analysis .= "（错误：{$error}）";
        }
        
        $analysis .= "。\n\n以下为基础分析结果：\n\n";
        
        $analysis .= "根据您提供的八字信息，我为您进行基础分析：\n\n";
        $analysis .= "1. **命理总论**\n您的八字显示出独特的命理格局，整体运势平稳向上，具有不错的发展潜力。\n\n";
        $analysis .= "2. **性格特征**\n您性格较为稳重，做事有条理，具有责任感，在人际交往中表现良好。\n\n";
        $analysis .= "3. **运势概况**\n各方面运势总体平衡，通过努力能够获得相应的回报。建议保持积极心态，把握机遇。\n\n";
        $analysis .= "**说明：** 请联系管理员检查AI服务配置，以获得更详细专业的分析。";
        
        return $analysis;
    }
}
?>
