<?php
require_once __DIR__ . '/../config/config.php';

class AIClient {
    private $apiUrl;
    private $apiKey;
    private $model;
    private $modelType;
    
    public function __construct($modelType = null) {
        $this->modelType = $modelType ?: Config::get('default_model') ?: 'deepseek';
        
        if ($this->modelType === 'deepseek') {
            $this->apiUrl = Config::get('deepseek_api_url') . '/chat/completions';
            $this->apiKey = Config::get('deepseek_api_key');
            $this->model = 'deepseek-chat';
        } else {
            $this->apiUrl = Config::get('qwen_api_url') . '/chat/completions';
            $this->apiKey = Config::get('qwen_api_key');
            $this->model = 'qwen-plus';
        }
    }
    
    public function analyze($name, $gender, $baziData, $baziText, $customQuestion = null) {
        $prompt = $this->buildAdvancedPrompt($name, $gender, $baziData, $baziText, $customQuestion);
        
        $data = [
            'model' => $this->model,
            'messages' => [
                [
                    'role' => 'system',
                    'content' => $this->getSystemPrompt()
                ],
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'temperature' => 0.7,
            'max_tokens' => 4000,
            'stream' => false
        ];
        
        $result = $this->callAPI($data);
        return $result;
    }
    
    private function getSystemPrompt() {
        return '你是一位精通中国传统易学和八字命理的顶级大师，拥有数十年的实践经验和深厚的理论基础。你精通：

1. 八字命理学：天干地支、五行生克、十神关系、格局分析
2. 大运流年：运势周期、吉凶判断、时机把握
3. 实用指导：事业发展、感情婚姻、财运健康、人生规划

你的分析特点：
- 逻辑严密，多重验证，确保准确性
- 既有专业术语，又通俗易懂
- 结合现代生活，提供实用建议
- 详细具体，有理有据

请根据用户提供的八字信息和具体问题，进行专业、详细、准确的命理分析。';
    }
    
    private function buildAdvancedPrompt($name, $gender, $baziData, $baziText, $customQuestion) {
        $prompt = "请为以下命主进行专业的八字命理分析：\n\n";
        $prompt .= "【基本信息】\n";
        $prompt .= "姓名：{$name}\n";
        $prompt .= "性别：{$gender}\n\n";
        
        $prompt .= "【八字命盘】\n";
        $prompt .= $baziText . "\n\n";
        
        // 添加五行分析
        if (isset($baziData['wuXingStats'])) {
            $prompt .= "【五行力量统计】\n";
            foreach ($baziData['wuXingStats'] as $element => $count) {
                $prompt .= "{$element}: {$count}个\n";
            }
            $prompt .= "\n";
        }
        
        // 添加大运信息
        if (isset($baziData['daYun'])) {
            $prompt .= "【大运信息】\n";
            foreach (array_slice($baziData['daYun'], 0, 3) as $dayun) {
                $prompt .= $dayun['period'] . ": " . $dayun['tianGan'] . $dayun['diZhi'] . "\n";
            }
            $prompt .= "\n";
        }
        
        // 添加流年信息
        if (isset($baziData['liuNian'])) {
            $prompt .= "【流年信息】\n";
            foreach ($baziData['liuNian'] as $year) {
                $prompt .= $year['year'] . "年: " . $year['tianGan'] . $year['diZhi'] . " (" . $year['shengXiao'] . ")\n";
            }
            $prompt .= "\n";
        }
        
        // 用户自定义问题或默认分析要求
        if ($customQuestion) {
            $prompt .= "【分析要求】\n";
            $prompt .= $customQuestion . "\n\n";
        } else {
            $prompt .= "【分析要求】\n";
            $prompt .= "请进行全面的八字分析，包括：\n";
            $prompt .= "1. 命局格局分析（身强身弱、用神忌神）\n";
            $prompt .= "2. 性格特征分析\n";
            $prompt .= "3. 事业财运分析\n";
            $prompt .= "4. 感情婚姻分析\n";
            $prompt .= "5. 健康状况分析\n";
            $prompt .= "6. 开运建议和注意事项\n\n";
        }
        
        $prompt .= "请用专业且通俗易懂的语言进行分析，术语和白话并用，内容详细准确。";
        
        return $prompt;
    }
    
    private function callAPI($data) {
        $ch = curl_init();
        
        curl_setopt_array($ch, [
            CURLOPT_URL => $this->apiUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $this->apiKey,
                'User-Agent: BaziAI/1.0'
            ],
            CURLOPT_TIMEOUT => 120,
            CURLOPT_CONNECTTIMEOUT => 30,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_FOLLOWLOCATION => true
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            throw new Exception('API请求失败: ' . $error);
        }
        
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception("API返回错误: HTTP {$httpCode}");
        }
        
        $result = json_decode($response, true);
        
        if (!$result) {
            throw new Exception('API响应解析失败');
        }
        
        if (isset($result['error'])) {
            throw new Exception('API错误: ' . ($result['error']['message'] ?? '未知错误'));
        }
        
        return $result['choices'][0]['message']['content'] ?? '分析结果获取失败';
    }
    
    public function getModelType() {
        return $this->modelType;
    }
}
?>
