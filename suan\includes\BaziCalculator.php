<?php
class BaziCalculator {
    
    // 天干数组
    private $tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸'];
    
    // 地支数组
    private $dizhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥'];
    
    // 时辰对应表
    private $shichen = [
        23 => '子', 0 => '子', 1 => '丑', 2 => '丑', 3 => '寅', 4 => '寅',
        5 => '卯', 6 => '卯', 7 => '辰', 8 => '辰', 9 => '巳', 10 => '巳',
        11 => '午', 12 => '午', 13 => '未', 14 => '未', 15 => '申', 16 => '申',
        17 => '酉', 18 => '酉', 19 => '戌', 20 => '戌', 21 => '亥', 22 => '亥'
    ];
    
    // 五行属性
    private $wuxing = [
        '甲' => '木', '乙' => '木', '丙' => '火', '丁' => '火', '戊' => '土',
        '己' => '土', '庚' => '金', '辛' => '金', '壬' => '水', '癸' => '水',
        '子' => '水', '丑' => '土', '寅' => '木', '卯' => '木', '辰' => '土',
        '巳' => '火', '午' => '火', '未' => '土', '申' => '金', '酉' => '金',
        '戌' => '土', '亥' => '水'
    ];
    
    // 干支组合对应纳音五行
    private $nayin = [
        '甲子' => '海中金', '乙丑' => '海中金', '丙寅' => '炉中火', '丁卯' => '炉中火',
        '戊辰' => '大林木', '己巳' => '大林木', '庚午' => '路旁土', '辛未' => '路旁土',
        '壬申' => '剑锋金', '癸酉' => '剑锋金', '甲戌' => '山头火', '乙亥' => '山头火',
        '丙子' => '涧下水', '丁丑' => '涧下水', '戊寅' => '城头土', '己卯' => '城头土',
        '庚辰' => '白蜡金', '辛巳' => '白蜡金', '壬午' => '杨柳木', '癸未' => '杨柳木',
        '甲申' => '泉中水', '乙酉' => '泉中水', '丙戌' => '屋上土', '丁亥' => '屋上土',
        '戊子' => '霹雳火', '己丑' => '霹雳火', '庚寅' => '松柏木', '辛卯' => '松柏木',
        '壬辰' => '长流水', '癸巳' => '长流水', '甲午' => '砂中金', '乙未' => '砂中金',
        '丙申' => '山下火', '丁酉' => '山下火', '戊戌' => '平地木', '己亥' => '平地木',
        '庚子' => '壁上土', '辛丑' => '壁上土', '壬寅' => '金箔金', '癸卯' => '金箔金',
        '甲辰' => '覆灯火', '乙巳' => '覆灯火', '丙午' => '天河水', '丁未' => '天河水',
        '戊申' => '大驿土', '己酉' => '大驿土', '庚戌' => '钗钏金', '辛亥' => '钗钏金',
        '壬子' => '桑柘木', '癸丑' => '桑柘木', '甲寅' => '大溪水', '乙卯' => '大溪水',
        '丙辰' => '沙中土', '丁巳' => '沙中土', '戊午' => '天上火', '己未' => '天上火',
        '庚申' => '石榴木', '辛酉' => '石榴木', '壬戌' => '大海水', '癸亥' => '大海水'
    ];
    
    /**
     * 安全获取数组值
     */
    private function safeArrayGet($array, $key, $default = '') {
        // 确保键是有效的类型
        if (!is_string($key) && !is_int($key)) {
            return $default;
        }
        
        // 确保数组存在且键存在
        if (!is_array($array) || !isset($array[$key])) {
            return $default;
        }
        
        return $array[$key];
    }
    
    /**
     * 计算八字
     */
    public function calculateBazi($year, $month, $day, $hour, $minute = 0) {
        try {
            // 验证输入参数
            if (!$this->validateInput($year, $month, $day, $hour, $minute)) {
                throw new Exception("输入参数无效");
            }
            
            $timestamp = mktime($hour, $minute, 0, $month, $day, $year);
            
            if ($timestamp === false) {
                throw new Exception("日期时间无效");
            }
            
            // 计算农历信息
            $lunar = $this->calculateLunar($year, $month, $day);
            
            // 计算年柱
            $yearGanZhi = $this->getYearGanZhi($year);
            
            // 计算月柱  
            $monthGanZhi = $this->getMonthGanZhi($year, $month, $yearGanZhi);
            
            // 计算日柱
            $dayGanZhi = $this->getDayGanZhi($year, $month, $day);
            
            // 计算时柱
            $hourGanZhi = $this->getHourGanZhi($hour, $dayGanZhi);
            
            // 计算五行分析
            $wuxingAnalysis = $this->analyzeWuxing($yearGanZhi, $monthGanZhi, $dayGanZhi, $hourGanZhi);
            
            // 计算大运
            $dayun = $this->calculateDayun($yearGanZhi, $monthGanZhi, $dayGanZhi, $hourGanZhi);
            
            return [
                'basic_info' => [
                    'year' => $year,
                    'month' => $month, 
                    'day' => $day,
                    'hour' => $hour,
                    'minute' => $minute
                ],
                'lunar_info' => $lunar,
                'bazi' => [
                    'year' => $yearGanZhi,
                    'month' => $monthGanZhi,
                    'day' => $dayGanZhi, 
                    'hour' => $hourGanZhi
                ],
                'wuxing' => $wuxingAnalysis,
                'dayun' => $dayun,
                'nayin' => [
                    'year' => $this->safeArrayGet($this->nayin, $yearGanZhi['combined'], '未知'),
                    'month' => $this->safeArrayGet($this->nayin, $monthGanZhi['combined'], '未知'),
                    'day' => $this->safeArrayGet($this->nayin, $dayGanZhi['combined'], '未知'),
                    'hour' => $this->safeArrayGet($this->nayin, $hourGanZhi['combined'], '未知')
                ],
                'formatted' => $this->formatBazi($yearGanZhi, $monthGanZhi, $dayGanZhi, $hourGanZhi)
            ];
            
        } catch (Exception $e) {
            error_log("BaziCalculator Error: " . $e->getMessage());
            return [
                'error' => true,
                'message' => $e->getMessage(),
                'basic_info' => [
                    'year' => $year ?? 0,
                    'month' => $month ?? 0,
                    'day' => $day ?? 0,
                    'hour' => $hour ?? 0,
                    'minute' => $minute ?? 0
                ]
            ];
        }
    }
    
    /**
     * 验证输入参数
     */
    private function validateInput($year, $month, $day, $hour, $minute) {
        if (!is_numeric($year) || $year < 1900 || $year > 2100) {
            return false;
        }
        
        if (!is_numeric($month) || $month < 1 || $month > 12) {
            return false;
        }
        
        if (!is_numeric($day) || $day < 1 || $day > 31) {
            return false;
        }
        
        if (!is_numeric($hour) || $hour < 0 || $hour > 23) {
            return false;
        }
        
        if (!is_numeric($minute) || $minute < 0 || $minute > 59) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 计算年柱
     */
    private function getYearGanZhi($year) {
        $ganIndex = ($year - 4) % 10;
        $zhiIndex = ($year - 4) % 12;
        
        // 安全获取天干地支
        $gan = $this->safeArrayGet($this->tiangan, $ganIndex, '甲');
        $zhi = $this->safeArrayGet($this->dizhi, $zhiIndex, '子');
        
        return [
            'gan' => $gan,
            'zhi' => $zhi,
            'combined' => $gan . $zhi,
            'gan_wuxing' => $this->safeArrayGet($this->wuxing, $gan, '木'),
            'zhi_wuxing' => $this->safeArrayGet($this->wuxing, $zhi, '水')
        ];
    }
    
    /**
     * 计算月柱
     */
    private function getMonthGanZhi($year, $month, $yearGanZhi) {
        // 简化的月柱计算（实际应该考虑节气）
        $zhiIndex = ($month + 1) % 12;
        $ganIndex = (array_search($yearGanZhi['gan'], $this->tiangan) * 2 + $month + 1) % 10;
        
        // 确保索引有效
        $ganIndex = max(0, min(9, $ganIndex));
        $zhiIndex = max(0, min(11, $zhiIndex));
        
        $gan = $this->safeArrayGet($this->tiangan, $ganIndex, '甲');
        $zhi = $this->safeArrayGet($this->dizhi, $zhiIndex, '子');
        
        return [
            'gan' => $gan,
            'zhi' => $zhi,
            'combined' => $gan . $zhi,
            'gan_wuxing' => $this->safeArrayGet($this->wuxing, $gan, '木'),
            'zhi_wuxing' => $this->safeArrayGet($this->wuxing, $zhi, '水')
        ];
    }
    
    /**
     * 计算日柱
     */
    private function getDayGanZhi($year, $month, $day) {
        // 使用公历日期计算日柱的简化方法
        $baseTimestamp = mktime(0, 0, 0, 1, 1, 1900);
        $currentTimestamp = mktime(0, 0, 0, $month, $day, $year);
        
        if ($baseTimestamp === false || $currentTimestamp === false) {
            // 如果时间戳计算失败，使用默认值
            return [
                'gan' => '甲',
                'zhi' => '子',
                'combined' => '甲子',
                'gan_wuxing' => '木',
                'zhi_wuxing' => '水'
            ];
        }
        
        $days = floor(($currentTimestamp - $baseTimestamp) / (24 * 60 * 60));
        
        $ganIndex = $days % 10;
        $zhiIndex = $days % 12;
        
        // 确保索引在有效范围内
        $ganIndex = max(0, min(9, $ganIndex));
        $zhiIndex = max(0, min(11, $zhiIndex));
        
        $gan = $this->safeArrayGet($this->tiangan, $ganIndex, '甲');
        $zhi = $this->safeArrayGet($this->dizhi, $zhiIndex, '子');
        
        return [
            'gan' => $gan,
            'zhi' => $zhi,
            'combined' => $gan . $zhi,
            'gan_wuxing' => $this->safeArrayGet($this->wuxing, $gan, '木'),
            'zhi_wuxing' => $this->safeArrayGet($this->wuxing, $zhi, '水')
        ];
    }
    
    /**
     * 计算时柱
     */
    private function getHourGanZhi($hour, $dayGanZhi) {
        // 确保输入有效
        if (!is_numeric($hour) || !isset($dayGanZhi['gan'])) {
            return [
                'gan' => '甲',
                'zhi' => '子',
                'combined' => '甲子',
                'gan_wuxing' => '木',
                'zhi_wuxing' => '水'
            ];
        }
        
        $hour = intval($hour);
        $zhi = $this->safeArrayGet($this->shichen, $hour, '子');
        
        // 根据日干和时支计算时干
        $dayGanIndex = array_search($dayGanZhi['gan'], $this->tiangan);
        if ($dayGanIndex === false) {
            $dayGanIndex = 0;
        }
        
        $zhiIndex = array_search($zhi, $this->dizhi);
        if ($zhiIndex === false) {
            $zhiIndex = 0;
        }
        
        $ganIndex = ($dayGanIndex * 2 + $zhiIndex) % 10;
        $gan = $this->safeArrayGet($this->tiangan, $ganIndex, '甲');
        
        return [
            'gan' => $gan,
            'zhi' => $zhi,
            'combined' => $gan . $zhi,
            'gan_wuxing' => $this->safeArrayGet($this->wuxing, $gan, '木'),
            'zhi_wuxing' => $this->safeArrayGet($this->wuxing, $zhi, '水')
        ];
    }
    
    /**
     * 分析五行
     */
    private function analyzeWuxing($year, $month, $day, $hour) {
        $wuxingCount = ['金' => 0, '木' => 0, '水' => 0, '火' => 0, '土' => 0];
        
        // 安全地统计五行
        $elements = [
            $year['gan_wuxing'] ?? '木',
            $year['zhi_wuxing'] ?? '水',
            $month['gan_wuxing'] ?? '木', 
            $month['zhi_wuxing'] ?? '水',
            $day['gan_wuxing'] ?? '木',
            $day['zhi_wuxing'] ?? '水',
            $hour['gan_wuxing'] ?? '木',
            $hour['zhi_wuxing'] ?? '水'
        ];
        
        foreach ($elements as $element) {
            if (isset($wuxingCount[$element])) {
                $wuxingCount[$element]++;
            }
        }
        
        // 找到最强和最弱的五行
        $max = max($wuxingCount);
        $min = min($wuxingCount);
        $strongest = array_search($max, $wuxingCount);
        $weakest = array_search($min, $wuxingCount);
        
        return [
            'count' => $wuxingCount,
            'strongest' => $strongest ?: '木',
            'weakest' => $weakest ?: '金',
            'balance_score' => $this->calculateBalanceScore($wuxingCount)
        ];
    }
    
    /**
     * 计算五行平衡分数
     */
    private function calculateBalanceScore($wuxingCount) {
        $total = array_sum($wuxingCount);
        if ($total == 0) return 0;
        
        $ideal = $total / 5;
        $variance = 0;
        
        foreach ($wuxingCount as $count) {
            $variance += pow($count - $ideal, 2);
        }
        
        $variance = $variance / 5;
        $standardDeviation = sqrt($variance);
        
        // 转换为100分制，标准差越小分数越高
        return max(0, 100 - ($standardDeviation * 20));
    }
    
    /**
     * 计算简化的农历信息
     */
    private function calculateLunar($year, $month, $day) {
        // 简化实现，仅返回基本信息
        return [
            'year' => $year,
            'month' => $month,
            'day' => $day,
            'is_leap' => false,
            'animal' => $this->getAnimal($year)
        ];
    }
    
    /**
     * 获取生肖
     */
    private function getAnimal($year) {
        $animals = ['鼠', '牛', '虎', '兔', '龙', '蛇', '马', '羊', '猴', '鸡', '狗', '猪'];
        return $this->safeArrayGet($animals, ($year - 4) % 12, '鼠');
    }
    
    /**
     * 计算大运（简化版）
     */
    private function calculateDayun($year, $month, $day, $hour) {
        $dayun = [];
        $startAge = 8; // 简化起运年龄
        
        for ($i = 0; $i < 8; $i++) {
            $age = $startAge + $i * 10;
            $ganIndex = (array_search($month['gan'], $this->tiangan) + $i) % 10;
            $zhiIndex = (array_search($month['zhi'], $this->dizhi) + $i) % 12;
            
            $gan = $this->safeArrayGet($this->tiangan, $ganIndex, '甲');
            $zhi = $this->safeArrayGet($this->dizhi, $zhiIndex, '子');
            
            $dayun[] = [
                'age_start' => $age,
                'age_end' => $age + 9,
                'gan' => $gan,
                'zhi' => $zhi,
                'combined' => $gan . $zhi,
                'nayin' => $this->safeArrayGet($this->nayin, $gan . $zhi, '未知')
            ];
        }
        
        return $dayun;
    }
    
    /**
     * 格式化八字输出
     */
    private function formatBazi($year, $month, $day, $hour) {
        return [
            'ganzhi' => sprintf('%s %s %s %s', 
                $year['combined'] ?? '甲子',
                $month['combined'] ?? '甲子', 
                $day['combined'] ?? '甲子',
                $hour['combined'] ?? '甲子'
            ),
            'tiangan' => sprintf('%s %s %s %s',
                $year['gan'] ?? '甲',
                $month['gan'] ?? '甲',
                $day['gan'] ?? '甲', 
                $hour['gan'] ?? '甲'
            ),
            'dizhi' => sprintf('%s %s %s %s',
                $year['zhi'] ?? '子',
                $month['zhi'] ?? '子',
                $day['zhi'] ?? '子',
                $hour['zhi'] ?? '子'
            )
        ];
    }
}
?>
