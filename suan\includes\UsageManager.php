<?php
class UsageManager {
    private $pdo;
    
    public function __construct() {
        global $pdo;
        $this->pdo = $pdo;
    }
    
    /**
     * 记录使用情况 - 明确设置date_used
     */
    public function recordUsage($userInfo = null, $analysisRecordId = null) {
        try {
            $userId = $userInfo['user_id'] ?? null;
            $phone = $userInfo['phone'] ?? null;
            $ipAddress = $this->getClientIP();
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            $now = date('Y-m-d H:i:s');
            $dateUsed = date('Y-m-d'); // 明确设置今天的日期
            
            $stmt = $this->pdo->prepare("
                INSERT INTO user_usage 
                (user_id, phone, ip_address, user_agent, analysis_record_id, is_free, used_at, date_used) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $result = $stmt->execute([
                $userId,
                $phone,
                substr($ipAddress, 0, 45),
                substr($userAgent, 0, 500),
                $analysisRecordId,
                $userId ? 0 : 1, // 注册用户为付费使用，游客为免费使用
                $now,
                $dateUsed
            ]);
            
            if (!$result) {
                error_log("Insert user_usage failed: " . implode(', ', $stmt->errorInfo()));
                return false;
            }
            
            // 如果是注册用户，扣减次数
            if ($userId) {
                return $this->deductUserBalance($userId);
            }
            
            return true;
            
        } catch (Exception $e) {
            error_log("Record usage error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 检查游客使用情况 - 使用date_used字段
     */
    private function checkGuestUsage($phone, $ipAddress, $today) {
        // 构建查询条件
        $whereConditions = ["date_used = ?"];
        $params = [$today];
        
        if ($phone) {
            $whereConditions[] = "(phone = ? OR ip_address = ?)";
            $params[] = $phone;
            $params[] = $ipAddress;
        } else {
            $whereConditions[] = "ip_address = ?";
            $params[] = $ipAddress;
        }
        
        $whereConditions[] = "user_id IS NULL";
        
        $sql = "
            SELECT COUNT(*) as usage_count 
            FROM user_usage 
            WHERE " . implode(' AND ', $whereConditions);
        
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            $result = $stmt->fetch();
            
            $usageCount = $result['usage_count'] ?? 0;
            
            if ($usageCount >= 1) {
                return [
                    'allowed' => false,
                    'message' => '您今日的免费使用次数已用完，注册账户可获得更多分析机会',
                    'remaining' => 0,
                    'need_register' => true
                ];
            }
            
            return [
                'allowed' => true,
                'message' => '今日还可免费使用 1 次，注册账户享受更多分析机会',
                'remaining' => 1
            ];
            
        } catch (Exception $e) {
            error_log("Check guest usage error: " . $e->getMessage());
            // 出错时允许使用（但会记录错误）
            return [
                'allowed' => true,
                'message' => '系统检查中，今日可使用 1 次',
                'remaining' => 1
            ];
        }
    }
    
    // 其他方法保持不变...
    private function getClientIP() {
        $ipAddress = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        if (strpos($ipAddress, ',') !== false) {
            $ipAddress = trim(explode(',', $ipAddress)[0]);
        }
        return $ipAddress;
    }
    
    public function checkUsagePermission($userInfo = null) {
        try {
            $userId = $userInfo['user_id'] ?? null;
            $phone = $userInfo['phone'] ?? null;
            $ipAddress = $this->getClientIP();
            $today = date('Y-m-d');
            
            if ($userId) {
                return $this->checkRegisteredUserUsage($userId);
            } else {
                return $this->checkGuestUsage($phone, $ipAddress, $today);
            }
            
        } catch (Exception $e) {
            error_log("UsageManager Error: " . $e->getMessage());
            return [
                'allowed' => false,
                'message' => '系统繁忙，请稍后再试',
                'remaining' => 0
            ];
        }
    }
    
    private function checkRegisteredUserUsage($userId) {
        $stmt = $this->pdo->prepare("
            SELECT analysis_count, expires_at 
            FROM user_balance 
            WHERE user_id = ? AND (expires_at IS NULL OR expires_at > NOW())
        ");
        $stmt->execute([$userId]);
        $balance = $stmt->fetch();
        
        if (!$balance || $balance['analysis_count'] <= 0) {
            return [
                'allowed' => false,
                'message' => '您的分析次数已用完，请购买套餐继续使用',
                'remaining' => 0,
                'need_purchase' => true
            ];
        }
        
        $expiryInfo = '';
        if ($balance['expires_at']) {
            $expiryDate = date('Y-m-d', strtotime($balance['expires_at']));
            $expiryInfo = "，有效期至 {$expiryDate}";
        }
        
        return [
            'allowed' => true,
            'message' => "您还有 {$balance['analysis_count']} 次分析机会{$expiryInfo}",
            'remaining' => $balance['analysis_count']
        ];
    }
    
    private function deductUserBalance($userId) {
        $stmt = $this->pdo->prepare("
            UPDATE user_balance 
            SET analysis_count = analysis_count - 1,
                updated_at = NOW()
            WHERE user_id = ? AND analysis_count > 0
        ");
        $stmt->execute([$userId]);
        
        return $stmt->rowCount() > 0;
    }
    
    public function addUserBalance($userId, $analysisCount, $validDays = null) {
        try {
            $expiresAt = null;
            if ($validDays) {
                $expiresAt = date('Y-m-d H:i:s', time() + ($validDays * 24 * 60 * 60));
            }
            
            $stmt = $this->pdo->prepare("
                SELECT id, analysis_count, expires_at 
                FROM user_balance 
                WHERE user_id = ?
            ");
            $stmt->execute([$userId]);
            $existing = $stmt->fetch();
            
            if ($existing) {
                $newCount = $existing['analysis_count'] + $analysisCount;
                
                $newExpiresAt = $expiresAt;
                if ($existing['expires_at'] && $expiresAt) {
                    $newExpiresAt = max($existing['expires_at'], $expiresAt);
                } elseif ($existing['expires_at']) {
                    $newExpiresAt = $existing['expires_at'];
                }
                
                $stmt = $this->pdo->prepare("
                    UPDATE user_balance 
                    SET analysis_count = ?, expires_at = ?, updated_at = NOW()
                    WHERE user_id = ?
                ");
                $stmt->execute([$newCount, $newExpiresAt, $userId]);
            } else {
                $stmt = $this->pdo->prepare("
                    INSERT INTO user_balance (user_id, analysis_count, expires_at) 
                    VALUES (?, ?, ?)
                ");
                $stmt->execute([$userId, $analysisCount, $expiresAt]);
            }
            
            return true;
            
        } catch (Exception $e) {
            error_log("Add user balance error: " . $e->getMessage());
            return false;
        }
    }
}
?>
