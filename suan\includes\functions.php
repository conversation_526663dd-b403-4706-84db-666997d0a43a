<?php
require_once __DIR__ . '/../config/database.php';

function saveAnalysisRecord($name, $gender, $year, $month, $day, $hour, $minute, $baziResult, $aiAnalysis, $sessionId = null, $analysisType = 'basic', $question = '') {
    global $pdo;
    
    $stmt = $pdo->prepare("INSERT INTO analysis_records (session_id, name, gender, birth_year, birth_month, birth_day, birth_hour, birth_minute, bazi_result, ai_analysis, question, analysis_type, ip_address) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $result = $stmt->execute([
        $sessionId ?: session_id(),
        $name, $gender, $year, $month, $day, $hour, $minute,
        json_encode($baziResult, JSON_UNESCAPED_UNICODE), 
        $aiAnalysis, 
        $question,
        $analysisType,
        $_SERVER['REMOTE_ADDR'] ?? ''
    ]);
    
    return $result ? $pdo->lastInsertId() : false;
}

function getAnalysisRecord($recordId) {
    global $pdo;
    
    $stmt = $pdo->prepare("SELECT * FROM analysis_records WHERE id = ?");
    $stmt->execute([$recordId]);
    
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

function validateInput($name, $gender, $year, $month, $day, $hour) {
    $errors = [];
    
    // 姓名验证
    if (empty($name)) {
        $errors[] = '姓名不能为空';
    } elseif (mb_strlen($name) > 20) {
        $errors[] = '姓名长度不能超过20个字符';
    } elseif (mb_strlen($name) < 2) {
        $errors[] = '姓名至少需要2个字符';
    }
    
    // 性别验证
    if (!in_array($gender, ['男', '女'])) {
        $errors[] = '性别选择无效';
    }
    
    // 年份验证
    if (!is_numeric($year) || $year < 1900 || $year > date('Y')) {
        $errors[] = '出生年份无效（1900-' . date('Y') . '）';
    }
    
    // 月份验证
    if (!is_numeric($month) || $month < 1 || $month > 12) {
        $errors[] = '出生月份无效（1-12）';
    }
    
    // 日期验证
    if (!is_numeric($day) || $day < 1 || $day > 31) {
        $errors[] = '出生日期无效（1-31）';
    } elseif (is_numeric($year) && is_numeric($month)) {
        // 验证具体月份的日期
        $maxDay = date('t', mktime(0, 0, 0, $month, 1, $year));
        if ($day > $maxDay) {
            $errors[] = "该月份最多只有{$maxDay}天";
        }
    }
    
    // 小时验证
    if (!is_numeric($hour) || $hour < 0 || $hour > 23) {
        $errors[] = '出生小时无效（0-23）';
    }
    
    return $errors;
}

function formatDateTime($year, $month, $day, $hour, $minute) {
    return sprintf('%04d年%02d月%02d日 %02d:%02d', $year, $month, $day, $hour, $minute);
}

function escape($text) {
    return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
}

function getClientIP() {
    $ipKeys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}

function sanitizeString($string, $maxLength = 255) {
    $string = strip_tags($string);
    $string = trim($string);
    if (strlen($string) > $maxLength) {
        $string = mb_substr($string, 0, $maxLength, 'UTF-8');
    }
    return $string;
}

function isValidDate($year, $month, $day) {
    return checkdate($month, $day, $year);
}

function logError($message, $context = []) {
    $logData = [
        'timestamp' => date('Y-m-d H:i:s'),
        'message' => $message,
        'context' => $context,
        'ip' => getClientIP()
    ];
    
    $logFile = __DIR__ . '/../logs/error.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents($logFile, json_encode($logData) . "\n", FILE_APPEND | LOCK_EX);
}

// 错误处理函数
function handleException($exception) {
    logError('Uncaught exception', [
        'message' => $exception->getMessage(),
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString()
    ]);
    
    if (!headers_sent()) {
        http_response_code(500);
        header('Content-Type: application/json');
    }
    
    echo json_encode([
        'success' => false,
        'error' => '系统内部错误，请稍后重试'
    ], JSON_UNESCAPED_UNICODE);
}

// 设置异常处理器
set_exception_handler('handleException');
?>
