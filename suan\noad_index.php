<?php
session_start();
require_once 'config/config.php';

$siteTitle = Config::get('site_title') ?: 'AI八字分析工具';
$siteSubtitle = Config::get('site_subtitle') ?: '传统智慧与现代AI完美结合';

// 获取问题模板
$questionTemplates = Config::getQuestionTemplates();
$categories = array_unique(array_column($questionTemplates, 'category'));
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>免费AI八字命理分析系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .card-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50px;
            padding: 12px 30px;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-content {
            background: white;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 90%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }
        
        .spinner-border-custom {
            width: 3rem;
            height: 3rem;
            color: #667eea;
        }
        
        .result-section {
            display: none;
            margin-top: 2rem;
        }
        
        .bazi-display {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin: 20px 0;
        }
        
        .wuxing-analysis {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
        }
        
        /* 广告位样式 */
        .ad-container {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            border: 2px solid #f39c12;
            position: relative;
            overflow: visible;
            min-height: 150px;
        }

        .ad-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 3s infinite;
            z-index: 0;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .ad-content {
            position: relative;
            z-index: 2;
            width: 100%;
        }

        .adsbygoogle {
            display: block !important;
            min-height: 100px;
            background: rgba(255,255,255,0.1);
        }

        /* 广告弹窗样式 */
        .ad-popup-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            backdrop-filter: blur(5px);
        }

        .ad-popup-content {
            background: white;
            border-radius: 20px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            animation: popupSlideIn 0.3s ease-out;
        }

        @keyframes popupSlideIn {
            from {
                opacity: 0;
                transform: scale(0.8) translateY(-50px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .ad-popup-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 20px 20px 0 0;
            text-align: center;
        }

        .countdown-timer {
            margin-top: 15px;
        }

        #countdownText {
            font-size: 16px;
            font-weight: bold;
        }

        #countdown {
            color: #ffd700;
            font-size: 20px;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        .ad-popup-body {
            padding: 20px;
            min-height: 250px;
        }

        .ad-content-popup {
            width: 100%;
            min-height: 250px;
            background: #f8f9fa;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .ad-popup-footer {
            padding: 15px 20px;
            background: #f8f9fa;
            border-radius: 0 0 20px 20px;
            text-align: center;
        }

        /* 响应式设计 */
        @media (max-width: 576px) {
            .ad-popup-content {
                width: 95%;
                margin: 10px;
            }
        }
        
        .progress-container {
            margin: 20px 0;
        }
        
        .progress-bar-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: width 0.3s ease;
        }
        
        .loading-tips {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .tip-item {
            margin: 8px 0;
            color: #666;
            font-size: 14px;
        }
        
        /* 广告拦截器警告样式 */
        .ad-blocker-warning {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.85);
            z-index: 10001;
            color: white;
            text-align: center;
            display: none;
        }
        
        .ad-blocker-content {
            max-width: 500px;
            margin: 100px auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            color: #333;
        }
    </style>
</head>
<body class="gradient-bg">
    <!-- 广告拦截警告 -->
    <div class="ad-blocker-warning" id="adBlockerWarning">
        <div class="ad-blocker-content">
            <i class="bi bi-shield-exclamation" style="font-size: 48px; color: #dc3545;"></i>
            <h3 class="mt-3">检测到广告拦截器</h3>
            <p class="mt-3">我们发现您正在使用广告拦截器。我们的网站通过广告收入来提供免费服务。</p>
            <p>请禁用广告拦截器并刷新页面以继续使用。</p>
            <button class="btn btn-primary mt-3" onclick="checkAgainForAdBlocker()">
                <i class="bi bi-arrow-clockwise"></i> 我已禁用，重新检测
            </button>
        </div>
    </div>

    <!-- 广告拦截检测测试元素 -->
    <div id="adBlockerDetection" style="height: 1px; width: 1px; position: absolute; bottom: -1px; opacity: 0.01;">
        <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-6209690704926515" crossorigin="anonymous"></script>
        <ins class="adsbygoogle"
             style="display:block;width:1px;height:1px"
             data-ad-client="ca-pub-6209690704926515"
             data-ad-slot="9999999999"></ins>
        <script>
             (adsbygoogle = window.adsbygoogle || []).push({});
        </script>
    </div>

    <!-- 广告弹窗 -->
    <div class="ad-popup-overlay" id="adPopupOverlay">
        <div class="ad-popup-content">
            <div class="ad-popup-header">
                <h5><i class="bi bi-play-circle"></i> 观看广告解锁AI分析</h5>
                <div class="countdown-timer">
                    <span id="countdownText">请观看广告 <span id="countdown">10</span> 秒</span>
                    <button id="skipButton" class="btn btn-success btn-sm" style="display: none;" onclick="skipAd()">
                        <i class="bi bi-check-circle"></i> 继续分析
                    </button>
                </div>
            </div>
            
            <div class="ad-popup-body">
                <!-- Google AdSense 广告 -->
                <div class="ad-content-popup" id="adContentPopup">
                    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-6209690704926515"
                     crossorigin="anonymous"></script>
                    <!-- Ad2025 -->
                    <ins class="adsbygoogle"
                         style="display:inline-block;width:300px;height:250px"
                         data-ad-client="ca-pub-6209690704926515"
                         data-ad-slot="2941694776"></ins>
                    <script>
                         (adsbygoogle = window.adsbygoogle || []).push({});
                    </script>
                </div>
            </div>
            
            <div class="ad-popup-footer">
                <small class="text-muted">
                    <i class="bi bi-info-circle"></i> 
                    观看广告支持我们提供免费服务，感谢您的支持！
                </small>
            </div>
        </div>
    </div>

    <!-- 加载提示 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner-border spinner-border-custom" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            
            <!-- 进度条 -->
            <div class="progress-container">
                <div class="progress" style="height: 8px; border-radius: 4px;">
                    <div class="progress-bar progress-bar-custom" id="loadingProgress" 
                         role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                    </div>
                </div>
                <small class="text-muted mt-2 d-block">分析进度: <span id="progressText">0%</span></small>
            </div>
            
            <div class="mt-3">
                <h5 id="loadingTitle">AI正在分析您的八字...</h5>
                <p id="loadingSubtitle">请稍候，这可能需要一点时间</p>
            </div>
            
            <!-- 加载提示 -->
            <div class="loading-tips">
                <div class="tip-item">
                    <i class="bi bi-lightbulb text-warning"></i> 
                    <span id="currentTip">正在计算您的生辰八字...</span>
                </div>
            </div>
        </div>
    </div>

    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card card-custom">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <h1 class="h2 text-gradient"><i class="bi bi-stars"></i> 免费AI八字命理分析</h1>
                            <p class="text-muted">基于传统命理学与现代AI技术的智能分析系统</p>
                        </div>

                        <!-- 输入表单 -->
                        <form id="baziForm" class="needs-validation" novalidate>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="name" class="form-label">
                                        <i class="bi bi-person"></i> 姓名
                                    </label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                    <div class="invalid-feedback">请输入您的姓名</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="gender" class="form-label">
                                        <i class="bi bi-gender-ambiguous"></i> 性别
                                    </label>
                                    <select class="form-control" id="gender" name="gender" required>
                                        <option value="">请选择性别</option>
                                        <option value="男">男</option>
                                        <option value="女">女</option>
                                    </select>
                                    <div class="invalid-feedback">请选择性别</div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="bi bi-calendar"></i> 出生日期
                                </label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <input type="number" class="form-control" id="year" name="year" 
                                               placeholder="年份" min="1900" max="2100" required>
                                        <div class="invalid-feedback">请输入有效年份</div>
                                    </div>
                                    <div class="col-md-4">
                                        <select class="form-control" id="month" name="month" required>
                                            <option value="">月份</option>
                                            <option value="1">1月</option>
                                            <option value="2">2月</option>
                                            <option value="3">3月</option>
                                            <option value="4">4月</option>
                                            <option value="5">5月</option>
                                            <option value="6">6月</option>
                                            <option value="7">7月</option>
                                            <option value="8">8月</option>
                                            <option value="9">9月</option>
                                            <option value="10">10月</option>
                                            <option value="11">11月</option>
                                            <option value="12">12月</option>
                                        </select>
                                        <div class="invalid-feedback">请选择月份</div>
                                    </div>
                                    <div class="col-md-4">
                                        <input type="number" class="form-control" id="day" name="day" 
                                               placeholder="日期" min="1" max="31" required>
                                        <div class="invalid-feedback">请输入有效日期</div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="bi bi-clock"></i> 出生时间
                                </label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <select class="form-control" id="hour" name="hour" required>
                                            <option value="">小时</option>
                                            <?php for($h = 0; $h < 24; $h++): ?>
                                            <option value="<?php echo $h; ?>"><?php echo sprintf('%02d时', $h); ?></option>
                                            <?php endfor; ?>
                                        </select>
                                        <div class="invalid-feedback">请选择小时</div>
                                    </div>
                                    <div class="col-md-6">
                                        <select class="form-control" id="minute" name="minute" required>
                                            <option value="">分钟</option>
                                            <?php for($m = 0; $m < 60; $m += 5): ?>
                                            <option value="<?php echo $m; ?>"><?php echo sprintf('%02d分', $m); ?></option>
                                            <?php endfor; ?>
                                        </select>
                                        <div class="invalid-feedback">请选择分钟</div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="question" class="form-label">
                                    <i class="bi bi-question-circle"></i> 您想了解什么？
                                </label>
                                <textarea class="form-control" id="question" name="question" rows="3" 
                                          placeholder="请输入您想了解的问题，例如：事业发展、感情婚姻、健康状况、财运分析等..." required></textarea>
                                <div class="invalid-feedback">请输入您想了解的问题</div>
                                <div class="form-text">
                                    <small class="text-muted">
                                        <i class="bi bi-lightbulb"></i> 
                                        建议问题示例：我的事业发展前景如何？什么时候适合结婚？
                                    </small>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="model" class="form-label">
                                    <i class="bi bi-cpu"></i> AI模型选择
                                </label>
                                <select class="form-control" id="model" name="model">
                                    <option value="deepseek">DeepSeek（推荐）</option>
                                    <option value="qwen">通义千问</option>
                                </select>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-magic"></i> 开始AI分析
                                </button>
                            </div>
                        </form>

                        <!-- 结果显示区域 -->
                        <div id="resultSection" class="result-section">
                            <hr class="my-4">
                            <div id="resultContent"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 广告拦截器检测
            function detectAdBlocker() {
                setTimeout(function() {
                    const testAd = document.querySelector('#adBlockerDetection ins.adsbygoogle');
                    const isAdBlocked = !testAd || 
                                        testAd.clientHeight === 0 || 
                                        testAd.style.display === 'none' || 
                                        window.getComputedStyle(testAd).display === 'none';
                    
                    if (isAdBlocked) {
                        document.getElementById('adBlockerWarning').style.display = 'block';
                        document.body.style.overflow = 'hidden'; // 防止滚动
                    }
                }, 2000); // 延迟2秒检测，确保广告有时间加载
            }

            // 重新检测
            window.checkAgainForAdBlocker = function() {
                const testAd = document.querySelector('#adBlockerDetection ins.adsbygoogle');
                const isAdBlocked = !testAd || 
                                    testAd.clientHeight === 0 || 
                                    testAd.style.display === 'none' || 
                                    window.getComputedStyle(testAd).display === 'none';
                
                if (!isAdBlocked) {
                    document.getElementById('adBlockerWarning').style.display = 'none';
                    document.body.style.overflow = 'auto'; // 恢复滚动
                } else {
                    alert('请禁用广告拦截器并刷新页面以继续使用。');
                }
            }

            // 页面加载完成后执行广告拦截检测
            detectAdBlocker();
            
            const form = document.getElementById('baziForm');
            const loadingOverlay = document.getElementById('loadingOverlay');
            const resultSection = document.getElementById('resultSection');
            const resultContent = document.getElementById('resultContent');

            // 广告倒计时相关变量
            let adCountdown = 10;
            let countdownInterval;
            let formDataToSubmit = null;

            // 生成时间选项
            generateTimeOptions();

            // 修改表单提交事件
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                if (!form.checkValidity()) {
                    e.stopPropagation();
                    form.classList.add('was-validated');
                    return;
                }

                // 收集表单数据
                formDataToSubmit = {
                    name: document.getElementById('name').value.trim(),
                    gender: document.getElementById('gender').value,
                    year: parseInt(document.getElementById('year').value),
                    month: parseInt(document.getElementById('month').value),
                    day: parseInt(document.getElementById('day').value),
                    hour: parseInt(document.getElementById('hour').value),
                    minute: parseInt(document.getElementById('minute').value),
                    question: document.getElementById('question').value.trim(),
                    model: document.getElementById('model').value
                };

                console.log('发送数据:', formDataToSubmit);

                // 验证数据
                if (!validateFormData(formDataToSubmit)) {
                    return;
                }

                // 显示广告弹窗而不是直接分析
                showAdPopup();
            });

            // 显示广告弹窗
            function showAdPopup() {
                const adPopup = document.getElementById('adPopupOverlay');
                adPopup.style.display = 'flex';
                
                // 重置倒计时
                adCountdown = 10;
                document.getElementById('countdown').textContent = adCountdown;
                document.getElementById('countdownText').style.display = 'inline';
                document.getElementById('skipButton').style.display = 'none';
                
                // 开始倒计时
                countdownInterval = setInterval(() => {
                    adCountdown--;
                    document.getElementById('countdown').textContent = adCountdown;
                    
                    if (adCountdown <= 0) {
                        clearInterval(countdownInterval);
                        // 倒计时结束，显示跳过按钮
                        document.getElementById('countdownText').style.display = 'none';
                        document.getElementById('skipButton').style.display = 'inline-block';
                    }
                }, 1000);
            }

            // 跳过广告，开始分析
            window.skipAd = function() {
                // 隐藏广告弹窗
                document.getElementById('adPopupOverlay').style.display = 'none';
                
                // 清除倒计时
                if (countdownInterval) {
                    clearInterval(countdownInterval);
                }
                
                // 开始真正的分析
                startAnalysis();
            }

            // 开始分析（原来的分析逻辑）
            function startAnalysis() {
                // 显示加载状态
                showLoading();

                // 发送请求
                fetch('result.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formDataToSubmit)
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络请求失败');
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('响应数据:', data);
                    hideLoading();
                    
                    if (data.success) {
                        displayResult(data.data);
                    } else {
                        throw new Error(data.error || '分析失败');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    hideLoading();
                    showError('分析失败：' + error.message);
                });
            }

            // 防止用户直接关闭弹窗
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && document.getElementById('adPopupOverlay').style.display === 'flex') {
                    e.preventDefault();
                    if (adCountdown <= 0) {
                        skipAd();
                    }
                }
            });

            function generateTimeOptions() {
                const hourSelect = document.getElementById('hour');
                const minuteSelect = document.getElementById('minute');

                // 生成小时选项
                hourSelect.innerHTML = '<option value="">小时</option>';
                for (let i = 0; i < 24; i++) {
                    const option = document.createElement('option');
                    option.value = i;
                    option.textContent = String(i).padStart(2, '0') + '时';
                    hourSelect.appendChild(option);
                }

                // 生成分钟选项
                minuteSelect.innerHTML = '<option value="">分钟</option>';
                for (let i = 0; i < 60; i += 5) {
                    const option = document.createElement('option');
                    option.value = i;
                    option.textContent = String(i).padStart(2, '0') + '分';
                    minuteSelect.appendChild(option);
                }
            }

            function validateFormData(data) {
                const errors = [];

                if (!data.name) errors.push('请输入姓名');
                if (!data.gender) errors.push('请选择性别');
                if (!data.year || data.year < 1900 || data.year > 2100) errors.push('请输入有效年份');
                if (!data.month || data.month < 1 || data.month > 12) errors.push('请选择有效月份');
                if (!data.day || data.day < 1 || data.day > 31) errors.push('请输入有效日期');
                if (data.hour === undefined || data.hour < 0 || data.hour > 23) errors.push('请选择有效小时');
                if (data.minute === undefined || data.minute < 0 || data.minute > 59) errors.push('请选择有效分钟');
                if (!data.question) errors.push('请输入您想了解的问题');

                // 检查日期有效性
                if (data.year && data.month && data.day) {
                    const date = new Date(data.year, data.month - 1, data.day);
                                        if (date.getFullYear() !== data.year || 
                        date.getMonth() !== data.month - 1 || 
                        date.getDate() !== data.day) {
                        errors.push('请输入有效的日期');
                    }
                }

                if (errors.length > 0) {
                    showError(errors.join('\n'));
                    return false;
                }

                return true;
            }

            // 加载状态管理
            let progressInterval;
            let tipInterval;
            
            const loadingTips = [
                '正在计算您的生辰八字...',
                '正在分析五行属性...',
                '正在查询天干地支...',
                '正在计算大运流年...',
                'AI正在深度学习您的命理特征...',
                '正在生成个性化分析报告...',
                '即将完成，请稍候...'
            ];

            function showLoading() {
                loadingOverlay.style.display = 'flex';
                resultSection.style.display = 'none';
                
                // 重置进度
                let progress = 0;
                let tipIndex = 0;
                
                // 更新进度条
                progressInterval = setInterval(() => {
                    progress += Math.random() * 15 + 5;
                    if (progress > 95) progress = 95;
                    
                    document.getElementById('loadingProgress').style.width = progress + '%';
                    document.getElementById('progressText').textContent = Math.round(progress) + '%';
                }, 800);
                
                // 更新提示文字
                tipInterval = setInterval(() => {
                    document.getElementById('currentTip').textContent = loadingTips[tipIndex];
                    tipIndex = (tipIndex + 1) % loadingTips.length;
                }, 2000);
            }

            function hideLoading() {
                loadingOverlay.style.display = 'none';
                
                // 清除定时器
                if (progressInterval) {
                    clearInterval(progressInterval);
                    progressInterval = null;
                }
                if (tipInterval) {
                    clearInterval(tipInterval);
                    tipInterval = null;
                }
                
                // 完成进度条
                document.getElementById('loadingProgress').style.width = '100%';
                document.getElementById('progressText').textContent = '100%';
            }

            function showError(message) {
                resultContent.innerHTML = `
                    <div class="alert alert-danger" role="alert">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>错误：</strong>${message}
                    </div>
                `;
                resultSection.style.display = 'block';
            }

            function displayResult(data) {
                const bazi = data.bazi.formatted.ganzhi || '未知';
                const analysis = data.analysis || '分析结果获取失败';
                
                resultContent.innerHTML = `
                    <div class="text-center mb-4">
                        <h3><i class="bi bi-person-badge"></i> ${data.name} 的八字分析</h3>
                        <p class="text-muted">性别：${data.gender} | 模型：${data.model_used}</p>
                    </div>

                    <div class="bazi-display">
                        <h4><i class="bi bi-yin-yang"></i> 四柱八字</h4>
                        <h2>${bazi}</h2>
                        <p>生肖：${data.bazi.lunar_info?.animal || '未知'}</p>
                    </div>

                    <div class="wuxing-analysis">
                        <h5><i class="bi bi-pentagon"></i> 五行分析</h5>
                        <div class="row text-center">
                            ${Object.entries(data.bazi.wuxing?.count || {}).map(([element, count]) => 
                                `<div class="col">
                                    <div class="fw-bold">${element}</div>
                                    <div class="h4 text-primary">${count}</div>
                                </div>`
                            ).join('')}
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">
                                平衡分数：${Math.round(data.bazi.wuxing?.balance_score || 0)}分 |
                                最强：${data.bazi.wuxing?.strongest || '未知'} |
                                最弱：${data.bazi.wuxing?.weakest || '未知'}
                            </small>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h5><i class="bi bi-robot"></i> AI命理分析</h5>
                            <small class="text-muted">问题：${data.question}</small>
                        </div>
                        <div class="card-body">
                            <div class="analysis-content">${formatAnalysis(analysis)}</div>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <button type="button" class="btn btn-outline-primary" onclick="window.location.reload()">
                            <i class="bi bi-arrow-clockwise"></i> 重新分析
                        </button>
                    </div>
                `;

                resultSection.style.display = 'block';
                resultSection.scrollIntoView({ behavior: 'smooth' });
            }

            function formatAnalysis(text) {
                return text.split('\n').map(line => {
                    line = line.trim();
                    if (line.match(/^\d+\./)) {
                        return `<h6 class="fw-bold text-primary mt-3">${line}</h6>`;
                    } else if (line.length > 0) {
                        return `<p>${line}</p>`;
                    }
                    return '';
                }).join('');
            }
        });
    </script>
    <script type="text/javascript">
    (function(c,l,a,r,i,t,y){
        c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
        t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
        y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
    })(window, document, "clarity", "script", "jtxjtkxsi8");
</script>
</body>
</html>
