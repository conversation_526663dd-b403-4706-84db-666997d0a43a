<?php
session_start();
require_once 'config/database.php';

// 获取套餐列表
try {
    $stmt = $pdo->query("
        SELECT * FROM packages 
        WHERE is_active = 1 
        ORDER BY sort_order ASC, price ASC
    ");
    $packages = $stmt->fetchAll();
} catch (Exception $e) {
    $packages = [];
    $error = '获取套餐信息失败';
}

$isLoggedIn = isset($_SESSION['user_id']);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>套餐选择 - AI八字分析</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .package-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            height: 100%;
        }
        .package-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
        }
        .package-card.featured {
            border: 3px solid #ffd700;
            position: relative;
        }
        .package-card.featured::before {
            content: '推荐';
            position: absolute;
            top: -10px;
            right: 20px;
            background: #ffd700;
            color: #333;
            padding: 5px 15px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        .price {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
        }
        .btn-purchase {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50px;
            padding: 12px 30px;
            color: white;
            text-decoration: none;
            transition: all 0.3s;
        }
        .btn-purchase:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body class="gradient-bg">
    <div class="container py-5">
        <!-- 导航栏 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <a href="index.php" class="text-white text-decoration-none">
                        <i class="bi bi-arrow-left"></i> 返回首页
                    </a>
                    <?php if ($isLoggedIn): ?>
                    <div class="text-white">
                        <i class="bi bi-person-circle"></i> 
                        欢迎，<?php echo htmlspecialchars($_SESSION['user_name'] ?: $_SESSION['user_phone']); ?>
                        <a href="logout.php" class="text-white ms-3">
                            <i class="bi bi-box-arrow-right"></i> 退出
                        </a>
                    </div>
                    <?php else: ?>
                    <div>
                        <a href="login.php" class="text-white text-decoration-none me-3">
                            <i class="bi bi-box-arrow-in-right"></i> 登录
                        </a>
                        <a href="register.php" class="text-white text-decoration-none">
                            <i class="bi bi-person-plus"></i> 注册
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- 页面标题 -->
        <div class="text-center mb-5">
            <h1 class="text-white mb-3">
                <i class="fas fa-crown"></i> 选择您的专属套餐
            </h1>
            <p class="text-white opacity-75 fs-5">
                解锁更多AI八字分析机会，深度了解您的命理运势
            </p>
        </div>

        <?php if (!$isLoggedIn): ?>
        <div class="alert alert-warning text-center mb-4">
            <i class="bi bi-info-circle"></i> 
            购买套餐需要先 <a href="register.php" class="alert-link">注册账户</a> 或 <a href="login.php" class="alert-link">登录</a>
        </div>
        <?php endif; ?>

        <!-- 套餐列表 -->
        <div class="row">
            <?php if (empty($packages)): ?>
            <div class="col-12">
                <div class="text-center text-white">
                    <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                    <h3>暂无可用套餐</h3>
                    <p>管理员还未配置套餐信息</p>
                </div>
            </div>
            <?php else: ?>
            <?php foreach ($packages as $index => $package): ?>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="package-card <?php echo $index === 1 ? 'featured' : ''; ?>">
                    <div class="card-body p-4 text-center">
                        <h4 class="card-title mb-3"><?php echo htmlspecialchars($package['name']); ?></h4>
                        
                        <div class="price mb-3">
                            ¥<?php echo number_format($package['price'], 2); ?>
                        </div>
                        
                        <p class="text-muted mb-4">
                            <?php echo htmlspecialchars($package['description']); ?>
                        </p>
                        
                        <div class="mb-4">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="h4 text-primary"><?php echo $package['analysis_count']; ?></div>
                                    <small class="text-muted">分析次数</small>
                                </div>
                                <div class="col-6">
                                    <div class="h4 text-success"><?php echo $package['valid_days']; ?></div>
                                    <small class="text-muted">有效天数</small>
                                </div>
                            </div>
                        </div>
                        
                        <?php if (!empty($package['features'])): ?>
                        <?php 
                        $features = json_decode($package['features'], true);
                        if (is_array($features)): 
                        ?>
                        <ul class="feature-list mb-4">
                            <?php foreach ($features as $feature): ?>
                            <li>
                                <i class="fas fa-check text-success me-2"></i>
                                <?php echo htmlspecialchars($feature); ?>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                        <?php endif; ?>
                        <?php endif; ?>
                        
                        <?php if ($isLoggedIn): ?>
                        <a href="payment.php?package_id=<?php echo $package['id']; ?>" 
                           class="btn btn-purchase w-100">
                            <i class="fas fa-shopping-cart"></i> 立即购买
                        </a>
                        <?php else: ?>
                        <a href="register.php" class="btn btn-purchase w-100">
                            <i class="fas fa-user-plus"></i> 注册购买
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <!-- 购买说明 -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card" style="background: rgba(255, 255, 255, 0.9); border-radius: 15px;">
                    <div class="card-body p-4">
                        <h5><i class="fas fa-info-circle text-primary"></i> 购买说明</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i> 支持支付宝、微信支付</li>
                                    <li><i class="fas fa-check text-success me-2"></i> 购买后立即到账</li>
                                    <li><i class="fas fa-check text-success me-2"></i> 有效期内无限制使用</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success me-2"></i> 专业AI分析引擎</li>
                                    <li><i class="fas fa-check text-success me-2"></i> 7×24小时客服支持</li>
                                    <li><i class="fas fa-check text-success me-2"></i> 数据安全保障</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
