<?php
session_start();
require_once 'config/database.php';

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $phone = trim($_POST['phone'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $name = trim($_POST['name'] ?? '');
    $email = trim($_POST['email'] ?? '');

    // 验证输入
    if (empty($phone)) {
        $error = '请输入手机号';
    } elseif (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
        $error = '请输入有效的手机号';
    } elseif (empty($password)) {
        $error = '请输入密码';
    } elseif (strlen($password) < 6) {
        $error = '密码长度至少6位';
    } elseif ($password !== $confirmPassword) {
        $error = '两次输入的密码不一致';
    } else {
        try {
            // 检查手机号是否已存在
            $stmt = $pdo->prepare("SELECT id FROM users WHERE phone = ?");
            $stmt->execute([$phone]);
            if ($stmt->fetch()) {
                $error = '该手机号已注册';
            } else {
                // 创建用户
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("
                    INSERT INTO users (phone, password, name, email, status) 
                    VALUES (?, ?, ?, ?, 'active')
                ");
                $stmt->execute([$phone, $hashedPassword, $name, $email]);
                
                $userId = $pdo->lastInsertId();
                
                // 给新用户赠送3次免费分析
                if (class_exists('UsageManager')) {
                    $usageManager = new UsageManager();
                    $usageManager->addUserBalance($userId, 3, 30); // 3次，30天有效期
                }
                
                $success = '注册成功！已赠送3次免费分析机会，请登录使用。';
            }
        } catch (Exception $e) {
            $error = '注册失败：' . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - AI八字分析</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .card-custom {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50px;
            padding: 12px 30px;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
    </style>
</head>
<body class="gradient-bg">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="card card-custom">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <h2><i class="bi bi-person-plus"></i> 用户注册</h2>
                            <p class="text-muted">注册即可获得3次免费分析机会</p>
                        </div>

                        <?php if ($error): ?>
                        <div class="alert alert-danger" role="alert">
                            <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                        </div>
                        <?php endif; ?>

                        <?php if ($success): ?>
                        <div class="alert alert-success" role="alert">
                            <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($success); ?>
                            <div class="mt-2">
                                <a href="login.php" class="btn btn-success btn-sm">立即登录</a>
                            </div>
                        </div>
                        <?php else: ?>

                        <form method="post" class="needs-validation" novalidate>
                            <div class="mb-3">
                                <label for="phone" class="form-label">
                                    <i class="bi bi-phone"></i> 手机号 *
                                </label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>" 
                                       placeholder="请输入手机号" required>
                                <div class="invalid-feedback">请输入有效的手机号</div>
                            </div>

                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    <i class="bi bi-person"></i> 姓名
                                </label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>" 
                                       placeholder="请输入真实姓名（可选）">
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="bi bi-envelope"></i> 邮箱
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" 
                                       placeholder="请输入邮箱（可选）">
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="bi bi-lock"></i> 密码 *
                                </label>
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="请输入密码（至少6位）" required>
                                <div class="invalid-feedback">密码长度至少6位</div>
                            </div>

                            <div class="mb-4">
                                <label for="confirm_password" class="form-label">
                                    <i class="bi bi-lock-fill"></i> 确认密码 *
                                </label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                       placeholder="请再次输入密码" required>
                                <div class="invalid-feedback">两次输入的密码不一致</div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-person-plus"></i> 立即注册
                                </button>
                            </div>
                        </form>

                        <div class="text-center mt-4">
                            <p class="text-muted">
                                已有账户？<a href="login.php" class="text-decoration-none">立即登录</a>
                            </p>
                            <p class="text-muted">
                                <a href="index.php" class="text-decoration-none">
                                    <i class="bi bi-arrow-left"></i> 返回首页
                                </a>
                            </p>
                        </div>

                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 表单验证
        (function() {
            'use strict';
            window.addEventListener('load', function() {
                var forms = document.getElementsByClassName('needs-validation');
                var validation = Array.prototype.filter.call(forms, function(form) {
                    form.addEventListener('submit', function(event) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        }
                        form.classList.add('was-validated');
                    }, false);
                });
            }, false);
        })();

        // 密码确认验证
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.setCustomValidity('两次输入的密码不一致');
            } else {
                this.setCustomValidity('');
            }
        });

        // 手机号验证
        document.getElementById('phone').addEventListener('input', function() {
            const phone = this.value;
            const phoneRegex = /^1[3-9]\d{9}$/;
            
            if (!phoneRegex.test(phone)) {
                this.setCustomValidity('请输入有效的手机号');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
