<?php
// 在 result.php 开头添加这些调试代码
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// 创建调试日志函数
function debug_log($message, $data = null) {
    $logMessage = "[" . date('Y-m-d H:i:s') . "] " . $message;
    if ($data !== null) {
        $logMessage .= " - Data: " . json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    error_log($logMessage);
    
    // 同时写入临时文件以便查看
    file_put_contents('debug.log', $logMessage . "\n", FILE_APPEND | LOCK_EX);
}

debug_log("=== result.php start ===");
debug_log("Request method", $_SERVER['REQUEST_METHOD']);
debug_log("Request headers", getallheaders());

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    debug_log("Processing OPTIONS request");
    http_response_code(200);
    exit();
}

// 创建响应函数
function sendResponse($success, $data = null, $error = null, $statusCode = 200) {
    debug_log("Sending response", [
        'success' => $success, 
        'error' => $error, 
        'statusCode' => $statusCode,
        'has_data' => !is_null($data)
    ]);
    
    http_response_code($statusCode);
    $response = ['success' => $success];
    if ($error) $response['error'] = $error;
    if ($data) $response['data'] = $data;
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    debug_log("=== result.php end ===");
    exit();
}

try {
    debug_log("Starting main processing...");
    
    // 检查请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendResponse(false, null, '只允许POST请求', 405);
    }

    // 获取输入数据
    $rawInput = file_get_contents('php://input');
    debug_log("Raw input length", strlen($rawInput));
    debug_log("Raw input preview", substr($rawInput, 0, 200));
    
    if (empty($rawInput)) {
        sendResponse(false, null, '请求数据为空', 400);
    }

    $input = json_decode($rawInput, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        debug_log("JSON decode error", json_last_error_msg());
        sendResponse(false, null, 'JSON解析失败: ' . json_last_error_msg(), 400);
    }

    debug_log("Decoded input", $input);

    // 检查必需字段
    $required = ['name', 'gender', 'year', 'month', 'day', 'hour', 'minute', 'question'];
    $missing = [];
    
    foreach ($required as $field) {
        if (!isset($input[$field]) || $input[$field] === '' || $input[$field] === null) {
            $missing[] = $field;
        }
    }

    if (!empty($missing)) {
        debug_log("Missing fields", $missing);
        sendResponse(false, null, '缺少必需字段: ' . implode(', ', $missing), 400);
    }

    debug_log("Field validation passed");

    // 提取并验证参数
    $name = trim($input['name']);
    $gender = trim($input['gender']);
    $year = intval($input['year']);
    $month = intval($input['month']);
    $day = intval($input['day']);
    $hour = intval($input['hour']);
    $minute = intval($input['minute']);
    $question = trim($input['question']);
    $model = isset($input['model']) ? trim($input['model']) : 'DeepSeek-R1';

    debug_log("Extracted parameters", [
        'name' => $name,
        'gender' => $gender,
        'birth' => "{$year}-{$month}-{$day} {$hour}:{$minute}",
        'question_length' => strlen($question),
        'model' => $model
    ]);

    // 验证参数范围
    if (empty($name)) sendResponse(false, null, '姓名不能为空', 400);
    if (!in_array($gender, ['男', '女'])) sendResponse(false, null, '性别必须是男或女', 400);
    if ($year < 1900 || $year > 2100) sendResponse(false, null, '年份必须在1900-2100之间', 400);
    if ($month < 1 || $month > 12) sendResponse(false, null, '月份必须在1-12之间', 400);
    if ($day < 1 || $day > 31) sendResponse(false, null, '日期必须在1-31之间', 400);
    if ($hour < 0 || $hour > 23) sendResponse(false, null, '小时必须在0-23之间', 400);
    if ($minute < 0 || $minute > 59) sendResponse(false, null, '分钟必须在0-59之间', 400);
    if (empty($question)) sendResponse(false, null, '问题不能为空', 400);

    // 检查日期有效性
    if (!checkdate($month, $day, $year)) {
        sendResponse(false, null, '无效的日期', 400);
    }

    debug_log("Parameter validation passed");

    // 加载必需的文件
    if (!file_exists('config/database.php')) {
        debug_log("Missing database config file");
        sendResponse(false, null, '数据库配置文件不存在', 500);
    }
    
    if (!file_exists('includes/BaziCalculator.php')) {
        debug_log("Missing BaziCalculator file");
        sendResponse(false, null, 'BaziCalculator.php文件不存在', 500);
    }
    
    if (!file_exists('includes/AIAnalyzer.php')) {
        debug_log("Missing AIAnalyzer file");
        sendResponse(false, null, 'AIAnalyzer.php文件不存在', 500);
    }

    debug_log("Files exist, loading...");
    
    require_once 'config/database.php';
    require_once 'includes/BaziCalculator.php';
    require_once 'includes/AIAnalyzer.php';

    debug_log("Files loaded successfully");

    // 计算八字
    debug_log("Starting bazi calculation");
    $calculator = new BaziCalculator();
    $baziResult = $calculator->calculateBazi($year, $month, $day, $hour, $minute);

    if (isset($baziResult['error'])) {
        debug_log("Bazi calculation failed", $baziResult['message']);
        sendResponse(false, null, '八字计算失败: ' . $baziResult['message'], 500);
    }

    debug_log("Bazi calculation successful");

    // 构建分析提示词
    debug_log("Building analysis prompt");
    $prompt = buildAnalysisPrompt($name, $gender, $baziResult, $question);
    debug_log("Prompt built, length", strlen($prompt));

    // AI分析
    debug_log("Starting AI analysis with model: " . $model);
    $analyzer = new AIAnalyzer();
    $aiResult = $analyzer->analyze($prompt, $model);

    debug_log("AI analysis completed", [
        'has_error' => isset($aiResult['error']),
        'content_length' => isset($aiResult['content']) ? strlen($aiResult['content']) : 0
    ]);

    if (!$aiResult || !isset($aiResult['content'])) {
        debug_log("AI analysis failed - no content");
        sendResponse(false, null, 'AI分析失败', 500);
    }

    if (isset($aiResult['error'])) {
        debug_log("AI analysis has error", $aiResult['error']);
        // 有错误但仍有内容，继续处理
    }

    debug_log("Preparing final response");

    // 保存记录到数据库（可选）
    $recordId = null;
    try {
        if (isset($pdo)) {
            debug_log("Saving to database");
            $stmt = $pdo->prepare("
                INSERT INTO analysis_records 
                (session_id, name, gender, birth_year, birth_month, birth_day, birth_hour, birth_minute, 
                 bazi_result, question, ai_analysis, model_used, created_at, ip_address) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?)
            ");
            
            $sessionId = session_id() ?: uniqid('bazi_', true);
            
            $stmt->execute([
                $sessionId, $name, $gender, $year, $month, $day, $hour, $minute,
                json_encode($baziResult, JSON_UNESCAPED_UNICODE),
                $question, $aiResult['content'], $model,
                $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);
            
            $recordId = $pdo->lastInsertId();
            debug_log("Database save successful, record ID: " . $recordId);
        }
    } catch (Exception $e) {
        debug_log("Database save failed", $e->getMessage());
        // 数据库保存失败不影响主功能
    }

    // 返回成功结果
    debug_log("Preparing success response");
    sendResponse(true, [
        'name' => $name,
        'gender' => $gender,
        'birth_info' => [
            'year' => $year,
            'month' => $month,
            'day' => $day,
            'hour' => $hour,
            'minute' => $minute,
            'formatted' => sprintf('%04d年%02d月%02d日 %02d:%02d', $year, $month, $day, $hour, $minute)
        ],
        'bazi' => $baziResult,
        'question' => $question,
        'analysis' => $aiResult['content'],
        'model_used' => isset($aiResult['model']) ? $aiResult['model'] : $model,
        'record_id' => $recordId,
        'timestamp' => date('Y-m-d H:i:s'),
        'debug_info' => [
            'has_ai_error' => isset($aiResult['error']),
            'ai_error' => isset($aiResult['error']) ? $aiResult['error'] : null
        ]
    ]);

} catch (Exception $e) {
    debug_log("Exception caught", [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
    sendResponse(false, null, '系统错误: ' . $e->getMessage(), 500);
} catch (Throwable $e) {
    debug_log("Fatal error caught", [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
    sendResponse(false, null, '系统发生严重错误', 500);
}

// buildAnalysisPrompt 函数保持不变...
function buildAnalysisPrompt($name, $gender, $baziResult, $question) {
    $bazi = isset($baziResult['formatted']['ganzhi']) ? $baziResult['formatted']['ganzhi'] : '未知';
    $animal = isset($baziResult['lunar_info']['animal']) ? $baziResult['lunar_info']['animal'] : '未知';
    
    $wuxingText = '';
    if (isset($baziResult['wuxing']['count'])) {
        foreach ($baziResult['wuxing']['count'] as $element => $count) {
            $wuxingText .= "{$element}:{$count}个 ";
        }
    }
    
    $balanceScore = round(isset($baziResult['wuxing']['balance_score']) ? $baziResult['wuxing']['balance_score'] : 0, 1);
    $strongest = isset($baziResult['wuxing']['strongest']) ? $baziResult['wuxing']['strongest'] : '未知';
    $weakest = isset($baziResult['wuxing']['weakest']) ? $baziResult['wuxing']['weakest'] : '未知';
    
    return "请作为专业的八字命理师，为以下信息进行详细分析：

【基本信息】
- 姓名：{$name}
- 性别：{$gender}
- 生肖：{$animal}

【八字信息】
- 四柱八字：{$bazi}
- 五行分布：{$wuxingText}
- 五行平衡分数：{$balanceScore}分
- 最强五行：{$strongest}
- 最弱五行：{$weakest}

【用户询问】
{$question}

请针对用户的具体问题，结合八字信息进行专业分析，内容要详细、准确、实用，既要有专业性，又要通俗易懂。";
}
?>
