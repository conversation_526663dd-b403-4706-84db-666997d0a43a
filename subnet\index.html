<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IPv4和IPv6子网计算工具</title>
    <!-- Load Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Custom scrollbar styles */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb {
            background: #a0aec0; /* Lighter gray for thumb */
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #718096; /* Darker gray on hover */
        }

        /* Ensure all elements use Inter font */
        body {
            font-family: 'Inter', sans-serif;
        }

        /* Custom button styling for a more appealing look */
        .btn-primary {
            background-image: linear-gradient(to right, #6366f1 0%, #8b5cf6 100%);
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px 0 rgba(99, 102, 241, 0.75);
        }
        .btn-primary:hover {
            background-position: right center; /* Change the gradient direction on hover */
            box-shadow: 0 6px 20px 0 rgba(99, 102, 241, 0.9);
            transform: translateY(-2px); /* Slight lift effect */
        }
        .btn-purple {
            background-image: linear-gradient(to right, #a855f7 0%, #d946ef 100%);
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px 0 rgba(168, 85, 247, 0.75);
        }
        .btn-purple:hover {
            background-position: right center;
            box-shadow: 0 6px 20px 0 rgba(168, 85, 247, 0.9);
            transform: translateY(-2px);
        }
        .btn-teal {
            background-image: linear-gradient(to right, #14b8a6 0%, #0d9488 100%);
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px 0 rgba(20, 184, 166, 0.75);
        }
        .btn-teal:hover {
            background-position: right center;
            box-shadow: 0 6px 20px 0 rgba(20, 184, 166, 0.9);
            transform: translateY(-2px);
        }
        .btn-orange {
            background-image: linear-gradient(to right, #f97316 0%, #ea580c 100%);
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px 0 rgba(249, 115, 22, 0.75);
        }
        .btn-orange:hover {
            background-position: right center;
            box-shadow: 0 6px 20px 0 rgba(249, 115, 22, 0.9);
            transform: translateY(-2px);
        }

        /* Message Box specific styles for animation */
        .fade-in {
            animation: fadeIn 0.3s ease-out forwards;
        }
        .fade-out {
            animation: fadeOut 0.3s ease-out forwards;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: scale(0.9); }
            to { opacity: 1; transform: scale(1); }
        }
        @keyframes fadeOut {
            from { opacity: 1; transform: scale(1); }
            to { opacity: 0; transform: scale(0.9); }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-200 to-purple-300 min-h-screen flex items-center justify-center p-4">
    <div class="bg-white p-10 rounded-3xl shadow-2xl w-full max-w-5xl border border-gray-100 transform transition-all duration-300 hover:shadow-3xl">
        <h1 class="text-5xl font-extrabold text-center text-gray-800 mb-10 tracking-tight leading-tight">
            网络子网计算器
        </h1>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-10 mb-8">
            <!-- IPv4 Calculation Section -->
            <div class="bg-blue-50 p-8 rounded-2xl border border-blue-100 shadow-lg transform transition-all duration-300 hover:shadow-xl hover:scale-105">
                <h2 class="text-3xl font-bold text-blue-700 mb-6 text-center">IPv4 子网</h2>
                <div class="mb-5">
                    <label for="ipv4Address" class="block text-base font-semibold text-gray-700 mb-2">IPv4 地址:</label>
                    <input type="text" id="ipv4Address" value="***********" placeholder="例如: ***********"
                           class="w-full p-3 border border-blue-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 transition duration-200 text-gray-800 bg-white shadow-sm">
                </div>
                <div class="mb-7">
                    <label for="ipv4Cidr" class="block text-base font-semibold text-gray-700 mb-2">CIDR 前缀 (0-32):</label>
                    <input type="number" id="ipv4Cidr" value="24" min="0" max="32" placeholder="例如: 24"
                           class="w-full p-3 border border-blue-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 transition duration-200 text-gray-800 bg-white shadow-sm">
                </div>
                <button id="calculateIpv4"
                        class="w-full text-white py-3.5 rounded-lg font-bold btn-primary focus:outline-none focus:ring-4 focus:ring-blue-300 transition duration-300 uppercase tracking-wider">
                    计算 IPv4
                </button>

                <div id="ipv4Results" class="mt-8 p-6 bg-white rounded-xl border border-blue-200 shadow-inner hidden">
                    <h3 class="text-2xl font-semibold text-gray-800 mb-4 border-b pb-2 border-gray-200">计算结果:</h3>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-y-3 gap-x-6 text-gray-700 text-base">
                        <p><strong>地址类型:</strong> <span id="ipv4_type" class="font-medium text-blue-700"></span></p>
                        <p><strong>网络地址:</strong> <span id="ipv4_network" class="font-medium text-blue-700"></span></p>
                        <p><strong>子网掩码:</strong> <span id="ipv4_subnet_mask" class="font-medium text-blue-700"></span></p>
                        <p><strong>广播地址:</strong> <span id="ipv4_broadcast" class="font-medium text-blue-700"></span></p>
                        <p><strong>可用主机数:</strong> <span id="ipv4_hosts" class="font-medium text-blue-700"></span></p>
                        <p><strong>主机范围:</strong> <span id="ipv4_host_range" class="font-medium text-blue-700"></span></p>
                    </div>
                </div>
            </div>

            <!-- IPv6 Calculation Section -->
            <div class="bg-purple-50 p-8 rounded-2xl border border-purple-100 shadow-lg transform transition-all duration-300 hover:shadow-xl hover:scale-105">
                <h2 class="text-3xl font-bold text-purple-700 mb-6 text-center">IPv6 子网</h2>
                <div class="mb-5">
                    <label for="ipv6Address" class="block text-base font-semibold text-gray-700 mb-2">IPv6 地址:</label>
                    <input type="text" id="ipv6Address" value="2001:0db8:85a3::" placeholder="例如: 2001:db8::"
                           class="w-full p-3 border border-purple-300 rounded-lg focus:ring-purple-500 focus:border-purple-500 transition duration-200 text-gray-800 bg-white shadow-sm">
                </div>
                <div class="mb-7">
                    <label for="ipv6Cidr" class="block text-base font-semibold text-gray-700 mb-2">CIDR 前缀 (0-128):</label>
                    <input type="number" id="ipv6Cidr" value="64" min="0" max="128" placeholder="例如: 64"
                           class="w-full p-3 border border-purple-300 rounded-lg focus:ring-purple-500 focus:border-purple-500 transition duration-200 text-gray-800 bg-white shadow-sm">
                </div>
                <button id="calculateIpv6"
                        class="w-full text-white py-3.5 rounded-lg font-bold btn-purple focus:outline-none focus:ring-4 focus:ring-purple-300 transition duration-300 uppercase tracking-wider">
                    计算 IPv6
                </button>

                <div id="ipv6Results" class="mt-8 p-6 bg-white rounded-xl border border-purple-200 shadow-inner hidden">
                    <h3 class="text-2xl font-semibold text-gray-800 mb-4 border-b pb-2 border-gray-200">计算结果:</h3>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-y-3 gap-x-6 text-gray-700 text-base">
                        <p><strong>地址类型:</strong> <span id="ipv6_type" class="font-medium text-purple-700"></span></p>
                        <p><strong>网络前缀:</strong> <span id="ipv6_network_prefix" class="font-medium text-purple-700 break-all"></span></p>
                        <p><strong>主机地址范围:</strong> <span id="ipv6_host_range" class="font-medium text-purple-700 break-all"></span></p>
                        <p><strong>总地址数:</strong> <span id="ipv6_total_addresses" class="font-medium text-purple-700 break-all"></span></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- VLSM/CIDR Tools Section -->
        <div class="bg-gray-50 p-10 rounded-3xl border border-gray-100 shadow-2xl mt-10">
            <h2 class="text-4xl font-extrabold text-center text-gray-800 mb-8 tracking-tight">
                VLSM/CIDR 工具
            </h2>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-10">
                <!-- Subnet Splitting Section -->
                <div class="bg-teal-50 p-8 rounded-2xl border border-teal-100 shadow-lg transform transition-all duration-300 hover:shadow-xl hover:scale-105">
                    <h3 class="text-3xl font-bold text-teal-700 mb-6 text-center">子网拆分 (VLSM)</h3>
                    <div class="mb-5">
                        <label for="splitIpAddress" class="block text-base font-semibold text-gray-700 mb-2">原始网络 (IPv4, CIDR):</label>
                        <input type="text" id="splitIpAddress" value="***********/24" placeholder="例如: ***********/24"
                               class="w-full p-3 border border-teal-300 rounded-lg focus:ring-teal-500 focus:border-teal-500 transition duration-200 text-gray-800 bg-white shadow-sm">
                    </div>
                    <div class="mb-5">
                        <label class="block text-base font-semibold text-gray-700 mb-2">拆分方式:</label>
                        <div class="flex space-x-4 mb-3">
                            <label class="inline-flex items-center">
                                <input type="radio" name="splitType" value="numSubnets" checked
                                       class="form-radio text-teal-600 focus:ring-teal-500">
                                <span class="ml-2 text-gray-700">按子网数量</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" name="splitType" value="numHosts"
                                       class="form-radio text-teal-600 focus:ring-teal-500">
                                <span class="ml-2 text-gray-700">按每个子网主机数</span>
                            </label>
                        </div>
                        <input type="number" id="splitValue" value="2" min="1" placeholder="例如: 2 (子网数) 或 60 (主机数)"
                               class="w-full p-3 border border-teal-300 rounded-lg focus:ring-teal-500 focus:border-teal-500 transition duration-200 text-gray-800 bg-white shadow-sm">
                    </div>
                    <button id="calculateSplit"
                            class="w-full text-white py-3.5 rounded-lg font-bold btn-teal focus:outline-none focus:ring-4 focus:ring-teal-300 transition duration-300 uppercase tracking-wider">
                        拆分网络
                    </button>

                    <div id="splitResults" class="mt-8 p-6 bg-white rounded-xl border border-teal-200 shadow-inner hidden">
                        <h3 class="text-2xl font-semibold text-gray-800 mb-4 border-b pb-2 border-gray-200">拆分结果:</h3>
                        <div id="splitSubnetsList" class="max-h-60 overflow-y-auto text-gray-700 text-base">
                            <!-- Split subnets will be listed here -->
                        </div>
                    </div>
                </div>

                <!-- Subnet Merging Section -->
                <div class="bg-orange-50 p-8 rounded-2xl border border-orange-100 shadow-lg transform transition-all duration-300 hover:shadow-xl hover:scale-105">
                    <h3 class="text-3xl font-bold text-orange-700 mb-6 text-center">子网合并 (CIDR 汇总)</h3>
                    <div class="mb-5">
                        <label for="mergeSubnetsInput" class="block text-base font-semibold text-gray-700 mb-2">输入子网列表 (IPv4, 每行一个，CIDR 格式):</label>
                        <textarea id="mergeSubnetsInput" rows="6" placeholder="例如:&#10;***********/24&#10;***********/24"
                                  class="w-full p-3 border border-orange-300 rounded-lg focus:ring-orange-500 focus:border-orange-500 transition duration-200 text-gray-800 bg-white shadow-sm"></textarea>
                    </div>
                    <button id="calculateMerge"
                            class="w-full text-white py-3.5 rounded-lg font-bold btn-orange focus:outline-none focus:ring-4 focus:ring-orange-300 transition duration-300 uppercase tracking-wider">
                        合并网络
                    </button>

                    <div id="mergeResults" class="mt-8 p-6 bg-white rounded-xl border border-orange-200 shadow-inner hidden">
                        <h3 class="text-2xl font-semibold text-gray-800 mb-4 border-b pb-2 border-gray-200">合并结果:</h3>
                        <div id="mergedSubnetsList" class="max-h-60 overflow-y-auto text-gray-700 text-base">
                            <!-- Merged subnets will be listed here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Message Box -->
        <div id="messageBox" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center p-4 z-50">
            <div class="bg-white p-8 rounded-lg shadow-2xl w-full max-w-sm text-center transform scale-0 opacity-0 transition-all duration-300">
                <p id="messageText" class="text-xl font-medium text-gray-800 mb-6"></p>
                <button id="closeMessageBox" class="bg-blue-600 text-white py-2.5 px-6 rounded-md hover:bg-blue-700 transition duration-200 focus:outline-none focus:ring-4 focus:ring-blue-300">
                    确定
                </button>
            </div>
        </div>

    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Get DOM elements
            const ipv4AddressInput = document.getElementById('ipv4Address');
            const ipv4CidrInput = document.getElementById('ipv4Cidr');
            const calculateIpv4Button = document.getElementById('calculateIpv4');
            const ipv4ResultsDiv = document.getElementById('ipv4Results');
            const ipv4NetworkSpan = document.getElementById('ipv4_network');
            const ipv4SubnetMaskSpan = document.getElementById('ipv4_subnet_mask');
            const ipv4BroadcastSpan = document.getElementById('ipv4_broadcast');
            const ipv4HostsSpan = document.getElementById('ipv4_hosts');
            const ipv4HostRangeSpan = document.getElementById('ipv4_host_range');
            const ipv4TypeSpan = document.getElementById('ipv4_type');

            const ipv6AddressInput = document.getElementById('ipv6Address');
            const ipv6CidrInput = document.getElementById('ipv6Cidr');
            const calculateIpv6Button = document.getElementById('calculateIpv6');
            const ipv6ResultsDiv = document.getElementById('ipv6Results');
            const ipv6NetworkPrefixSpan = document.getElementById('ipv6_network_prefix');
            const ipv6HostRangeSpan = document.getElementById('ipv6_host_range');
            const ipv6TotalAddressesSpan = document.getElementById('ipv6_total_addresses');
            const ipv6TypeSpan = document.getElementById('ipv6_type');

            const messageBox = document.getElementById('messageBox');
            const messageText = document.getElementById('messageText');
            const closeMessageBoxButton = document.getElementById('closeMessageBox');
            const messageBoxContent = messageBox.querySelector('div');

            // VLSM/CIDR Tool elements
            const splitIpAddressInput = document.getElementById('splitIpAddress');
            const splitTypeRadios = document.querySelectorAll('input[name="splitType"]');
            const splitValueInput = document.getElementById('splitValue');
            const calculateSplitButton = document.getElementById('calculateSplit');
            const splitResultsDiv = document.getElementById('splitResults');
            const splitSubnetsList = document.getElementById('splitSubnetsList');

            const mergeSubnetsInput = document.getElementById('mergeSubnetsInput');
            const calculateMergeButton = document.getElementById('calculateMerge');
            const mergeResultsDiv = document.getElementById('mergeResults');
            const mergedSubnetsList = document.getElementById('mergedSubnetsList');


            /**
             * Show the message box with a given message.
             * Adds fade-in animation.
             * @param {string} message - The message to display.
             */
            function showMessageBox(message) {
                messageText.textContent = message;
                messageBox.classList.remove('hidden');
                messageBox.classList.add('flex');
                // Trigger reflow to ensure animation plays
                void messageBoxContent.offsetWidth;
                messageBoxContent.classList.remove('scale-0', 'opacity-0', 'fade-out');
                messageBoxContent.classList.add('fade-in');
            }

            /**
             * Close the message box.
             * Adds fade-out animation.
             */
            closeMessageBoxButton.addEventListener('click', () => {
                messageBoxContent.classList.remove('fade-in');
                messageBoxContent.classList.add('fade-out');
                messageBoxContent.addEventListener('animationend', function handler() {
                    messageBox.classList.add('hidden');
                    messageBox.classList.remove('flex');
                    messageBoxContent.classList.remove('fade-out'); // Clean up animation class
                    messageBoxContent.classList.add('scale-0', 'opacity-0'); // Reset for next fade-in
                    messageBoxContent.removeEventListener('animationend', handler);
                });
            });

            // =========================================================================
            // Common IP Utility Functions
            // =========================================================================

            /**
             * Validate IPv4 address format.
             * @param {string} ipAddress - The IPv4 address string to validate.
             * @returns {boolean} - True if format is valid, false otherwise.
             */
            function isValidIpv4(ipAddress) {
                return /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/.test(ipAddress);
            }

            /**
             * Convert an IPv4 address string to a 32-bit binary string.
             * @param {string} ipAddress - The IPv4 address string.
             * @returns {string} - 32-bit binary string.
             */
            function ipv4ToBinary(ipAddress) {
                return ipAddress.split('.').map(octet => {
                    return parseInt(octet).toString(2).padStart(8, '0');
                }).join('');
            }

            /**
             * Convert a 32-bit binary string to an IPv4 address string.
             * @param {string} binaryIp - The 32-bit binary string.
             * @returns {string} - IPv4 address string.
             */
            function binaryToIpv4(binaryIp) {
                const octets = [];
                for (let i = 0; i < 32; i += 8) {
                    octets.push(parseInt(binaryIp.substring(i, i + 8), 2));
                }
                return octets.join('.');
            }

            /**
             * Calculate the subnet mask based on CIDR prefix.
             * @param {number} cidr - CIDR prefix (0-32).
             * @returns {string} - Subnet mask string.
             */
            function getSubnetMask(cidr) {
                let maskBinary = '1'.repeat(cidr) + '0'.repeat(32 - cidr);
                return binaryToIpv4(maskBinary);
            }

            /**
             * Determine the type of IPv4 address (Class A, B, C, D, E, private, loopback).
             * @param {string} ipAddress - The IPv4 address.
             * @returns {string} - The address type.
             */
            function getIpv4AddressType(ipAddress) {
                const octets = ipAddress.split('.').map(Number);
                const firstOctet = octets[0];

                if (firstOctet === 127) return '环回地址 (Loopback)'; // *********/8
                if (firstOctet >= 1 && firstOctet <= 126) {
                    // Class A Private: 10.0.0.0 - **************
                    if (firstOctet === 10) return 'A 类 (私有)';
                    return 'A 类 (公共)';
                } else if (firstOctet >= 128 && firstOctet <= 191) {
                    // Class B Private: ********** - **************
                    if (firstOctet === 172 && octets[1] >= 16 && octets[1] <= 31) return 'B 类 (私有)';
                    return 'B 类 (公共)';
                } else if (firstOctet >= 192 && firstOctet <= 223) {
                    // Class C Private: *********** - ***************
                    if (firstOctet === 192 && octets[1] === 168) return 'C 类 (私有)';
                    return 'C 类 (公共)';
                } else if (firstOctet >= 224 && firstOctet <= 239) {
                    return 'D 类 (组播)';
                } else if (firstOctet >= 240 && firstOctet <= 255) {
                    return 'E 类 (实验)';
                }
                return '未知类型';
            }

            /**
             * Normalize IPv6 address (expand abbreviations, lowercase).
             * @param {string} ipv6Address - Unnormalized IPv6 address string.
             * @returns {string|null} - Normalized IPv6 address or null if invalid.
             */
            function normalizeIpv6(ipv6Address) {
                try {
                    const url = `http://[${ipv6Address}]`;
                    const parsedUrl = new URL(url);
                    const normalized = parsedUrl.hostname.slice(1, -1);
                    if (isValidIpv6(normalized)) {
                        return normalized.toLowerCase();
                    }
                    return null;
                } catch (e) {
                    return null;
                }
            }

            /**
             * Validate IPv6 address format (basic validation).
             * @param {string} ipAddress - The IPv6 address string to validate.
             * @returns {boolean} - True if format is valid, false otherwise.
             */
            function isValidIpv6(ipAddress) {
                const ipv6Regex = /(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3,3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3,3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))$/;
                return ipv6Regex.test(ipAddress);
            }

            /**
             * Convert a normalized IPv6 address string to a 128-bit binary string.
             * @param {string} normalizedIpv6 - Normalized IPv6 address string.
             * @returns {string} - 128-bit binary string.
             */
            function ipv6ToBinary(normalizedIpv6) {
                return normalizedIpv6.split(':').map(hextet => {
                    return parseInt(hextet, 16).toString(2).padStart(16, '0');
                }).join('');
            }

            /**
             * Convert a 128-bit binary string to a normalized IPv6 address string.
             * @param {string} binaryIpv6 - 128-bit binary string.
             * @returns {string} - Normalized IPv6 address string.
             */
            function binaryToIpv6(binaryIpv6) {
                const hextets = [];
                for (let i = 0; i < 128; i += 16) {
                    hextets.push(parseInt(binaryIpv6.substring(i, i + 16), 2).toString(16));
                }
                return hextets.join(':');
            }

            /**
             * Determine the type of IPv6 address.
             * @param {string} ipv6Address - The IPv6 address (can be compressed).
             * @returns {string} - The address type.
             */
            function getIpv6AddressType(ipv6Address) {
                const normalized = normalizeIpv6(ipv6Address);
                if (!normalized) return '无效类型';

                if (normalized === '::1') return '环回地址 (Loopback)';
                if (normalized.startsWith('fe80:')) return '链路本地地址 (Link-Local)';
                if (normalized.startsWith('fc00:') || normalized.startsWith('fd00:')) return '唯一本地地址 (Unique Local Address)';
                if (normalized.startsWith('ff00:')) return '组播地址 (Multicast)';
                if (normalized.startsWith('2000:') || normalized.startsWith('3000:')) return '全球单播地址 (Global Unicast)';
                return '其他/未知类型';
            }


            // =========================================================================
            // IPv4 Calculation Logic (Existing, copied for clarity)
            // =========================================================================
            calculateIpv4Button.addEventListener('click', () => {
                const ipAddress = ipv4AddressInput.value.trim();
                const cidr = parseInt(ipv4CidrInput.value);

                if (!isValidIpv4(ipAddress)) {
                    showMessageBox('无效的 IPv4 地址格式。请使用例如 *********** 这样的格式。');
                    ipv4ResultsDiv.classList.add('hidden');
                    return;
                }
                if (isNaN(cidr) || cidr < 0 || cidr > 32) {
                    showMessageBox('CIDR 前缀必须是 0 到 32 之间的数字。');
                    ipv4ResultsDiv.classList.add('hidden');
                    return;
                }

                const ipBinary = ipv4ToBinary(ipAddress);
                const networkBinary = ipBinary.substring(0, cidr) + '0'.repeat(32 - cidr);
                const networkAddress = binaryToIpv4(networkBinary);
                const broadcastBinary = ipBinary.substring(0, cidr) + '1'.repeat(32 - cidr);
                const broadcastAddress = binaryToIpv4(broadcastBinary);
                const subnetMask = getSubnetMask(cidr);

                let numberOfHosts;
                let hostRange = 'N/A';

                if (32 - cidr === 0) {
                    numberOfHosts = 1;
                    hostRange = ipAddress;
                } else if (32 - cidr === 1) {
                    numberOfHosts = 0;
                    hostRange = '无可用主机 (点对点)';
                } else {
                    numberOfHosts = Math.pow(2, (32 - cidr)) - 2;
                    const firstHostBinary = networkBinary.substring(0, 31) + '1';
                    const firstHost = binaryToIpv4(firstHostBinary);
                    const lastHostBinary = broadcastBinary.substring(0, 31) + '0';
                    const lastHost = binaryToIpv4(lastHostBinary);
                    hostRange = `${firstHost} - ${lastHost}`;
                }

                const addressType = getIpv4AddressType(ipAddress);

                ipv4NetworkSpan.textContent = networkAddress;
                ipv4SubnetMaskSpan.textContent = subnetMask;
                ipv4BroadcastSpan.textContent = broadcastAddress;
                ipv4HostsSpan.textContent = numberOfHosts.toLocaleString();
                ipv4HostRangeSpan.textContent = hostRange;
                ipv4TypeSpan.textContent = addressType;

                ipv4ResultsDiv.classList.remove('hidden');
            });

            // =========================================================================
            // IPv6 Calculation Logic (Existing, copied for clarity)
            // =========================================================================
            calculateIpv6Button.addEventListener('click', () => {
                const ipAddress = ipv6AddressInput.value.trim();
                const cidr = parseInt(ipv6CidrInput.value);

                if (isNaN(cidr) || cidr < 0 || cidr > 128) {
                    showMessageBox('CIDR 前缀必须是 0 到 128 之间的数字。');
                    ipv6ResultsDiv.classList.add('hidden');
                    return;
                }

                const normalizedIpv6 = normalizeIpv6(ipAddress);
                if (!normalizedIpv6) {
                    showMessageBox('无效的 IPv6 地址格式。请使用例如 2001:db8:: 或 2001:0db8:85a3:0000:0000:8a2e:0370:7334 这样的格式。');
                    ipv6ResultsDiv.classList.add('hidden');
                    return;
                }

                const ipBinary = ipv6ToBinary(normalizedIpv6);
                const networkBinary = ipBinary.substring(0, cidr) + '0'.repeat(128 - cidr);
                const networkPrefix = binaryToIpv6(networkBinary);
                const firstHostBinary = networkBinary;
                const lastHostBinary = ipBinary.substring(0, cidr) + '1'.repeat(128 - cidr);
                const firstHost = binaryToIpv6(firstHostBinary);
                const lastHost = binaryToIpv6(lastHostBinary);

                const hostRange = `${firstHost} - ${lastHost}`;
                const totalAddresses = 2n ** BigInt(128 - cidr);
                const addressType = getIpv6AddressType(ipAddress);

                ipv6NetworkPrefixSpan.textContent = `${networkPrefix}/${cidr}`;
                ipv6HostRangeSpan.textContent = hostRange;
                ipv6TotalAddressesSpan.textContent = totalAddresses.toString();
                ipv6TypeSpan.textContent = addressType;

                ipv6ResultsDiv.classList.remove('hidden');
            });


            // =========================================================================
            // VLSM/CIDR Split Logic
            // =========================================================================

            calculateSplitButton.addEventListener('click', () => {
                splitSubnetsList.innerHTML = '';
                splitResultsDiv.classList.add('hidden');

                const fullCidrInput = splitIpAddressInput.value.trim();
                const parts = fullCidrInput.split('/');
                if (parts.length !== 2 || !isValidIpv4(parts[0]) || isNaN(parseInt(parts[1])) || parseInt(parts[1]) < 0 || parseInt(parts[1]) > 32) {
                    showMessageBox('原始网络格式无效。请使用例如 ***********/24 这样的格式。');
                    return;
                }
                const originalIp = parts[0];
                const originalCidr = parseInt(parts[1]);

                const splitValue = parseInt(splitValueInput.value);
                if (isNaN(splitValue) || splitValue <= 0) {
                    showMessageBox('子网数量或主机数量必须是正整数。');
                    return;
                }

                const splitType = document.querySelector('input[name="splitType"]:checked').value;
                let newCidr;
                let numDesiredSubnets = 0;
                let requiredHostsPerSubnet = 0;

                if (splitType === 'numSubnets') {
                    numDesiredSubnets = splitValue;
                    // Calculate the new CIDR based on the number of desired subnets
                    // Find smallest N such that 2^N >= numDesiredSubnets
                    const bitsNeeded = Math.ceil(Math.log2(numDesiredSubnets));
                    newCidr = originalCidr + bitsNeeded;

                    if (newCidr > 32) {
                        showMessageBox(`无法将 /${originalCidr} 拆分成 ${numDesiredSubnets} 个子网，需要的前缀长度超过 32。`);
                        return;
                    }
                } else { // numHosts
                    requiredHostsPerSubnet = splitValue;
                    // Calculate the new CIDR based on the number of hosts per subnet
                    // Find smallest N such that 2^N - 2 (usable hosts) >= requiredHostsPerSubnet
                    // If N=1 (for /31), usable hosts = 0. If N=0 (for /32), usable hosts = 1.
                    let hostBitsNeeded = 0;
                    if (requiredHostsPerSubnet === 1) { // Requires /32
                        hostBitsNeeded = 0;
                    } else if (requiredHostsPerSubnet > 0) {
                        hostBitsNeeded = Math.ceil(Math.log2(requiredHostsPerSubnet + 2)); // +2 for network and broadcast
                        if (hostBitsNeeded === 1 && requiredHostsPerSubnet > 0) { // If it calculates to /31, but hosts are > 0, need at least 2 bits for 1 usable host (if we stretch the definition for /30). Let's be strict.
                            hostBitsNeeded = 2; // Minimum 2 host bits for usable hosts > 0
                        }
                    }

                    newCidr = 32 - hostBitsNeeded;

                    if (newCidr < originalCidr) {
                        showMessageBox(`无法从 /${originalCidr} 拆分出每个子网至少 ${requiredHostsPerSubnet} 个主机的网络。`);
                        return;
                    }
                }

                const originalIpBinary = ipv4ToBinary(originalIp);
                const networkPart = originalIpBinary.substring(0, originalCidr);
                const numSubnetsToGenerate = Math.pow(2, (newCidr - originalCidr));

                const subnets = [];
                for (let i = 0; i < numSubnetsToGenerate; i++) {
                    const subnetBinarySuffix = i.toString(2).padStart(newCidr - originalCidr, '0');
                    const newNetworkBinary = networkPart + subnetBinarySuffix + '0'.repeat(32 - newCidr);
                    const newNetworkAddress = binaryToIpv4(newNetworkBinary);
                    subnets.push(`${newNetworkAddress}/${newCidr}`);
                }

                if (subnets.length > 0) {
                    subnets.forEach(subnet => {
                        const li = document.createElement('p');
                        li.textContent = subnet;
                        splitSubnetsList.appendChild(li);
                    });
                    splitResultsDiv.classList.remove('hidden');
                } else {
                    showMessageBox('未能生成子网。请检查输入。');
                }
            });


            // =========================================================================
            // CIDR Merge Logic
            // =========================================================================

            /**
             * Converts an IPv4 address string to a 32-bit unsigned integer.
             * @param {string} ipAddress - The IPv4 address string.
             * @returns {number} - The 32-bit unsigned integer representation.
             */
            function ipv4ToLong(ipAddress) {
                const parts = ipAddress.split('.').map(Number);
                return (parts[0] << 24 | parts[1] << 16 | parts[2] << 8 | parts[3]) >>> 0; // >>> 0 for unsigned
            }

            /**
             * Converts a 32-bit unsigned integer to an IPv4 address string.
             * @param {number} longIp - The 32-bit unsigned integer.
             * @returns {string} - The IPv4 address string.
             */
            function longToIpv4(longIp) {
                return `${(longIp >>> 24) & 0xFF}.${(longIp >>> 16) & 0xFF}.${(longIp >>> 8) & 0xFF}.${longIp & 0xFF}`;
            }

            calculateMergeButton.addEventListener('click', () => {
                mergedSubnetsList.innerHTML = '';
                mergeResultsDiv.classList.add('hidden');

                const inputRaw = mergeSubnetsInput.value.trim();
                const lines = inputRaw.split('\n').map(line => line.trim()).filter(line => line !== '');

                if (lines.length < 1) {
                    showMessageBox('请输入至少一个子网进行合并。');
                    return;
                }

                // Parse and validate input subnets
                const subnets = [];
                for (const line of lines) {
                    const parts = line.split('/');
                    if (parts.length !== 2 || !isValidIpv4(parts[0]) || isNaN(parseInt(parts[1])) || parseInt(parts[1]) < 0 || parseInt(parts[1]) > 32) {
                        showMessageBox(`无效的子网格式: ${line}。请使用例如 ***********/24 这样的格式。`);
                        return;
                    }
                    const ipLong = ipv4ToLong(parts[0]);
                    const cidr = parseInt(parts[1]);
                    // Calculate network address (important for merging)
                    const networkAddressLong = ipLong & (0xFFFFFFFF << (32 - cidr));
                    subnets.push({ ipLong: networkAddressLong, cidr: cidr, original: line });
                }

                // Sort subnets by IP address
                subnets.sort((a, b) => a.ipLong - b.ipLong);

                const merged = [];
                let i = 0;
                while (i < subnets.length) {
                    let current = subnets[i];
                    let mergedThisPass = false;

                    for (let j = i + 1; j < subnets.length; j++) {
                        let next = subnets[j];

                        // Conditions for merging two subnets:
                        // 1. Same CIDR prefix
                        // 2. Next network immediately follows the current one
                        // 3. Both can be represented by one larger CIDR
                        if (current.cidr === next.cidr) {
                            const newCidr = current.cidr - 1; // Potential new CIDR (one bit smaller)
                            if (newCidr >= 0) { // Ensure CIDR doesn't go below 0
                                const blockCount = Math.pow(2, (32 - current.cidr)); // Number of IPs in current block
                                // Check if next IP is exactly current IP + block size, AND both align to the new CIDR
                                if (next.ipLong === (current.ipLong + blockCount) &&
                                    (current.ipLong & (0xFFFFFFFF << (32 - newCidr))) === current.ipLong) {
                                    current = {
                                        ipLong: current.ipLong,
                                        cidr: newCidr,
                                        original: `${longToIpv4(current.ipLong)}/${newCidr}` // Update original for display
                                    };
                                    // Remove the merged 'next' subnet from consideration for this pass
                                    subnets.splice(j, 1);
                                    j--; // Adjust index because an element was removed
                                    mergedThisPass = true;
                                }
                            }
                        }
                    }

                    if (mergedThisPass) {
                        // If merged, try to merge this new 'current' with others again in the next iteration
                        // (i.e., don't increment 'i', re-evaluate current)
                    } else {
                        merged.push(`${longToIpv4(current.ipLong)}/${current.cidr}`);
                        i++; // No merge happened for this 'current', move to the next original subnet
                    }
                }

                if (merged.length > 0) {
                    merged.forEach(subnet => {
                        const li = document.createElement('p');
                        li.textContent = subnet;
                        mergedSubnetsList.appendChild(li);
                    });
                    mergeResultsDiv.classList.remove('hidden');
                } else {
                    showMessageBox('未能合并子网。请检查输入是否有效且可合并。');
                }
            });

        });
    </script>
    <script type="text/javascript">
    (function(c,l,a,r,i,t,y){
        c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
        t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
        y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
    })(window, document, "clarity", "script", "jtxjtkxsi8");
</script>
</body>
</html>
